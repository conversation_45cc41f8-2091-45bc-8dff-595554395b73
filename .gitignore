# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
uploads/
processed/
*.db
*.sqlite
*.sqlite3

# Logs
*.log

# Temporary files
*.tmp
*.temp

# Docker
.dockerignore

# Anaconda
anaconda_projects/

# Jupyter notebooks
*.ipynb_checkpoints/

# Environment files
.env
.env.local 
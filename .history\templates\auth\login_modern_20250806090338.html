<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - {{ SYSTEM_NAME }}</title>
    
    <!-- CSS样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 1rem;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            padding: 3rem 2.5rem;
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }
        
        .system-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
        }
        
        .system-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .login-header h1 {
            color: var(--gray-900);
            font-weight: 700;
            font-size: 1.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }
        
        .login-header p {
            color: var(--gray-600);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 16px;
            padding: 1.25rem 1rem;
            font-size: 1rem;
            transition: var(--transition);
            background: white;
            height: auto;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            outline: none;
        }
        
        .form-control::placeholder {
            color: var(--gray-400);
        }
        
        .form-floating > label {
            color: var(--gray-600);
            font-weight: 500;
            padding: 1.25rem 1rem;
        }
        
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color);
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 16px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(99, 102, 241, 0.4);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
            cursor: not-allowed;
        }
        
        .form-check {
            margin: 1.5rem 0;
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .form-check-label {
            color: var(--gray-700);
            font-weight: 500;
        }
        
        .forgot-password {
            text-align: center;
            margin: 1rem 0;
        }
        
        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .forgot-password a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        .register-link {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--gray-200);
        }
        
        .register-link p {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }
        
        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .register-link a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 16px;
            border: none;
            margin-bottom: 1.5rem;
            padding: 1rem 1.25rem;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: #991b1b;
            border-left: 4px solid #ef4444;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #065f46;
            border-left: 4px solid #10b981;
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .login-header h1 {
                font-size: 1.5rem;
            }
            
            .system-logo {
                width: 64px;
                height: 64px;
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰 -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="bi bi-file-earmark-text" style="font-size: 3rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-cpu" style="font-size: 2.5rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-graph-up" style="font-size: 3.5rem;"></i>
        </div>
    </div>
    
    <div class="login-container">
        <div class="login-header">
            <div class="system-logo">
                <img src="{{ url_for('static', filename='img/80_80.png') }}" alt="系统Logo">
            </div>
            <h1>{{ SYSTEM_NAME }}</h1>
            <p>智能文档分析系统</p>
        </div>
        
        <!-- 消息提示 -->
        <div id="messageContainer"></div>
        
        <form id="loginForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="用户名" required>
                <label for="username">
                    <i class="bi bi-person me-2"></i>用户名
                </label>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="密码" required>
                <label for="password">
                    <i class="bi bi-lock me-2"></i>密码
                </label>
            </div>
            
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                <label class="form-check-label" for="rememberMe">
                    记住我
                </label>
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <i class="bi bi-box-arrow-in-right me-2"></i>
                登录
            </button>
        </form>

        <!-- 测试按钮 -->
        <button type="button" onclick="testLogin()" style="margin-top: 10px; background: #28a745; color: white; border: none; padding: 10px; border-radius: 4px;">
            测试登录
        </button>
        
        <div class="forgot-password">
            <a href="#" onclick="showMessage('请联系管理员重置密码', 'info')">忘记密码？</a>
        </div>
        
        <div class="register-link">
            <p>还没有账户？</p>
            <a href="{{ url_for('auth.register') }}">立即注册</a>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
    
    <script>
        // 显示消息
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const iconMap = {
                'success': 'bi-check-circle',
                'error': 'bi-exclamation-triangle',
                'warning': 'bi-exclamation-triangle',
                'info': 'bi-info-circle'
            };
            
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const icon = iconMap[type] || 'bi-info-circle';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="bi ${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            container.innerHTML = alertHtml;
            
            // 5秒后自动消失
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        // 测试登录函数
        function testLogin() {
            console.log('测试登录函数被调用');
            showMessage('开始测试登录...', 'info');

            const data = {
                username: 'admin',
                password: 'admin123',
                remember: false
            };

            console.log('发送测试登录请求:', data);

            axios.post('/auth/login', data)
                .then(response => {
                    console.log('测试登录响应:', response);
                    if (response.data.success) {
                        showMessage('测试登录成功！用户: ' + response.data.user.username, 'success');
                    } else {
                        showMessage('测试登录失败: ' + response.data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('测试登录失败:', error);
                    showMessage('测试登录网络错误: ' + error.message, 'error');
                });
        }

        // 全局暴露测试函数
        window.testLogin = testLogin;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');

            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');

            console.log('表单元素:', loginForm);
            console.log('登录按钮:', loginBtn);

            if (!loginForm) {
                console.error('未找到登录表单');
                return;
            }

            if (!loginBtn) {
                console.error('未找到登录按钮');
                return;
            }

            // 页面加载动画
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            // 表单提交
            console.log('绑定表单提交事件');
            loginForm.addEventListener('submit', function(e) {
                console.log('表单提交事件被触发');
                e.preventDefault();

                const formData = new FormData(this);
                const data = {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    remember: formData.get('remember_me') === 'on'
                };

                console.log('准备发送登录请求:', data);

                const originalText = loginBtn.innerHTML;
                loginBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>登录中...';
                loginBtn.disabled = true;

                // 发送登录请求
                console.log('发送登录请求到:', '/auth/login');
                axios.post('/auth/login', data)
                    .then(response => {
                        console.log('登录响应:', response);
                        if (response.data.success) {
                            showMessage('登录成功！正在跳转...', 'success');
                            setTimeout(() => {
                                window.location.href = response.data.redirect || '/';
                            }, 1000);
                        } else {
                            showMessage(response.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('登录失败:', error);
                        console.error('错误详情:', error.response);
                        if (error.response && error.response.data && error.response.data.message) {
                            showMessage(error.response.data.message, 'error');
                        } else {
                            showMessage('登录失败，请稍后重试', 'error');
                        }
                    })
                    .finally(() => {
                        loginBtn.innerHTML = originalText;
                        loginBtn.disabled = false;
                    });
            });
        });
    </script>
</body>
</html>

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [], "source": ["from util_img_process import *"]}, {"cell_type": "code", "execution_count": 33, "id": "f27c8632", "metadata": {}, "outputs": [], "source": ["img_prompt = \"\"\"\n", "请从图片中提取以下完整字段信息，严格按照JSON格式输出：\n", "1. 资金账户信息：\n", "   - 资金账号\n", "   - 交易编码对应名称\n", "   - 交易密码\n", "   - 统一交易编码\n", "   - 交易编码(8位数字)\n", "     - 上期所(8位数字)\n", "     - 大商所(8位数字)\n", "     - 郑商所(8位数字)\n", "     - 中金所（投机）(8位数字)\n", "     - 中金所（套保）(8位数字)\n", "     - 上能所(8位数字)\n", "     - 广期所(8位数字)\n", "   \n", "2. 期货公司保证金账户信息：\n", "   - 保证金开户行（银行网点）\n", "   - 户名\n", "   - 账号\n", "\n", "3. 银期转账信息：\n", "   - 银期转账机构代码\n", "   - 资金密码（与交易密码区分）\n", "\n", "4. 交易所会员号（四位数字）：\n", "   - 郑州交易所（郑商所）\n", "   - 大连交易所（大商所）\n", "   - 上海交易所（上期所）\n", "   - 金融交易所（中金所）\n", "   - 能源中心（上能所）\n", "   - 广州交易所（广期所）\n", "\n", "5. 监控中心信息：\n", "   - 监控中心登录名\n", "   - 监控中心密码\n", "\n", "=== 处理要求 ===\n", "1. 完整性处理：\n", "   - 跨页数据自动拼接（如长文本的交易编码对应名称）\n", "   - 对每个交易所会员号分别识别\n", "   - 图片中不存在的数据字段输出\"None\"\n", "\n", "2. 格式规范：\n", "   ▸ 使用标准JSON格式（无注释、无尾随逗号）\n", "   ▸ 字符串值保留原始大小写和特殊字符（如\"6508Kxcl\"）\n", "   ▸ 字典键名严格使用中文（如\"郑州交易所\"非\"zhengzhou\"）\n", "\n", "3. 错误防护：\n", "   - 交易密码/资金密码/监控密码三者分别识别\n", "   - 交易编码/交易所会员号二者分别识别\n", "   - 排除页码/水印/无关表格干扰\n", "   - 账号类数字需完整提取（包含前置0）\n", "\n", "4. 输出示例：\n", "```json\n", "{\n", "  \"资金账户信息\": {\n", "    \"资金账号\": \"2120061\",\n", "    \"交易编码对应名称\": \"金瑞期货股份有限公司-金瑞前海资本管理(深圳)有限公司-金瑞同进尊享1号FOF单一资产管理计划\",\n", "    \"交易密码\": \"None\",\n", "    \"统一交易编码\": \"99909194\",\n", "    \"交易编码\": {\n", "      \"上期所\": \"81010373\",\n", "      \"大商所\": \"06837313\",\n", "      \"郑商所\": \"99871700\",\n", "      \"中金所（投机）\": \"00185013\",\n", "      \"中金所（套保）\": \"None\",\n", "      \"上能所\": \"81010376\",\n", "      \"广期所\": \"04471686\"\n", "    }\n", "  },\n", "  \"期货公司保证金账户信息\": {\n", "    \"保证金开户行\": \"交通银行上海分行营业部\",\n", "    \"户名\": \"宁波银行股份有限公司金瑞同进尊享1号FOF单一资产管理计划\",\n", "    \"账号\": \"31006666101880043387\"\n", "  },\n", "  \"银期转账信息\": {\n", "    \"银期转账机构代码\": \"000087\",\n", "    \"资金密码\": \"None\"\n", "  },\n", "  \"交易所会员号: {\n", "    \"郑州交易所\": \"0121\",\n", "    \"大连交易所\": \"0195\",\n", "    \"上海交易所\": \"0059\",\n", "    \"金融交易所\": \"0170\",\n", "    \"能源中心\": \"8059\",\n", "    \"广州交易所\": \"0021\"\n", "  },\n", "  \"监控中心信息\": {\n", "    \"监控中心登录名\": \"None\",\n", "    \"监控中心密码\": \"None\"\n", "  }\n", "}\n", "```\n", "\n", "请务必核对：\n", "1. 是否遗漏6个交易所中的任何一个\n", "2. 三个密码字段是否混淆\n", "3. JSON最后键值对无逗号\n", "4. 注意区分交易编码与交易所会员号，其中交易所会员号是四位数字，交易编码一般是8位数字\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 37, "id": "aaee57bf", "metadata": {}, "outputs": [], "source": ["pdf_file_name = \"test_data/大模型样例/期货交易会员解析/泰盈期货.PNG_1745473507696.png\"\n", "if pdf_file_name.endswith('.pdf'):\n", "    process_pdf(\n", "            pdf_path=pdf_file_name,     # 输入PDF路径\n", "            output_path=\"output.jpg\", # 输出图片路径\n", "            dpi=200,                  # 控制转换质量（推荐150-300）\n", "            border=15                 # 内容边界保留距离（像素）\n", "        )\n", "    res = chat_bot_img(img_prompt, img_url='output.jpg')\n", "else:\n", "    res = chat_bot_img(img_prompt, img_url=pdf_file_name)"]}, {"cell_type": "code", "execution_count": 38, "id": "b34c59fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"资金账户信息\": {\n", "    \"资金账号\": \"310066661018800297534\",\n", "    \"交易编码对应名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\",\n", "    \"交易密码\": \"None\",\n", "    \"统一交易编码\": \"None\",\n", "    \"交易编码\": {\n", "      \"上期所\": \"None\",\n", "      \"大商所\": \"None\",\n", "      \"郑商所\": \"None\",\n", "      \"中金所（投机）\": \"None\",\n", "      \"中金所（套保）\": \"None\",\n", "      \"上能所\": \"None\",\n", "      \"广期所\": \"None\"\n", "    }\n", "  },\n", "  \"期货公司保证金账户信息\": {\n", "    \"保证金开户行\": \"交通银行上海分行\",\n", "    \"户名\": \"宁波银行股份有限公司创金合信泰盈双季红6个月定期开放债券型证券投资基金\",\n", "    \"账号\": \"310066661018800297534\"\n", "  },\n", "  \"银期转账信息\": {\n", "    \"银期转账机构代码\": \"None\",\n", "    \"资金密码\": \"None\"\n", "  },\n", "  \"交易所会员号\": {\n", "    \"郑州交易所\": \"None\",\n", "    \"大连交易所\": \"None\",\n", "    \"上海交易所\": \"None\",\n", "    \"金融交易所\": \"None\",\n", "    \"能源中心\": \"None\",\n", "    \"广州交易所\": \"None\"\n", "  },\n", "  \"监控中心信息\": {\n", "    \"监控中心登录名\": \"None\",\n", "    \"监控中心密码\": \"None\"\n", "  }\n", "}\n", "```\n", "\n", "### 解析说明：\n", "1. **资金账户信息**：\n", "   - **资金账号**：从图片中提取为 `310066661018800297534`。\n", "   - **交易编码对应名称**：从图片中提取为“创金合信泰盈双季红6个月定期开放债券型证券投资基金”。\n", "   - **交易密码**：图片中未提供，因此设置为 `None`。\n", "   - **统一交易编码**：图片中未提供，因此设置为 `None`。\n", "   - **交易编码**：图片中未提供各交易所的交易编码，因此所有交易所的交易编码均设置为 `None`。\n", "\n", "2. **期货公司保证金账户信息**：\n", "   - **保证金开户行**：从图片中提取为“交通银行上海分行”。\n", "   - **户名**：从图片中提取为“宁波银行股份有限公司创金合信泰盈双季红6个月定期开放债券型证券投资基金”。\n", "   - **账号**：从图片中提取为 `310066661018800297534`。\n", "\n", "3. **银期转账信息**：\n", "   - **银期转账机构代码**：图片中未提供，因此设置为 `None`。\n", "   - **资金密码**：图片中未提供，因此设置为 `None`。\n", "\n", "4. **交易所会员号**：\n", "   - 所有交易所的会员号均未在图片中提供，因此所有交易所的会员号均设置为 `None`。\n", "\n", "5. **监控中心信息**：\n", "   - **监控中心登录名**：图片中未提供，因此设置为 `None`。\n", "   - **监控中心密码**：图片中未提供，因此设置为 `None`。\n", "\n", "### 注意事项：\n", "- 交易密码、资金密码和监控密码均未在图片中提供，因此均设置为 `None`。\n", "- 各交易所的交易编码和会员号均未在图片中提供，因此均设置为 `None`。\n", "- 账号类数字完整提取，包含前置0。\n", "- 严格按照JSON格式输出，无注释、无尾随逗号。\n"]}], "source": ["print(res)"]}, {"cell_type": "markdown", "id": "dc19d9b3", "metadata": {}, "source": ["测试下来发现：\"test_data/大模型样例/期货交易会员解析/均衡成长-期货户-中信期货.pdf_1745473949235.pdf\"\n", "这文件在测试中，始终无法正确捕获交易编码，其余字段表现正常"]}, {"cell_type": "markdown", "id": "be3f6955", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析类型统一管理工具
确保整个系统使用一致的分析类型配置
只支持config.py中定义的标准类型，不再支持旧类型
"""

def get_standard_analysis_types():
    """
    获取标准的分析类型配置
    与config.py中的ANALYSIS_TYPES保持一致
    """
    return {
        'futures_account': '期货账户/开户文件解析',
        'wealth_management': '理财产品说明书',
        'broker_interest': '券商账户计息变更',
        'account_opening': '账户开户场景',
        'ningyin_fee': '宁银理财费用变更',
        'non_standard_trade': '非标交易确认单解析'
    }

def get_display_name_mappings():
    """
    获取中文显示名称到英文键值的映射
    只支持标准的完整显示名称
    """
    standard_types = get_standard_analysis_types()

    # 创建反向映射：中文显示名称 -> 英文键值
    mappings = {}
    for key, display_name in standard_types.items():
        mappings[display_name] = key

    return mappings

def normalize_analysis_type(analysis_type):
    """
    标准化分析类型
    只支持标准的英文键值和完整的中文显示名称

    Args:
        analysis_type (str): 输入的分析类型

    Returns:
        str: 标准化后的分析类型键值，如果不是有效类型则返回None
    """
    if not analysis_type:
        return None

    # 如果已经是标准类型，直接返回
    standard_types = get_standard_analysis_types()
    if analysis_type in standard_types:
        return analysis_type

    # 检查是否是中文显示名称
    display_mappings = get_display_name_mappings()
    if analysis_type in display_mappings:
        return display_mappings[analysis_type]

    # 如果都不匹配，返回None表示无效类型
    return None

def get_analysis_type_display_name(analysis_type):
    """
    获取分析类型的显示名称

    Args:
        analysis_type (str): 分析类型键值

    Returns:
        str: 显示名称，如果不是有效类型则返回原值
    """
    # 先标准化类型
    normalized_type = normalize_analysis_type(analysis_type)

    if normalized_type:
        # 获取显示名称
        standard_types = get_standard_analysis_types()
        return standard_types.get(normalized_type, analysis_type)
    else:
        # 无效类型，返回原值
        return analysis_type

def validate_analysis_type(analysis_type):
    """
    验证分析类型是否有效

    Args:
        analysis_type (str): 分析类型

    Returns:
        tuple: (is_valid, normalized_type, display_name)
    """
    if not analysis_type:
        return False, None, None

    normalized_type = normalize_analysis_type(analysis_type)

    if normalized_type:
        standard_types = get_standard_analysis_types()
        return True, normalized_type, standard_types[normalized_type]
    else:
        return False, None, analysis_type

def get_all_supported_types():
    """
    获取所有支持的分析类型
    只返回标准类型

    Returns:
        dict: 所有支持的类型及其显示名称
    """
    # 只返回标准类型
    return get_standard_analysis_types()

# 标准分析类型常量
FUTURES_ACCOUNT = 'futures_account'
WEALTH_MANAGEMENT = 'wealth_management'
BROKER_INTEREST = 'broker_interest'
ACCOUNT_OPENING = 'account_opening'
NINGYIN_FEE = 'ningyin_fee'
NON_STANDARD_TRADE = 'non_standard_trade'

# 所有标准类型的列表
STANDARD_TYPES = [
    FUTURES_ACCOUNT,
    WEALTH_MANAGEMENT,
    BROKER_INTEREST,
    ACCOUNT_OPENING,
    NINGYIN_FEE,
    NON_STANDARD_TRADE
]

def is_valid_analysis_type(analysis_type):
    """
    快速检查分析类型是否有效

    Args:
        analysis_type (str): 分析类型

    Returns:
        bool: 是否有效
    """
    return normalize_analysis_type(analysis_type) is not None

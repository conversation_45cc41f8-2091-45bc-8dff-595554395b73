{% extends "base.html" %}

{% block title %}文档分析 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}文档分析{% endblock %}

{% block extra_css %}
<style>
    /* 上传区域样式 */
    .upload-section {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .upload-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .upload-header h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .upload-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
    }

    .analysis-types {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 2rem;
        background: var(--gray-50);
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
    }

    .analysis-type-card {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        text-align: center;
        background: white;
        position: relative;
        overflow: hidden;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .analysis-type-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gray-200);
        transition: var(--transition);
    }

    .analysis-type-card:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .analysis-type-card:hover::before {
        background: var(--primary-color);
    }

    .analysis-type-card.selected {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        box-shadow: var(--shadow-md);
    }

    .analysis-type-card.selected::before {
        background: var(--primary-color);
    }

    .analysis-type-icon {
        width: 64px;
        height: 64px;
        background: var(--gray-100);
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: var(--gray-600);
        transition: var(--transition);
    }

    .analysis-type-card.selected .analysis-type-icon,
    .analysis-type-card:hover .analysis-type-icon {
        background: var(--primary-color);
        color: white;
    }

    .analysis-type-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
    }

    .analysis-type-desc {
        font-size: 0.875rem;
        color: var(--gray-600);
        line-height: 1.5;
    }

    .upload-area {
        border: 3px dashed var(--gray-300);
        border-radius: var(--border-radius-lg);
        padding: 4rem 2rem;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
        background: white;
        margin: 2rem;
        position: relative;
    }

    .upload-area:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
    }

    .upload-area.dragover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: scale(1.02);
        box-shadow: var(--shadow-lg);
    }

    .upload-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: white;
        font-size: 2.5rem;
        box-shadow: var(--shadow-lg);
    }

    .upload-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 1rem;
    }

    .upload-desc {
        font-size: 1rem;
        color: var(--gray-600);
        margin-bottom: 0.5rem;
    }

    .upload-info {
        font-size: 0.875rem;
        color: var(--gray-500);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .file-preview {
        background: white;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin: 2rem;
        border: 1px solid var(--gray-200);
    }

    /* 文件列表样式 */
    .file-list-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        margin: 2rem 0;
    }

    .file-list-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    }

    .file-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
        position: relative;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item:hover {
        background: var(--gray-50);
    }

    .file-item.analyzed {
        background: #f0f9ff;
        border-left: 4px solid var(--success-color);
    }

    .file-item.deprecated {
        background: #fef2f2;
        opacity: 0.7;
        border-left: 4px solid var(--warning-color);
    }

    .file-item.analyzing {
        background: #fffbeb;
        border-left: 4px solid var(--primary-color);
    }

    .file-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        position: relative;
    }

    .file-status-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
        border: 2px solid white;
    }

    .file-status-badge.analyzed {
        background: var(--success-color);
    }

    .file-status-badge.analyzing {
        background: var(--primary-color);
        animation: pulse 2s infinite;
    }

    .file-status-badge.deprecated {
        background: var(--warning-color);
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .file-info {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }

    .file-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    /* 文件操作按钮组 - 与文件管理页面一致 */
    .file-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .file-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
        min-width: 70px;
        text-align: center;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
    }

    .file-checkbox {
        margin-right: 1rem;
        transform: scale(1.2);
    }

    .file-checkbox input[type="checkbox"] {
        cursor: pointer;
    }

    /* 废弃文件样式 - 与文件管理页面一致 */
    .file-deprecated {
        background-color: #f8f9fa !important;
    }

    .file-deprecated:hover {
        background-color: #e9ecef !important;
    }

    /* 废弃文件的按钮禁用样式 */
    .file-deprecated .btn:not(.btn-view-result):not(.btn-restore) {
        opacity: 0.5;
        pointer-events: none;
        cursor: not-allowed;
        background: #9ca3af !important;
        border-color: #9ca3af !important;
    }

    /* 废弃文件的复选框禁用样式 */
    .file-deprecated .tech-checkbox {
        opacity: 0.6;
        pointer-events: none;
        cursor: not-allowed;
    }

    /* 按钮颜色样式 - 与文件管理页面一致 */

    /* 分析按钮 - 绿色 */
    .btn-analyze {
        background: #059669;
        border: 1px solid #059669;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-analyze:hover {
        background: #047857;
        border-color: #047857;
        color: white;
    }

    /* 重分析按钮 - 深蓝色 */
    .btn-reanalyze {
        background: #1e40af;
        border: 1px solid #1e40af;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-reanalyze:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
        color: white;
    }

    /* 查看结果按钮 - 青色 */
    .btn-view-result {
        background: #0891b2;
        border: 1px solid #0891b2;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-view-result:hover {
        background: #0e7490;
        border-color: #0e7490;
        color: white;
    }

    /* 废弃按钮 - 橙红色 */
    .btn-deprecate {
        background: #dc2626;
        border: 1px solid #dc2626;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-deprecate:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        color: white;
    }

    /* 恢复按钮 - 天蓝色 */
    .btn-restore {
        background: #0ea5e9;
        border: 1px solid #0ea5e9;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-restore:hover {
        background: #0284c7;
        border-color: #0284c7;
        color: white;
    }

    /* 禁用状态的按钮 */
    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
        pointer-events: none;
    }

    /* 禁用状态的查看结果按钮 - 保持原色但变浅 */
    .btn-view-result:disabled {
        background: #0891b2;
        border-color: #0891b2;
        color: white;
        opacity: 0.4;
    }

    /* 确保弹窗固定比例布局正确 */
    #resultModal .modal-dialog,
    #analysisResultModal .modal-dialog {
        height: 95vh !important;
        margin: 2.5vh auto !important;
    }

    #resultModal .modal-content,
    #analysisResultModal .modal-content {
        height: 100% !important;
        overflow: hidden !important;
        display: flex !important;
        flex-direction: column !important;
    }

    #resultModal .modal-header,
    #analysisResultModal .modal-header {
        height: 8% !important;
        min-height: 60px !important;
        max-height: 80px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .stats-section,
    #analysisResultModal .stats-section {
        height: 12% !important;
        min-height: 80px !important;
        max-height: 120px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .modal-body,
    #analysisResultModal .modal-body {
        height: 70% !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    #resultModal .modal-footer-custom,
    #analysisResultModal .modal-footer-custom {
        height: 10% !important;
        min-height: 70px !important;
        max-height: 100px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .result-columns,
    #analysisResultModal .result-columns {
        overflow: hidden !important;
    }

    /* 确保四列内容区域滚动正常 */
    .four-columns-content {
        scrollbar-width: thin;
        scrollbar-color: var(--gray-400) var(--gray-100);
    }

    .four-columns-content::-webkit-scrollbar {
        width: 8px;
    }

    .four-columns-content::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .four-columns-content::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
    }

    .four-columns-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
    }

    /* 确保固定比例布局的稳定性 */
    #resultModal *,
    #analysisResultModal * {
        box-sizing: border-box !important;
    }

    #resultModal .modal-dialog,
    #analysisResultModal .modal-dialog {
        display: flex !important;
        align-items: center !important;
    }

    #resultModal .modal-content,
    #analysisResultModal .modal-content {
        margin: 0 !important;
        border-radius: 8px !important;
    }

    /* 防止内容溢出 */
    #resultModal .modal-header,
    #resultModal .stats-section,
    #resultModal .modal-footer-custom,
    #analysisResultModal .modal-header,
    #analysisResultModal .stats-section,
    #analysisResultModal .modal-footer-custom {
        overflow: hidden !important;
    }

    #resultModal .modal-body,
    #analysisResultModal .modal-body {
        min-height: 0 !important;
    }

    /* 确保主要内容底部有足够间隙 */
    #resultModal .modal-body {
        height: 68% !important;
    }

    #analysisResultModal .modal-body {
        height: 68% !important;
    }

    #resultModal #fourColumnsContentContainer,
    #analysisResultModal #analysisFourColumnsContentContainer {
        padding-bottom: 1rem !important;
    }

    #resultModal #originalFileContent,
    #analysisResultModal #analysisOriginalFileContent {
        padding-bottom: 1rem !important;
    }

    .footer-left, .footer-right {
        flex-shrink: 0;
    }

    .footer-left {
        flex: 1;
        max-width: 600px;
    }

    .modal-footer-custom .btn {
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .modal-footer-custom .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .modal-footer-custom .form-control {
        border-radius: 6px;
        border: 1px solid var(--gray-300);
        transition: border-color 0.2s ease;
    }

    .modal-footer-custom .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    .modal-footer-custom .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* 合并分组标题样式 */
    .merged-group-header-row {
        position: relative;
    }

    .merged-group-header {
        position: relative;
        z-index: 2;
    }

    .four-column-data-row {
        position: relative;
    }

    .four-column-data-row .field-row-aligned {
        margin: 0.25rem 0;
        border-radius: 4px;
        border-left: 3px solid var(--primary-color);
        background: var(--gray-50);
        display: flex;
        align-items: center;
        position: relative;
        transition: all 0.2s ease;
    }

    /* 四列布局中非备注字段的固定高度 */
    .four-column-data-row .field-row-aligned:not(.remarks-field) {
        height: 38px !important;
        min-height: 38px !important;
        max-height: 38px !important;
    }

    .four-column-data-row .field-row-aligned:hover {
        background: rgba(var(--primary-color-rgb), 0.05) !important;
        transform: translateX(2px);
    }

    .four-column-data-row .field-row-aligned.field-name {
        border-left-color: #6366f1;
    }

    .four-column-data-row .field-row-aligned.ai-value {
        border-left-color: #3b82f6;
    }

    .four-column-data-row .field-row-aligned.expected-value {
        border-left-color: #10b981;
    }

    .four-column-data-row .field-row-aligned.comparison-result {
        border-left-color: #f59e0b;
    }

    /* 四列容器中的备注字段特殊样式 */
    .four-column-data-row .field-row-aligned.remarks-field {
        height: auto !important;
        min-height: 38px !important;
        max-height: none !important;
        align-items: flex-start !important;
    }

    .four-column-data-row .field-row-aligned.remarks-field > div,
    .four-column-data-row .field-row-aligned.remarks-field > textarea,
    .four-column-data-row .field-row-aligned.remarks-field > input {
        height: auto !important;
        min-height: 32px !important;
        max-height: none !important;
        white-space: pre-wrap !important;
        word-break: break-word !important;
        overflow: visible !important;
        text-overflow: unset !important;
    }

    /* 强制覆盖所有可能的高度限制 */
    .remarks-field,
    .remarks-field > *,
    .four-column-data-row .remarks-field,
    .four-column-data-row .remarks-field > * {
        height: auto !important;
        max-height: none !important;
    }

    /* 自动调整高度的 textarea 样式 */
    .auto-resize-textarea {
        resize: vertical;
        overflow-y: auto;
        min-height: 32px;
        height: auto;
        font-family: 'Consolas', 'Monaco', monospace;
        line-height: 1.4;
    }

    /* 五列对齐样式（保留备用） */
    .five-columns-container {
        position: relative;
    }

    .five-columns-header {
        background: var(--gray-50);
        position: sticky;
        top: 0;
        z-index: 5;
    }

    .column-header {
        background: var(--gray-50);
        font-weight: 600;
    }

    .five-columns-content {
        background: white;
    }

    .column-content {
        background: white;
        overflow-wrap: break-word;
    }

    /* 分组单元格样式 */
    .group-cell-merged {
        position: relative;
    }

    .empty-cell {
        visibility: hidden;
    }

    /* 四列对齐样式（保留备用） */
    .four-columns-container {
        position: relative;
    }

    .four-columns-header {
        background: var(--gray-50);
        position: sticky;
        top: 0;
        z-index: 5;
    }

    .four-columns-content {
        background: white;
    }

    /* 展开/收起按钮样式 */
    .toggle-original-btn,
    .expand-original-btn {
        transition: all 0.3s ease !important;
    }

    .toggle-original-btn:hover,
    .expand-original-btn:hover {
        background: #4f46e5 !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }

    .toggle-original-btn:active,
    .expand-original-btn:active {
        transform: scale(0.95) !important;
    }

    /* 原件区域过渡动画 */
    .result-columns {
        transition: grid-template-columns 0.3s ease !important;
    }

    #originalFileArea {
        transition: all 0.3s ease !important;
    }

    .field-row-aligned {
        transition: background-color 0.2s ease;
        height: 38px !important;
        min-height: 38px !important;
        max-height: 38px !important;
    }

    .field-row-aligned:hover {
        background: rgba(var(--primary-color-rgb), 0.05) !important;
    }

    .field-row-aligned.field-name {
        border-left-color: #6366f1;
    }

    .field-row-aligned.field-name > div {
        font-weight: 500;
    }

    .field-row-aligned.ai-value {
        border-left-color: #3b82f6;
    }

    .field-row-aligned.expected-value {
        border-left-color: #10b981;
    }

    .field-row-aligned.comparison-result {
        border-left-color: #f59e0b;
    }

    .field-row-aligned.comparison-result > div {
        text-align: center;
        font-weight: 500;
        width: 100%;
    }

    /* 确保所有字段行内的元素都有统一高度 - 排除备注字段 */
    .field-row-aligned:not(.remarks-field) input,
    .field-row-aligned:not(.remarks-field) > div {
        height: 32px !important;
        box-sizing: border-box;
    }

    /* 可编辑字段特殊样式 */
    .field-row-aligned .editable-field {
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .field-row-aligned .editable-field:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
        outline: none;
    }

    .field-group-header-aligned:first-child {
        margin-top: 0;
    }

    /* 滚动条样式 */
    .five-columns-content::-webkit-scrollbar,
    .four-columns-content::-webkit-scrollbar {
        width: 8px;
    }

    .five-columns-content::-webkit-scrollbar-track,
    .four-columns-content::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .five-columns-content::-webkit-scrollbar-thumb,
    .four-columns-content::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
    }

    .five-columns-content::-webkit-scrollbar-thumb:hover,
    .four-columns-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
    }

    /* 禁用状态的分析按钮 - 保持原色但变浅 */
    .btn-analyze:disabled {
        background: #059669;
        border-color: #059669;
        color: white;
        opacity: 0.4;
    }

    /* 禁用状态的重分析按钮 - 保持原色但变浅 */
    .btn-reanalyze:disabled {
        background: #1e40af !important;
        border-color: #1e40af !important;
        color: white !important;
        opacity: 0.4;
    }

    /* 确保重分析按钮的颜色优先级 */
    .file-actions .btn-reanalyze {
        background: #1e40af !important;
        border-color: #1e40af !important;
        color: white !important;
    }

    .file-actions .btn-reanalyze:hover {
        background: #1d4ed8 !important;
        border-color: #1d4ed8 !important;
        color: white !important;
    }

    /* 分析类型标签样式 - 与文件管理页面一致 */
    .analysis-type-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
        text-align: center;
        min-width: 80px;
        white-space: nowrap;
    }

    /* 期货账户/开户文件解析 - 深蓝色 */
    .analysis-type-futures_account {
        background: rgba(30, 64, 175, 0.1);
        color: #1e40af;
        border-color: rgba(30, 64, 175, 0.2);
    }

    /* 理财产品说明书 - 青色 */
    .analysis-type-wealth_management {
        background: rgba(6, 182, 212, 0.1);
        color: #0891b2;
        border-color: rgba(6, 182, 212, 0.2);
    }

    /* 券商账户计息变更 - 紫色 */
    .analysis-type-broker_interest {
        background: rgba(124, 58, 237, 0.1);
        color: #7c3aed;
        border-color: rgba(124, 58, 237, 0.2);
    }

    /* 账户开户场景 - 绿色 */
    .analysis-type-account_opening {
        background: rgba(5, 150, 105, 0.1);
        color: #059669;
        border-color: rgba(5, 150, 105, 0.2);
    }

    /* 账户开户场景特殊界面样式 */
    .account-opening-interface {
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .product-type-selection {
        margin-bottom: 2rem;
    }

    .product-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .product-type-card {
        background: #fff;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
    }

    .product-type-card:hover {
        border-color: #059669;
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.15);
        transform: translateY(-2px);
    }

    .product-type-card.selected {
        border-color: #059669;
        background: rgba(5, 150, 105, 0.05);
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
    }

    .product-type-card .card-icon {
        font-size: 2.5rem;
        color: #059669;
        margin-bottom: 1rem;
    }

    .product-type-card .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .product-type-card .card-subtitle {
        font-size: 0.9rem;
        color: #6b7280;
        margin-bottom: 1rem;
    }

    .product-type-card .file-count-badge {
        background: #059669;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    /* 文档上传网格样式 */
    .document-upload-section {
        margin-top: 2rem;
    }

    .document-upload-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        justify-content: flex-start;
        margin-top: 1rem;
    }

    /* 根据文件框数量自动调整宽度 */
    .document-upload-grid[data-file-count="1"] .document-upload-item {
        flex: 0 0 calc(100% - 0.75rem);
        max-width: 400px;
    }

    .document-upload-grid[data-file-count="2"] .document-upload-item {
        flex: 0 0 calc(50% - 0.75rem);
        min-width: 280px;
    }

    .document-upload-grid[data-file-count="3"] .document-upload-item {
        flex: 0 0 calc(33.333% - 1rem);
        min-width: 250px;
    }

    /* 响应式适配 */
    @media (max-width: 768px) {
        .document-upload-grid .document-upload-item {
            flex: 0 0 100% !important;
        }

        .product-type-grid {
            grid-template-columns: 1fr;
        }
    }

    .document-upload-item {
        background: #f8fafc;
        border: 2px dashed #cbd5e1;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .document-upload-item:hover {
        border-color: #059669;
        background: rgba(5, 150, 105, 0.05);
    }

    .document-upload-item.has-file {
        border-style: solid;
        border-color: #059669;
        background: rgba(5, 150, 105, 0.05);
    }

    .document-upload-item.staged {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.05);
    }

    .document-upload-item .upload-icon {
        font-size: 2rem;
        color: #94a3b8;
        margin-bottom: 0.5rem;
    }

    .document-upload-item.has-file .upload-icon {
        color: #059669;
    }

    .document-upload-item.staged .upload-icon {
        color: #3b82f6;
    }

    .document-upload-item.uploading {
        border-color: #f59e0b;
        background: rgba(245, 158, 11, 0.05);
    }

    .document-upload-item.uploading .upload-icon {
        color: #f59e0b;
    }

    .document-upload-item.uploaded {
        border-color: #059669;
        background: rgba(5, 150, 105, 0.05);
    }

    .document-upload-item.uploaded .upload-icon {
        color: #059669;
    }

    .document-upload-item.analyzed {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.05);
    }

    .document-upload-item.analyzed .upload-icon {
        color: #10b981;
    }

    .document-upload-item .upload-label {
        font-weight: 600;
        color: #475569;
        margin-bottom: 0.5rem;
    }

    .document-upload-item .upload-hint {
        font-size: 0.8rem;
        color: #94a3b8;
        margin-bottom: 1rem;
    }

    .document-upload-item .file-info {
        margin-top: 0.5rem;
    }

    .document-upload-item .file-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #1f2937;
        word-break: break-all;
        margin-bottom: 0.25rem;
    }

    .document-upload-item .file-size {
        font-size: 0.8rem;
        color: #6b7280;
    }

    .document-upload-item .file-actions {
        margin-top: 1rem;
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .document-upload-item .file-actions .btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }

    .upload-actions {
        text-align: center;
        padding: 1.5rem 0;
        border-top: 1px solid #e5e7eb;
    }

    .analysis-section {
        text-align: center;
        padding: 1.5rem 0;
        border-top: 1px solid #e5e7eb;
    }

    /* 宁银理财费用变更 - 橙色 */
    .analysis-type-ningyin_fee {
        background: rgba(234, 88, 12, 0.1);
        color: #ea580c;
        border-color: rgba(234, 88, 12, 0.2);
    }

    /* 非标交易确认单解析 - 灰色 */
    .analysis-type-non_standard_trade {
        background: rgba(75, 85, 99, 0.1);
        color: #4b5563;
        border-color: rgba(75, 85, 99, 0.2);
    }

    /* 准确率指示器样式 - 与文件管理页面一致 */
    .accuracy-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .accuracy-bar {
        width: 60px;
        height: 6px;
        background: var(--gray-200);
        border-radius: 3px;
        overflow: hidden;
    }

    .accuracy-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .accuracy-excellent { background: linear-gradient(90deg, var(--success-color) 0%, #10b981 100%); }
    .accuracy-good { background: linear-gradient(90deg, var(--info-color) 0%, #0891b2 100%); }
    .accuracy-fair { background: linear-gradient(90deg, var(--warning-color) 0%, #d97706 100%); }
    .accuracy-poor { background: linear-gradient(90deg, var(--danger-color) 0%, #dc2626 100%); }

    /* 保持向后兼容性 */
    .accuracy-fill.high {
        background: linear-gradient(90deg, var(--success-color), #10b981);
    }

    .accuracy-fill.medium {
        background: linear-gradient(90deg, var(--warning-color), #f59e0b);
    }

    .accuracy-fill.low {
        background: linear-gradient(90deg, var(--danger-color), #ef4444);
    }

    /* 状态样式 - 与文件管理页面一致 */
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        white-space: nowrap;
    }

    .status-pending {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
    }

    .status-processing {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .status-completed {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
    }

    .status-failed {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .status-uploaded {
        background: rgba(168, 85, 247, 0.1);
        color: #a855f7;
    }

    .status-pending-audit {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .status-pending-review {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
    }

    .status-audit-rejected {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .status-review-rejected {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .btn-remove:hover {
        background: #dc2626;
        border-color: #dc2626;
        color: white;
    }

    .btn-reanalyze {
        background: var(--secondary-color);
        color: white;
        border-color: var(--secondary-color);
    }

    .btn-reanalyze:hover {
        background: #4b5563;
        border-color: #4b5563;
        color: white;
    }

    .file-pdf {
        background: #fee2e2;
        color: #dc2626;
    }

    .file-image {
        background: #dbeafe;
        color: #2563eb;
    }

    /* 分页样式 */
    .file-pagination {
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pagination-info {
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-btn {
        padding: 0.375rem 0.75rem;
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        font-size: 0.875rem;
    }

    .pagination-btn:hover:not(:disabled) {
        background: var(--gray-50);
        border-color: var(--gray-400);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* 空状态样式 */
    .file-list-empty {
        padding: 3rem 1.5rem;
        text-align: center;
        color: var(--gray-500);
    }

    .file-list-empty i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .file-info {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }

    .file-size {
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    .file-remove {
        width: 32px;
        height: 32px;
        border: none;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .file-remove:hover {
        background: #dc2626;
        transform: scale(1.1);
    }

    .progress-container {
        display: none;
        margin: 0.5rem 0;
        padding: 1rem;
        background: var(--gray-50);
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
    }

    .progress-container.show {
        display: block;
    }

    .progress-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .progress-title {
        font-weight: 600;
        color: var(--gray-900);
    }

    .progress-percent {
        font-weight: 600;
        color: var(--primary-color);
    }

    .action-buttons {
        padding: 2rem;
        text-align: center;
        background: white;
        border-top: 1px solid var(--gray-200);
    }

    .btn-analyze {
        background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        border: none;
        border-radius: var(--border-radius-lg);
        padding: 1rem 3rem;
        font-weight: 600;
        font-size: 1.125rem;
        color: white;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .btn-analyze::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-analyze:hover::before {
        left: 100%;
    }

    .btn-analyze:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4);
        color: white;
    }

    .btn-analyze:disabled {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
        cursor: not-allowed;
    }

    /* 平板设备适配 */
    @media (max-width: 1024px) and (min-width: 769px) {
        .analysis-types {
            max-width: 900px;
            gap: 1.25rem;
            padding: 1.5rem;
        }

        .analysis-type-card {
            min-height: 160px;
            padding: 1.25rem;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .upload-header {
            padding: 1.5rem;
        }

        .upload-header h2 {
            font-size: 1.5rem;
        }

        .analysis-types {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(3, 1fr);
            padding: 1rem;
            gap: 1rem;
        }

        .analysis-type-card {
            min-height: 140px;
            padding: 1rem;
        }

        .analysis-type-icon {
            width: 48px;
            height: 48px;
            font-size: 1.25rem;
        }

        .analysis-type-title {
            font-size: 1rem;
        }

        .analysis-type-desc {
            font-size: 0.75rem;
        }
    }

    /* 统一文件管理器样式 */
    .unified-file-manager {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .unified-file-list-container {
        border-top: 1px solid var(--gray-200);
    }

    .file-filters-container {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    /* 筛选控件样式 */
    .search-box {
        background: white;
        border: 1px solid var(--gray-300);
        color: var(--gray-900);
        border-radius: var(--border-radius);
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        background: white;
        border-color: var(--primary-color);
        color: var(--gray-900);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
    }

    .search-box::placeholder {
        color: var(--gray-500);
    }

    .btn-search {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border: 1px solid var(--primary-color);
        color: white;
        transition: all 0.2s ease;
    }

    .btn-search:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        border-color: var(--primary-dark);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
    }

    .btn-refresh {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        border: 1px solid #64748b;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-refresh:hover {
        background: linear-gradient(135deg, #475569 0%, #334155 100%);
        border-color: #475569;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(100, 116, 139, 0.2);
    }

    .btn-clear {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border: 1px solid #ef4444;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-clear:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-color: #dc2626;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
    }

    .file-list-content {
        min-height: auto;
    }

    .file-list-pagination {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 0.75rem 1.5rem 0.5rem 1.5rem;
        border-top: 1px solid var(--gray-200);
        border-radius: 0 0 8px 8px;
        display: none; /* 默认隐藏，只在历史记录视图且有多页时显示 */
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.02);
    }

    /* 分页内容容器 */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        gap: 1rem;
    }

    /* 分页信息样式 */
    .pagination-info-section {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-wrap: wrap;
        flex-shrink: 0;
    }

    .pagination-info-text {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
        white-space: nowrap;
        margin: 0;
    }

    .per-page-selector {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        background: white;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        flex-shrink: 0;
    }

    .per-page-selector label {
        color: #64748b;
        font-size: 0.75rem;
        font-weight: 500;
        margin: 0;
        white-space: nowrap;
    }

    .per-page-selector select {
        border: none;
        background: transparent;
        color: #374151;
        font-weight: 600;
        font-size: 0.8rem;
        padding: 0;
        width: auto;
        min-width: 50px;
    }

    .per-page-selector select:focus {
        outline: none;
        box-shadow: none;
    }

    /* 分页按钮组样式 */
    .pagination-buttons {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        background: white;
        padding: 0.25rem;
        border-radius: 10px;
        border: 1px solid #e2e8f0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        flex-shrink: 0;
    }

    .pagination-buttons .btn {
        border: none;
        border-radius: 6px;
        padding: 0.375rem 0.75rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination-buttons .btn-outline-secondary {
        background: transparent;
        color: #64748b;
        border: none;
    }

    .pagination-buttons .btn-outline-secondary:hover {
        background: #f1f5f9;
        color: #374151;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .pagination-buttons .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .pagination-buttons .btn-primary:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    }

    /* 分页省略号样式 */
    .pagination-ellipsis {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        color: #94a3b8;
        font-weight: 600;
        font-size: 0.8rem;
        user-select: none;
        padding: 0 0.25rem;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .file-list-pagination {
            padding: 0.5rem 1rem;
        }

        .pagination-container {
            flex-direction: column;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination-info-section {
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .pagination-buttons {
            justify-content: center;
        }

        .pagination-info-text {
            font-size: 0.8rem;
        }

        .per-page-selector {
            padding: 0.2rem 0.4rem;
        }

        .per-page-selector label {
            font-size: 0.7rem;
        }

        .per-page-selector select {
            font-size: 0.75rem;
            min-width: 45px;
        }
    }

    /* 高亮提醒效果 */
    .highlight-section {
        animation: highlightPulse 2s ease-in-out;
        border-radius: 12px;
    }

    @keyframes highlightPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
            background-color: rgba(255, 193, 7, 0.1);
        }
        50% {
            box-shadow: 0 0 0 20px rgba(255, 193, 7, 0);
            background-color: rgba(255, 193, 7, 0.2);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
            background-color: transparent;
        }

    }

    /* 视图切换按钮样式 */
    .btn-group .btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* 文件项样式增强 */
    .file-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
        background: white;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item:hover {
        background: var(--gray-50);
    }

    .file-item.selected {
        background: var(--primary-bg);
        border-left: 4px solid var(--primary-color);
    }

    .file-checkbox {
        margin-right: 1rem;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        background: var(--gray-100);
        color: var(--gray-600);
    }

    .file-icon.file-pdf {
        background: #fee2e2;
        color: #dc2626;
    }

    .file-icon.file-image {
        background: #dbeafe;
        color: #2563eb;
    }

    .file-info {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }

    .file-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: var(--gray-600);
        flex-wrap: wrap;
    }

    .file-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 1rem;
    }

    /* 状态徽章样式 */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-badge.uploaded {
        background: #e0f2fe;
        color: #0277bd;
    }

    .status-badge.pending {
        background: #fef3c7;
        color: #92400e;
    }

    .status-badge.processing {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-badge.completed {
        background: #d1fae5;
        color: #065f46;
    }

    .status-badge.failed {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-badge.analyzed {
        background: #f3e8ff;
        color: #7c3aed;
    }

    .status-badge.deprecated {
        background: #f3f4f6;
        color: #6b7280;
    }

    /* 文件状态图标样式 */
    .file-status-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
        z-index: 1;
    }

    .file-status-badge.analyzing {
        background: var(--primary-color);
        animation: spin 1s linear infinite;
    }

    .file-status-badge.analyzed {
        background: var(--success-color);
    }

    .file-status-badge.failed {
        background: var(--danger-color);
    }

    .file-status-badge.deprecated {
        background: var(--gray-500);
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 文件项状态样式 */
    .file-item.analyzing {
        border-left: 4px solid var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    .file-item.analyzed {
        border-left: 4px solid var(--success-color);
    }

    .file-item.failed {
        border-left: 4px solid var(--danger-color);
        background: rgba(var(--danger-rgb), 0.05);
    }

    .file-item.deprecated {
        opacity: 0.6;
        background: var(--gray-50);
    }

    /* 分析结果弹窗样式 */
    .modal-xl {
        max-width: 95%;
    }

    .modal-header-actions {
        display: flex;
        align-items: center;
    }

    #analysisResultContent {
        min-height: 400px;
    }

    .result-content {
        padding: 1.5rem;
    }

    .result-section {
        margin-bottom: 2rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        overflow: hidden;
    }

    .result-section-header {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        color: var(--text-color);
    }

    .result-section-body {
        padding: 1.5rem;
    }

    .result-field {
        display: flex;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .result-field:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .result-field-label {
        font-weight: 600;
        color: var(--text-secondary);
        min-width: 120px;
        margin-right: 1rem;
    }

    .result-field-value {
        flex: 1;
        color: var(--text-color);
        word-break: break-word;
    }

    .result-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .result-status.success {
        background: var(--success-light);
        color: var(--success-color);
    }

    .result-status.error {
        background: var(--danger-light);
        color: var(--danger-color);
    }

    .result-status.warning {
        background: var(--warning-light);
        color: var(--warning-color);
    }

    .result-json {
        background: var(--gray-50);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .result-error {
        background: var(--danger-light);
        border: 1px solid var(--danger-color);
        border-radius: 6px;
        padding: 1rem;
        color: var(--danger-color);
        text-align: center;
    }

    .result-loading {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    /* 预期结果编辑样式 */
    .editable-expected-result {
        background: var(--gray-50);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 1rem;
    }

    .editable-input {
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .editable-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        outline: 0;
    }

    .editable-field {
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: background-color 0.15s ease-in-out;
    }

    .editable-field:hover {
        background: var(--gray-100);
    }

    .result-section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .result-section-header button {
        margin-left: auto;
    }

    /* 文件管理栏分析按钮样式 */
    .file-list-header .btn-group + .d-flex {
        margin-left: 1rem;
    }

    .file-list-header .vr {
        height: 1.5rem;
        opacity: 0.3;
    }

    #analyzeBtn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    #analyzeBtn.btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        transition: all 0.2s ease;
    }

    #analyzeBtn.btn-success:hover:not(:disabled) {
        background: linear-gradient(135deg, #218838, #1ea085);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        transform: translateY(-1px);
    }

    #analyzeBtn.btn-success:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    /* 平滑滚动效果 */
    html {
        scroll-behavior: smooth;
    }

    /* 上传区域高亮动画 */
    .upload-area.highlight {
        animation: uploadHighlight 0.6s ease-out;
    }

    @keyframes uploadHighlight {
        0% {
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        50% {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    }

    /* 自动分析开关样式 */
    #autoAnalysisSwitch {
        cursor: pointer;
    }

    #autoAnalysisSwitch:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    #autoAnalysisSwitch + label {
        cursor: pointer;
        transition: color 0.2s ease;
        user-select: none;
    }

    #autoAnalysisSwitch:checked + label {
        color: #28a745 !important;
        font-weight: 500;
    }



    /* 文件项复选框对齐 */
    .file-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
    }

    .file-item .file-checkbox {
        transform: scale(1.2);
        margin-right: 1rem;
        flex-shrink: 0;
    }

    /* 自动分析开关样式 */
    #autoAnalysisSwitch {
        cursor: pointer;
    }

    #autoAnalysisSwitch:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    #autoAnalysisSwitch + label {
        cursor: pointer;
        transition: color 0.2s ease;
        user-select: none;
    }

    #autoAnalysisSwitch:checked + label {
        color: #28a745 !important;
        font-weight: 500;
    }

    /* 自动分析消息样式 */
    .auto-analysis-message {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        border-left: 4px solid #2196f3;
        padding: 12px 16px;
        border-radius: 8px;
        margin: 8px 0;
    }

    .auto-analysis-message .bi {
        color: #2196f3;
    }

    /* 超小屏幕 */
    @media (max-width: 480px) {
        .analysis-types {
            grid-template-columns: 1fr;
            grid-template-rows: repeat(6, 1fr);
            padding: 0.5rem;
            gap: 0.75rem;
        }

        .analysis-type-card {
            min-height: 120px;
            padding: 0.75rem;
        }

        .upload-area {
            padding: 2rem 1rem;
            margin: 1rem;
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }

        .upload-title {
            font-size: 1.25rem;
        }

        .action-buttons {
            padding: 1.5rem;
        }

        .btn-analyze {
            padding: 0.875rem 2rem;
            font-size: 1rem;
        }

        .history-record-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .history-record-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .history-record-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }

    /* JSON字段样式 */
    .json-field {
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        background: var(--gray-50);
        border-radius: var(--border-radius-sm);
        border-left: 3px solid var(--primary-color);
    }

    .json-field-name {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.25rem;
    }

    .json-field-value {
        color: var(--gray-700);
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
    }

    .json-field-value.editable {
        background: white;
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius-sm);
        padding: 0.375rem;
        min-height: 2rem;
        cursor: text;
    }

    .json-field-value.editable:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }



    /* 全屏模态框样式 */
    .modal-fullscreen .modal-body {
        padding: 0;
    }

    #analysisFullscreenContent iframe {
        width: 100% !important;
        height: 100% !important;
        border: none !important;
    }



    /* 响应式调整 */
    @media (max-width: 1200px) {
        #resultModal .result-columns,
        #analysisResultModal .result-columns {
            grid-template-columns: 1fr 350px !important;
        }
    }

    /* 响应式布局 - 保持固定比例 */
    @media (max-width: 768px) {
        #resultModal .stats-section .row,
        #analysisResultModal .stats-section .row {
            text-align: center;
        }

        #resultModal .stats-section .stat-value,
        #analysisResultModal .stats-section .stat-value {
            font-size: 1.25rem !important;
        }

        #resultModal .result-columns,
        #analysisResultModal .result-columns {
            grid-template-columns: 1fr !important;
            grid-template-rows: 1fr auto;
            gap: 0.5rem;
            padding: 0.5rem;
        }

        #resultModal .result-column,
        #analysisResultModal .result-column {
            padding: 0.75rem !important;
        }

        #resultModal .four-columns-header .column-header,
        #analysisResultModal .four-columns-header .column-header {
            padding: 0.5rem !important;
            font-size: 0.75rem !important;
        }

        #resultModal .four-column-data-row .column-data,
        #analysisResultModal .four-column-data-row .column-data {
            padding: 0.5rem !important;
            font-size: 0.875rem !important;
        }
        #resultModal .modal-dialog,
        #analysisResultModal .modal-dialog {
            height: 90vh !important;
            margin: 5vh auto !important;
        }

        #resultModal .modal-header,
        #analysisResultModal .modal-header {
            height: 10% !important;
            min-height: 50px !important;
            max-height: 70px !important;
            padding: 0.75rem 1rem !important;
        }

        #resultModal .stats-section,
        #analysisResultModal .stats-section {
            height: 15% !important;
            min-height: 70px !important;
            max-height: 100px !important;
            padding: 0.75rem 1rem !important;
        }

        #resultModal .modal-body,
        #analysisResultModal .modal-body {
            height: 63% !important;
        }

        #resultModal .modal-footer-custom,
        #analysisResultModal .modal-footer-custom {
            height: 10% !important;
            min-height: 60px !important;
            max-height: 80px !important;
            padding: 0.75rem 1rem !important;
        }

        .modal-footer-custom .d-flex {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .footer-left {
            order: 1;
            max-width: 100%;
        }

        .footer-right {
            order: 2;
            align-self: flex-end;
        }

        .footer-left .d-flex {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .footer-left .form-control {
            width: 100% !important;
        }
    }

    @media (max-width: 576px) {
        #resultModal .modal-dialog,
        #analysisResultModal .modal-dialog {
            height: 85vh !important;
            margin: 7.5vh auto !important;
        }

        #resultModal .modal-header,
        #analysisResultModal .modal-header {
            height: 12% !important;
            min-height: 45px !important;
            max-height: 60px !important;
            padding: 0.5rem !important;
        }

        #resultModal .stats-section,
        #analysisResultModal .stats-section {
            height: 18% !important;
            min-height: 60px !important;
            max-height: 90px !important;
            padding: 0.5rem !important;
        }

        #resultModal .modal-body,
        #analysisResultModal .modal-body {
            height: 58% !important;
        }

        #resultModal .modal-footer-custom,
        #analysisResultModal .modal-footer-custom {
            height: 10% !important;
            min-height: 50px !important;
            max-height: 70px !important;
            padding: 0.5rem !important;
        }

        .footer-right .d-flex {
            flex-direction: column;
            gap: 0.25rem !important;
        }

        .footer-right .btn {
            width: 100%;
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
        }

        .footer-right {
            align-self: stretch;
        }

        .modal-title {
            font-size: 1rem !important;
        }

        .stats-section .col-md-3 {
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 1.25rem !important;
        }
    }

    /* 加载遮罩层样式 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 3rem;
        height: 3rem;
        color: white;
    }

    /* 数组项样式 */
    .array-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        padding: 2px 0;
    }

    .array-item:last-child {
        margin-bottom: 0;
    }

    .array-item-text {
        font-size: 0.9em;
        line-height: 1.3;
        word-break: break-word;
    }

    .array-item .badge {
        font-size: 0.7em;
        min-width: 20px;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<!-- 文档分析主区域 -->
<div class="upload-section">
    <!-- 页面头部 -->
    <div class="upload-header">
        <h2>智能文档分析</h2>
        <p>选择分析类型，上传您的文档，让AI为您智能识别和分析</p>
    </div>

    <!-- 分析类型选择 -->
    <div class="analysis-types" id="analysisTypes">
        <!-- 动态生成分析类型选项 -->
    </div>

    <!-- 统一文件管理区域 -->
    <div class="unified-file-manager" id="fileManagementArea">
        <!-- 账户开户场景特殊界面 -->
        <div class="account-opening-interface" id="accountOpeningInterface" style="display: none;">
            <!-- 产品类型选择 -->
            <div class="product-type-selection" id="productTypeSelection">
                <h4 class="mb-3">
                    <i class="bi bi-person-plus text-success"></i>
                    选择产品类型
                </h4>
                <div class="product-type-grid" id="productTypeGrid">
                    <!-- 产品类型卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 文档上传区域 -->
            <div class="document-upload-section" id="documentUploadSection" style="display: none;">
                <div class="section-header">
                    <h4 class="mb-3">
                        <i class="bi bi-cloud-upload text-primary"></i>
                        上传相关文档
                    </h4>
                    <p class="text-muted mb-3">请根据产品类型上传对应的文档文件</p>
                </div>

                <!-- 文档上传网格 -->
                <div class="document-upload-grid" id="documentUploadGrid">
                    <!-- 文件上传框将在这里动态生成 -->
                </div>

                <!-- 操作按钮区域 -->
                <div class="upload-actions mt-4">
                    <button type="button" class="btn btn-success" id="completeUploadBtn" style="display: none;">
                        <i class="bi bi-check-circle"></i>
                        上传完毕
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="resetUploadBtn">
                        <i class="bi bi-arrow-clockwise"></i>
                        重新选择
                    </button>
                </div>

                <!-- 分析按钮 -->
                <div class="analysis-section mt-4" id="analysisSection" style="display: none;">
                    <button type="button" class="btn btn-primary btn-lg" id="startAccountOpeningAnalysisBtn">
                        <i class="bi bi-play-circle"></i>
                        开始分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 常规文件上传区域 -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">
                <i class="bi bi-cloud-upload"></i>
            </div>
            <h3 class="upload-title">拖拽文件到此处或点击选择文件</h3>
            <p class="upload-desc">支持 PDF、PNG、JPG、JPEG、GIF、BMP、TIFF 格式</p>
            <p class="upload-info">
                <i class="bi bi-info-circle"></i>
                最大文件大小：200MB，支持批量上传
            </p>
            <input type="file" id="fileInput" class="d-none" accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.tiff" multiple>
        </div>

        <!-- 统一文件列表区域 -->
        <div class="unified-file-list-container">
            <!-- 文件列表头部 -->
            <div class="file-list-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-files me-2"></i>
                        文件管理列表
                        <span class="badge bg-primary ms-2" id="totalFileCountBadge">0</span>
                    </h5>
                    <div class="d-flex align-items-center gap-2">
                        <!-- 视图切换按钮 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary active" id="currentFilesTab" onclick="switchFileView('current')">
                                <i class="bi bi-upload me-1"></i>当前上传 (<span id="currentFileCount">0</span>)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="historyFilesTab" onclick="switchFileView('history')">
                                <i class="bi bi-clock-history me-1"></i>历史记录 (<span id="historyFileCount">0</span>)
                            </button>
                        </div>
                        <!-- 操作按钮 -->
                        <div class="d-flex align-items-center gap-1">
                            <!-- 自动分析开关 -->
                            <div class="form-check form-switch me-2">
                                <input class="form-check-input" type="checkbox" id="autoAnalysisSwitch" checked title="开启后，单文件上传时自动开始分析">
                                <label class="form-check-label text-muted" for="autoAnalysisSwitch" style="font-size: 0.875rem;">
                                    自动分析
                                </label>
                            </div>
                            <div class="vr mx-1"></div>
                            <!-- 一键自动审核按钮 -->
                            <button class="btn btn-sm btn-warning" id="autoAuditBtn" onclick="autoAuditFiles()" title="自动审核100%识别率的待审核文件">
                                <i class="bi bi-magic me-1"></i>一键自动审核
                            </button>
                            <div class="vr mx-1"></div>
                            <!-- 开始智能分析按钮 -->
                            <button class="btn btn-sm btn-success" id="analyzeBtn" onclick="startAnalysis()" disabled title="开始智能分析">
                                <i class="bi bi-cpu me-1"></i>开始分析
                            </button>
                            <div class="vr mx-1"></div>
                            <button class="btn btn-sm btn-success" onclick="refreshFileList()" title="刷新列表">
                                <i class="bi bi-arrow-clockwise me-1"></i>刷新列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选控件区域 -->
            <div class="file-filters-container" id="fileFiltersContainer">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control search-box" id="fileSearchInput"
                                   placeholder="搜索文件名、分析类型..." onkeyup="searchFiles()">
                            <button class="btn btn-search" onclick="searchFiles()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select search-box" id="fileTypeFilter" onchange="filterFiles()">
                            <option value="">全部分析类型</option>
                            <option value="futures_account">期货账户/开户文件解析</option>
                            <option value="wealth_management">理财产品说明书</option>
                            <option value="broker_interest">券商账户计息变更</option>
                            <option value="account_opening">账户开户场景</option>
                            <option value="ningyin_fee">宁银理财费用变更</option>
                            <option value="non_standard_trade">非标交易确认单解析</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select search-box" id="fileStatusFilter" onchange="filterFiles()">
                            <option value="">全部状态</option>
                            <option value="pending">待分析</option>
                            <option value="uploaded">已上传</option>
                            <option value="processing">处理中</option>
                            <option value="analyzing">分析中</option>
                            <option value="completed">已完成</option>
                            <option value="analyzed">已分析</option>
                            <option value="pending_audit">待审核</option>
                            <option value="pending_review">待复核</option>
                            <option value="audit_rejected">审核不通过</option>
                            <option value="review_rejected">复核不通过</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select search-box" id="fileActiveStatusFilter" onchange="filterFiles()">
                            <option value="all" selected>全部文件</option>
                            <option value="active">正常文件</option>
                            <option value="deprecated">废弃文件</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex gap-2 justify-content-end">
                            <button class="btn btn-sm btn-clear" onclick="clearFileFilters()">
                                <i class="bi bi-x-circle me-1"></i>清除筛选
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件列表内容 -->
            <div class="file-list-content">
                <!-- 当前上传文件列表 -->
                <div id="currentFilesList" class="file-list">
                    <div class="table-responsive">
                        <table class="table table-hover file-table">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="tech-checkbox" id="selectAllCurrent" onchange="toggleSelectAllCurrent(this.checked)">
                                    </th>
                                    <th>文件名</th>
                                    <th width="120">分析类型</th>
                                    <th width="100">状态</th>
                                    <th width="100">准确率</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="currentFilesTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <i class="bi bi-inbox display-4 text-muted"></i>
                                        <div class="mt-3 text-muted">暂无上传文件</div>
                                        <div class="text-muted">请先选择分析类型并上传文件</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 历史记录文件列表 -->
                <div id="historyFilesList" class="file-list" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-hover file-table">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="tech-checkbox" id="selectAllHistory" onchange="toggleSelectAllHistory(this.checked)">
                                    </th>
                                    <th>文件名</th>
                                    <th width="120">分析类型</th>
                                    <th width="100">状态</th>
                                    <th width="100">准确率</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyFilesTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <div class="mt-2 text-muted">正在加载历史记录...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="file-list-pagination" id="filePagination">
                <!-- 动态生成分页控件 -->
            </div>
        </div>
    </div>

        <!-- 进度条 -->
        <div class="progress-container" id="progressContainer" style="display: none;">
            <div class="progress-header">
                <span class="progress-title">上传进度</span>
                <span class="progress-percent" id="progressPercent">0%</span>
            </div>
            <div class="progress mb-3">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
            </div>
            <div class="text-center">
                <small class="text-muted" id="progressText">准备上传...</small>
            </div>
        </div>

    </div>
</div>

<!-- 使用说明 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle me-2"></i>
                    使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>支持的文档类型</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success me-2"></i>期货账户/开户文件解析</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>理财产品说明书</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>券商账户计息变更文档</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>期货交易会员文档</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>宁银理财费用变更</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>非标交易确认单</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>使用步骤</h6>
                        <ol class="list-unstyled">
                            <li><span class="badge bg-primary me-2">1</span>选择对应的分析类型</li>
                            <li><span class="badge bg-primary me-2">2</span>上传需要分析的文档</li>
                            <li><span class="badge bg-primary me-2">3</span>在文件管理栏点击"开始分析"</li>
                            <li><span class="badge bg-primary me-2">4</span>等待AI分析完成</li>
                            <li><span class="badge bg-primary me-2">5</span>查看分析结果和准确率</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let selectedAnalysisType = '';
    let fileIdCounter = 0;

    // 按分析类型分组的文件管理
    let filesByType = {}; // 按分析类型分组存储文件 { 'futures_account': [], 'wealth_management': [], ... }
    let fileRecordsByType = {}; // 按分析类型分组存储文件记录 { 'futures_account': [], 'wealth_management': [], ... }

    // 统一文件管理相关变量
    let currentFileView = 'current'; // 'current' 或 'history'
    let historyFiles = []; // 历史记录文件
    let filteredFiles = []; // 筛选后的文件

    // 账户开户场景专用变量
    let accountOpeningStagedFiles = {}; // 暂存的文件 { slotIndex: fileObject }
    let accountOpeningFilePaths = []; // 上传成功的文件路径
    let accountOpeningRecordName = ''; // 记录名称
    let currentAccountOpeningSubtype = null; // 当前选择的产品子类型

    // 防护标志
    window.accountOpeningStagingInProgress = false; // 暂存进行中标志
    window.fileSlotClickInProgress = false; // 防重复点击标志
    window.userClickedFileSlot = false; // 用户点击文件槽位标志

    // 账户开户场景子类型配置
    const accountOpeningSubTypes = {
        'insurance_product': {
            name: '保险产品',
            fileCount: 3,
            suggestedDocuments: ['资管协议', '托管协议', '管理人信息备忘录'],
            icon: 'bi-shield-check',
            description: '保险资管产品开户文档'
        },
        'public_fund': {
            name: '公募基金',
            fileCount: 1,
            suggestedDocuments: ['资管合同'],
            icon: 'bi-graph-up',
            description: '公募基金产品开户文档'
        },
        'fund_account': {
            name: '基金专户',
            fileCount: 2,
            suggestedDocuments: ['资管合同', '管理人信息备忘录'],
            icon: 'bi-briefcase',
            description: '基金专户产品开户文档'
        },
        'wealth_product': {
            name: '理财产品',
            fileCount: 3,
            suggestedDocuments: ['托管协议', '资管合同', '管理人信息备忘录'],
            icon: 'bi-piggy-bank',
            description: '理财产品开户文档'
        },
        'broker_asset': {
            name: '券商资管',
            fileCount: 2,
            suggestedDocuments: ['资管合同', '管理人信息备忘录'],
            icon: 'bi-building',
            description: '券商资管产品开户文档'
        },
        'private_fund': {
            name: '私募基金',
            fileCount: 3,
            suggestedDocuments: ['托管协议', '资管合同', '管理人信息备忘录'],
            icon: 'bi-lock',
            description: '私募基金产品开户文档'
        },
        'trust_product': {
            name: '信托产品',
            fileCount: 3,
            suggestedDocuments: ['信托合同', '补充协议', '托管协议'],
            icon: 'bi-bank',
            description: '信托产品开户文档'
        }
    };
    let fileFilters = {
        type: '',
        status: '',
        activeStatus: 'all',
        search: ''
    };
    let filePagination = {
        currentPage: 1,
        itemsPerPage: 20,
        totalItems: 0,
        totalPages: 0
    };

    // 账户开户场景界面管理函数
    function showAccountOpeningInterface() {
        console.log('显示账户开户场景界面');

        // 隐藏常规上传界面
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.style.display = 'none';
        }

        // 显示账户开户场景界面
        const accountOpeningInterface = document.getElementById('accountOpeningInterface');
        if (accountOpeningInterface) {
            accountOpeningInterface.style.display = 'block';
            generateProductTypeGrid();
        }

        // 设置防护标志
        window.accountOpeningStagingInProgress = true;

        // 延迟重置防护标志，允许用户操作
        setTimeout(() => {
            window.accountOpeningStagingInProgress = false;
        }, 500);
    }

    function hideAccountOpeningInterface() {
        console.log('隐藏账户开户场景界面');

        // 显示常规上传界面
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.style.display = 'block';
        }

        // 隐藏账户开户场景界面
        const accountOpeningInterface = document.getElementById('accountOpeningInterface');
        if (accountOpeningInterface) {
            accountOpeningInterface.style.display = 'none';
        }

        // 重置状态
        resetAccountOpeningState();
    }

    function resetAccountOpeningState() {
        console.log('重置账户开户场景状态');

        // 清空暂存文件
        accountOpeningStagedFiles = {};
        accountOpeningFilePaths = [];
        accountOpeningRecordName = '';
        currentAccountOpeningSubtype = null;

        // 重置防护标志
        window.accountOpeningStagingInProgress = false;
        window.fileSlotClickInProgress = false;
        window.userClickedFileSlot = false;

        // 隐藏文档上传区域
        const documentUploadSection = document.getElementById('documentUploadSection');
        if (documentUploadSection) {
            documentUploadSection.style.display = 'none';
        }

        // 隐藏分析按钮
        const analysisSection = document.getElementById('analysisSection');
        if (analysisSection) {
            analysisSection.style.display = 'none';
        }

        // 清空产品类型选择
        const productTypeCards = document.querySelectorAll('.product-type-card');
        productTypeCards.forEach(card => card.classList.remove('selected'));

        // 清空文档上传网格的事件标记
        const documentUploadGrid = document.getElementById('documentUploadGrid');
        if (documentUploadGrid) {
            documentUploadGrid.removeAttribute('data-events-setup');
        }
    }

    // 生成产品类型网格
    function generateProductTypeGrid() {
        console.log('生成产品类型网格');

        const container = document.getElementById('productTypeGrid');
        if (!container) return;

        container.innerHTML = '';

        Object.keys(accountOpeningSubTypes).forEach(subtypeId => {
            const subtype = accountOpeningSubTypes[subtypeId];

            const card = document.createElement('div');
            card.className = 'product-type-card';
            card.dataset.subtypeId = subtypeId;

            card.innerHTML = `
                <div class="card-icon">
                    <i class="${subtype.icon}"></i>
                </div>
                <div class="card-title">${subtype.name}</div>
                <div class="card-subtitle">${subtype.description}</div>
                <div class="file-count-badge">${subtype.fileCount}个文件</div>
            `;

            card.addEventListener('click', () => selectProductType(subtypeId));
            container.appendChild(card);
        });
    }

    // 选择产品类型
    function selectProductType(subtypeId) {
        console.log('选择产品类型:', subtypeId);

        // 移除其他选中状态
        const productTypeCards = document.querySelectorAll('.product-type-card');
        productTypeCards.forEach(card => card.classList.remove('selected'));

        // 设置当前选中
        const selectedCard = document.querySelector(`[data-subtype-id="${subtypeId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // 保存当前选择
        currentAccountOpeningSubtype = subtypeId;

        // 设置防护标志
        window.accountOpeningStagingInProgress = true;

        // 显示文档上传区域
        const documentUploadSection = document.getElementById('documentUploadSection');
        if (documentUploadSection) {
            documentUploadSection.style.display = 'block';
            generateDocumentUploadGrid(subtypeId);
        }

        // 延迟重置防护标志
        setTimeout(() => {
            window.accountOpeningStagingInProgress = false;
        }, 500);
    }

    // 生成文档上传网格
    function generateDocumentUploadGrid(subtypeId) {
        console.log('生成文档上传网格:', subtypeId);

        const container = document.getElementById('documentUploadGrid');
        if (!container) return;

        const subtype = accountOpeningSubTypes[subtypeId];
        if (!subtype) return;

        // 清空容器
        container.innerHTML = '';

        // 设置文件框数量属性
        container.setAttribute('data-file-count', subtype.fileCount);

        // 创建指定数量的文件上传框
        for (let i = 0; i < subtype.fileCount; i++) {
            const uploadItem = createFileUploadSlot(i, subtype.suggestedDocuments[i]);
            container.appendChild(uploadItem);
        }

        // 设置事件监听器
        setupDocumentUploadEvents(subtypeId);
    }

    // 创建文件上传槽位
    function createFileUploadSlot(slotIndex, suggestedDocument) {
        const uploadItem = document.createElement('div');
        uploadItem.className = 'document-upload-item';
        uploadItem.dataset.slotClick = slotIndex;

        uploadItem.innerHTML = `
            <div class="upload-icon">
                <i class="bi bi-cloud-upload"></i>
            </div>
            <div class="upload-label">槽位 ${slotIndex + 1}</div>
            <div class="upload-hint">建议上传：${suggestedDocument}</div>
            <div class="upload-status">点击选择文件</div>
            <input type="file" id="fileInput_${slotIndex}" class="d-none"
                   accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.tiff">
            <div class="file-info" style="display: none;">
                <div class="file-name"></div>
                <div class="file-size"></div>
                <div class="file-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary"
                            data-action="replace" data-slot="${slotIndex}">
                        <i class="bi bi-arrow-repeat"></i> 替换
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            data-action="remove" data-slot="${slotIndex}">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        `;

        return uploadItem;
    }

    // 设置文档上传事件监听器
    function setupDocumentUploadEvents(subtypeId) {
        console.log('设置文档上传事件监听器:', subtypeId);

        const container = document.getElementById('documentUploadGrid');
        if (!container) return;

        // 检查是否已经设置过事件监听器，避免重复绑定
        if (container.hasAttribute('data-events-setup')) {
            console.log('Events already setup, skipping');
            return;
        }

        // 使用事件委托，统一处理点击事件
        container.addEventListener('click', function(event) {
            console.log('🎯 Click event triggered on documentUploadGrid');

            const target = event.target;
            const uploadContent = target.closest('[data-slot-click]');
            const actionButton = target.closest('[data-action]');
            const fileInput = target.closest('input[type="file"]');
            const button = target.closest('button');

            // 如果点击的是文件输入框，不处理
            if (fileInput) {
                console.log('File input clicked, ignoring');
                return;
            }

            // 如果点击的是按钮（但不是替换/删除按钮），不处理
            if (button && !actionButton) {
                console.log('Button clicked (not action button), ignoring:', button.id);
                return;
            }

            if (uploadContent && !actionButton) {
                const slotIndex = parseInt(uploadContent.dataset.slotClick);
                if (!isNaN(slotIndex)) {
                    // 阻止事件冒泡和默认行为
                    event.stopPropagation();
                    event.preventDefault();

                    // 防止重复点击
                    if (window.fileSlotClickInProgress) {
                        console.log('⚠️ File slot click already in progress, ignoring');
                        return;
                    }

                    // 检查是否是真正的用户点击（不是程序触发）
                    if (event.isTrusted === false) {
                        console.log('⚠️ Not a trusted user event, ignoring');
                        return;
                    }

                    window.fileSlotClickInProgress = true;
                    window.userClickedFileSlot = true;

                    selectDocumentFileSlot(slotIndex, subtypeId);

                    // 重置进行中标志
                    setTimeout(() => {
                        window.fileSlotClickInProgress = false;
                    }, 500);
                }
            } else if (actionButton) {
                const action = actionButton.dataset.action;
                const slotIndex = parseInt(actionButton.dataset.slot);

                if (action === 'replace' && !isNaN(slotIndex)) {
                    // 阻止事件冒泡和默认行为
                    event.stopPropagation();
                    event.preventDefault();

                    // 防止重复点击和事件信任检查
                    if (window.fileSlotClickInProgress || event.isTrusted === false) {
                        return;
                    }

                    window.fileSlotClickInProgress = true;
                    window.userClickedFileSlot = true;

                    selectDocumentFileSlot(slotIndex, subtypeId);

                    setTimeout(() => {
                        window.fileSlotClickInProgress = false;
                    }, 500);
                } else if (action === 'remove' && !isNaN(slotIndex)) {
                    event.stopPropagation();
                    event.preventDefault();
                    removeDocumentFileSlot(slotIndex);
                }
            }
        });

        // 为每个文件输入框设置change事件监听器
        const subtype = accountOpeningSubTypes[subtypeId];
        for (let i = 0; i < subtype.fileCount; i++) {
            const fileInput = container.querySelector(`#fileInput_${i}`);
            if (fileInput) {
                const handleFileChange = function(event) {
                    console.log('File input change event for slot:', i);
                    handleDocumentFileSlotSelect(event, i, subtypeId);
                };
                fileInput.addEventListener('change', handleFileChange);
            }
        }

        // 标记事件已设置
        container.setAttribute('data-events-setup', 'true');
        console.log('✅ Document upload events setup completed');
    }

    // 选择文档文件槽位
    function selectDocumentFileSlot(slotIndex, subtypeId) {
        console.log('选择文档文件槽位:', slotIndex, subtypeId);

        const fileInput = document.getElementById(`fileInput_${slotIndex}`);
        if (fileInput) {
            fileInput.click();
        }
    }

    // 处理文档文件槽位选择
    function handleDocumentFileSlotSelect(event, slotIndex, subtypeId) {
        console.log('处理文档文件槽位选择:', slotIndex, subtypeId);

        const files = event.target.files;
        if (files && files.length > 0) {
            const file = files[0];

            // 验证文件
            if (validateFile(file)) {
                stageFileToSlot(file, slotIndex);
                updateStagedFilesDisplay();
            }
        }
    }

    // 验证文件
    function validateFile(file) {
        const validTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff'];
        const maxSize = 200 * 1024 * 1024; // 200MB

        if (!validTypes.includes(file.type) && !file.name.match(/\.(pdf|png|jpg|jpeg|gif|bmp|tiff)$/i)) {
            showMessage(`文件 ${file.name} 格式不支持`, 'warning');
            return false;
        }

        if (file.size > maxSize) {
            showMessage(`文件 ${file.name} 超过200MB限制`, 'warning');
            return false;
        }

        return true;
    }

    // 文件暂存到指定槽位
    function stageFileToSlot(file, slotIndex) {
        console.log('文件暂存到槽位:', slotIndex, file.name);

        // 存储文件到暂存槽位
        accountOpeningStagedFiles[slotIndex] = {
            file: file,
            name: file.name,
            size: file.size,
            slotIndex: slotIndex,
            status: 'staged'
        };

        // 更新UI状态为已暂存
        updateSlotUploadStatus(slotIndex, file, 'staged');
    }

    // 更新槽位上传状态
    function updateSlotUploadStatus(slotIndex, file, status) {
        const uploadItem = document.querySelector(`[data-slot-click="${slotIndex}"]`);
        if (!uploadItem) return;

        const uploadIcon = uploadItem.querySelector('.upload-icon i');
        const uploadStatus = uploadItem.querySelector('.upload-status');
        const fileInfo = uploadItem.querySelector('.file-info');
        const fileName = uploadItem.querySelector('.file-name');
        const fileSize = uploadItem.querySelector('.file-size');

        // 移除所有状态类
        uploadItem.classList.remove('has-file', 'staged', 'uploading', 'uploaded', 'analyzed');

        if (status === 'staged') {
            uploadItem.classList.add('staged');
            uploadIcon.className = 'bi bi-file-earmark-check';
            uploadStatus.textContent = '已暂存';

            if (fileName) fileName.textContent = file.name;
            if (fileSize) fileSize.textContent = formatFileSize(file.size);
            if (fileInfo) fileInfo.style.display = 'block';
        } else if (status === 'uploading') {
            uploadItem.classList.add('uploading');
            uploadIcon.className = 'bi bi-hourglass-split';
            uploadStatus.textContent = '上传中...';
        } else if (status === 'uploaded') {
            uploadItem.classList.add('uploaded');
            uploadIcon.className = 'bi bi-check-circle';
            uploadStatus.textContent = '已上传';
        } else if (status === 'analyzed') {
            uploadItem.classList.add('analyzed');
            uploadIcon.className = 'bi bi-check-circle-fill';
            uploadStatus.textContent = '已分析';
        }
    }

    // 删除文档文件槽位
    function removeDocumentFileSlot(slotIndex) {
        console.log('删除文档文件槽位:', slotIndex);

        // 从暂存中删除
        delete accountOpeningStagedFiles[slotIndex];

        // 重置UI状态
        const uploadItem = document.querySelector(`[data-slot-click="${slotIndex}"]`);
        if (uploadItem) {
            uploadItem.classList.remove('has-file', 'staged', 'uploading', 'uploaded');

            const uploadIcon = uploadItem.querySelector('.upload-icon i');
            const uploadStatus = uploadItem.querySelector('.upload-status');
            const fileInfo = uploadItem.querySelector('.file-info');

            if (uploadIcon) uploadIcon.className = 'bi bi-cloud-upload';
            if (uploadStatus) uploadStatus.textContent = '点击选择文件';
            if (fileInfo) fileInfo.style.display = 'none';
        }

        // 清空文件输入框
        const fileInput = document.getElementById(`fileInput_${slotIndex}`);
        if (fileInput) {
            fileInput.value = '';
        }

        // 更新暂存文件显示
        updateStagedFilesDisplay();
    }

    // 更新暂存文件显示
    function updateStagedFilesDisplay() {
        const stagedFiles = Object.values(accountOpeningStagedFiles).filter(file => file && file.status === 'staged');
        const completeUploadBtn = document.getElementById('completeUploadBtn');

        if (stagedFiles.length > 0) {
            if (completeUploadBtn) {
                completeUploadBtn.style.display = 'inline-block';
            }
        } else {
            if (completeUploadBtn) {
                completeUploadBtn.style.display = 'none';
            }
        }
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 生成拼接的记录名称
    function generateRecordName() {
        const now = new Date();
        const dateStr = now.getFullYear().toString() +
                       (now.getMonth() + 1).toString().padStart(2, '0') +
                       now.getDate().toString().padStart(2, '0');
        const timeStr = now.getHours().toString().padStart(2, '0') +
                       now.getMinutes().toString().padStart(2, '0');

        // 获取所有暂存文件的文件名（去除扩展名）
        const subtype = accountOpeningSubTypes[currentAccountOpeningSubtype];
        const fileNames = [];

        for (let i = 0; i < subtype.fileCount; i++) {
            const stagedFile = accountOpeningStagedFiles[i];
            if (stagedFile) {
                const nameWithoutExt = stagedFile.name.replace(/\.[^/.]+$/, '');
                fileNames.push(nameWithoutExt);
            } else {
                fileNames.push('空文件');
            }
        }

        // 拼接格式：日期_时间_第一个文件名_第二个文件名_第三个文件名
        return `${dateStr}_${timeStr}_${fileNames.join('_')}`;
    }

    // 处理"上传完毕"按钮点击
    function handleCompleteUpload() {
        console.log('处理上传完毕');

        const stagedFiles = Object.values(accountOpeningStagedFiles).filter(file => file && file.status === 'staged');

        if (stagedFiles.length === 0) {
            showMessage('请先选择文件', 'warning');
            return;
        }

        // 生成记录名称
        accountOpeningRecordName = generateRecordName();

        // 显示上传进度
        showMessage('正在上传文件...', 'info');

        // 批量上传所有暂存文件
        uploadAllStagedFiles();
    }

    // 批量上传所有暂存文件
    function uploadAllStagedFiles() {
        console.log('批量上传所有暂存文件');

        const stagedFiles = Object.values(accountOpeningStagedFiles).filter(file => file && file.status === 'staged');
        let totalCount = stagedFiles.length;
        accountOpeningFilePaths = [];

        const uploadPromises = stagedFiles.map((fileData, index) => {
            return uploadSingleAccountOpeningFile(fileData, index, totalCount);
        });

        Promise.all(uploadPromises)
            .then(results => {
                const successCount = results.filter(result => result.success).length;
                if (successCount === totalCount) {
                    showMessage(`所有文件上传成功，记录名称：${accountOpeningRecordName}`, 'success');
                    showAnalysisButton();

                    // 重置暂存进行中标志，允许文件列表刷新
                    window.accountOpeningStagingInProgress = false;

                    // 上传完成后，添加到文件列表显示
                    setTimeout(() => {
                        addAccountOpeningFilesToList();
                        updateFileCountBadges();
                        displayCurrentFiles();
                    }, 500);
                } else {
                    showMessage(`${successCount}/${totalCount} 个文件上传成功`, 'warning');

                    // 重置暂存进行中标志
                    window.accountOpeningStagingInProgress = false;

                    // 部分成功也要更新文件列表
                    setTimeout(() => {
                        addAccountOpeningFilesToList();
                        updateFileCountBadges();
                        displayCurrentFiles();
                    }, 500);
                }
            })
            .catch(error => {
                console.error('批量上传失败:', error);
                showMessage('文件上传失败，请重试', 'error');

                // 重置暂存进行中标志
                window.accountOpeningStagingInProgress = false;
            });
    }

    // 上传单个账户开户文件
    function uploadSingleAccountOpeningFile(fileData, index, totalCount) {
        return new Promise((resolve) => {
            const formData = new FormData();
            formData.append('file', fileData.file);
            formData.append('type', 'account_opening');

            // 更新UI状态为上传中
            updateSlotUploadStatus(fileData.slotIndex, fileData.file, 'uploading');

            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新文件状态为已上传
                    accountOpeningStagedFiles[fileData.slotIndex].status = 'uploaded';
                    accountOpeningStagedFiles[fileData.slotIndex].filePath = data.data.filepath;
                    accountOpeningStagedFiles[fileData.slotIndex].recordId = data.data.id;
                    accountOpeningFilePaths.push(data.data.id); // 存储记录ID用于后续分析

                    // 更新UI状态为已上传
                    updateSlotUploadStatus(fileData.slotIndex, fileData.file, 'uploaded');

                    resolve({ success: true, filePath: data.data.filepath, recordId: data.data.id });
                } else {
                    console.error('文件上传失败:', data.message);
                    showMessage(`文件 ${fileData.name} 上传失败: ${data.message}`, 'error');
                    resolve({ success: false, error: data.message });
                }
            })
            .catch(error => {
                console.error('文件上传错误:', error);
                showMessage(`文件 ${fileData.name} 上传失败`, 'error');
                resolve({ success: false, error: error.message });
            });
        });
    }

    // 将账户开户文件添加到文件列表
    function addAccountOpeningFilesToList() {
        console.log('Adding account opening files to list');

        const uploadedFiles = Object.values(accountOpeningStagedFiles).filter(file =>
            file && file.status === 'uploaded'
        );

        if (uploadedFiles.length === 0) {
            console.log('No uploaded files to add');
            return;
        }

        // 创建一个合并记录，包含所有上传的文件信息
        const fileIds = uploadedFiles.map(file => file.recordId);
        const fileNames = uploadedFiles.map(file => file.name);

        const mergedRecord = {
            id: fileIds[0], // 使用第一个文件的ID作为主记录ID
            fileId: fileIds[0],
            recordId: fileIds[0],
            name: accountOpeningRecordName, // 使用拼接的记录名称
            originalName: accountOpeningRecordName,
            type: 'account_opening',
            status: 'pending', // 上传后状态为待分析
            upload_time: new Date().toISOString(),
            created_at: new Date().toISOString(),
            file_size: uploadedFiles.reduce((total, file) => total + (file.size || 0), 0),
            selected: false,
            // 账户开户场景特有字段
            file_count: uploadedFiles.length,
            file_ids: fileIds, // 存储所有文件ID，用于批量分析
            file_names: fileNames, // 存储所有文件名
            is_merged_record: true // 标记为合并记录
        };

        // 添加到当前分析类型的文件列表
        if (!filesByType['account_opening']) {
            filesByType['account_opening'] = [];
        }

        // 添加到文件记录列表
        if (!fileRecordsByType['account_opening']) {
            fileRecordsByType['account_opening'] = [];
        }

        // 检查是否已经存在相同记录名称的文件
        const existingIndex = filesByType['account_opening'].findIndex(f => f.name === accountOpeningRecordName);
        if (existingIndex >= 0) {
            // 更新现有记录
            filesByType['account_opening'][existingIndex] = mergedRecord;
        } else {
            // 添加新记录
            filesByType['account_opening'].push(mergedRecord);
        }

        // 同样更新fileRecordsByType
        const existingRecordIndex = fileRecordsByType['account_opening'].findIndex(f => f.name === accountOpeningRecordName);
        if (existingRecordIndex >= 0) {
            fileRecordsByType['account_opening'][existingRecordIndex] = mergedRecord;
        } else {
            fileRecordsByType['account_opening'].push(mergedRecord);
        }

        console.log('Account opening merged record added to list successfully');
        console.log('Merged record:', mergedRecord);
    }

    // 调试函数：检查当前文件列表状态
    window.debugFileList = function() {
        console.log('=== 文件列表调试信息 ===');
        console.log('📊 filesByType:', filesByType);
        console.log('📋 fileRecordsByType:', fileRecordsByType);
        console.log('🔍 selectedAnalysisType:', selectedAnalysisType);

        if (filesByType['account_opening']) {
            console.log('📁 账户开户文件列表:');
            filesByType['account_opening'].forEach((file, index) => {
                console.log(`  ${index + 1}. ID: ${file.id}, 名称: ${file.name}, 合并记录: ${file.is_merged_record}, 状态: ${file.status}`);
            });
        } else {
            console.log('❌ 没有账户开户文件');
        }
        console.log('========================');
    }

    // 测试函数：手动触发合并分析
    window.testMergedAnalysis = function() {
        console.log('🧪 开始测试合并分析');

        // 查找第一个合并记录
        const allFiles = filesByType['account_opening'] || [];
        const mergedRecord = allFiles.find(f => f.is_merged_record);

        if (mergedRecord) {
            console.log('✅ 找到合并记录，开始分析:', mergedRecord);
            analyzeMergedRecord(mergedRecord.id);
        } else {
            console.log('❌ 没有找到合并记录');
            console.log('📁 当前文件列表:', allFiles);
        }
    }

    // 简单的API测试函数
    window.testAPI = function() {
        console.log('🧪 测试API连接');
        fetch('/api/account-opening/analyze-merged', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                main_record_id: 621,
                file_ids: [620, 621],
                record_name: 'test'
            })
        })
        .then(response => {
            console.log('🔍 API响应状态:', response.status);
            console.log('🔍 API响应头:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('📊 API响应数据:', data);
        })
        .catch(error => {
            console.error('❌ API请求失败:', error);
        });
    }

    // 测试功能修复的函数
    window.testFixes = function() {
        console.log('🔧 测试功能修复');

        // 1. 测试时间格式化
        const testDate = new Date();
        const formattedTime = formatDateTime(testDate);
        console.log('✅ 时间格式化测试:', formattedTime);

        // 2. 测试准确率处理
        const testAccuracy1 = 0.95; // 小数形式
        const testAccuracy2 = 95.5; // 百分比形式
        console.log('✅ 准确率处理测试 (小数):', testAccuracy1 > 1 ? testAccuracy1.toFixed(1) : (testAccuracy1 * 100).toFixed(1));
        console.log('✅ 准确率处理测试 (百分比):', testAccuracy2 > 1 ? testAccuracy2.toFixed(1) : (testAccuracy2 * 100).toFixed(1));

        // 3. 测试字段计数功能
        const testAiResult = {
            "manager_info": {"name": "测试机构", "address": "测试地址", "contact": "测试联系方式"},
            "investor_info": {"name": "测试投资者", "type": "个人", "account_nature": "普通"},
            "contact_info": [{"contact_person": "张三", "phone": "***********"}],
            "seal_integrity": "complete",
            "page_continuity": "continuous"
        };
        const fieldCount = countFieldsInObject(testAiResult);
        console.log('✅ 字段计数测试:', fieldCount, '个字段');

        // 4. 测试统计信息更新
        const mockResultData = {
            analysis_type: 'account_opening',
            ai_result: testAiResult,
            expected_result: null, // 模拟没有预期结果的情况
            accuracy_score: null
        };
        console.log('✅ 测试统计信息更新（无预期结果）');
        updateAnalysisStatsSection(mockResultData);

        // 5. 测试文件列表显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
            console.log('✅ 文件列表刷新完成');
        }

        console.log('🎉 所有测试完成');
    }

    // 显示分析按钮
    function showAnalysisButton() {
        const analysisSection = document.getElementById('analysisSection');
        if (analysisSection) {
            analysisSection.style.display = 'block';
        }

        // 隐藏上传完毕按钮
        const completeUploadBtn = document.getElementById('completeUploadBtn');
        if (completeUploadBtn) {
            completeUploadBtn.style.display = 'none';
        }
    }

    // 分析合并记录（账户开户场景）
    window.analyzeMergedRecord = function(recordId) {
        console.log('🔍 analyzeMergedRecord 函数被调用，recordId:', recordId, 'type:', typeof recordId);
        console.log('📊 当前 filesByType:', filesByType);

        // 确保recordId是数字类型
        const numericRecordId = parseInt(recordId);
        console.log('🔢 转换后的recordId:', numericRecordId);

        // 查找合并记录
        const allFiles = filesByType['account_opening'] || [];
        console.log('📁 account_opening 文件列表:', allFiles);

        // 先尝试按ID和is_merged_record查找
        let mergedRecord = allFiles.find(f => f.id === numericRecordId && f.is_merged_record);

        // 如果没找到，尝试只按ID查找（可能是合并记录但标记丢失）
        if (!mergedRecord) {
            mergedRecord = allFiles.find(f => f.id === numericRecordId);
            console.log('🔍 按ID查找到的记录:', mergedRecord);

            // 检查是否有file_ids字段（合并记录的特征）
            if (mergedRecord && mergedRecord.file_ids && mergedRecord.file_ids.length > 1) {
                console.log('✅ 发现合并记录特征，继续处理');
            } else {
                console.error('❌ 记录不是合并记录或缺少file_ids');
                showMessage('该记录不是合并记录', 'error');
                return;
            }
        } else {
            console.log('🔍 查找到的合并记录:', mergedRecord);
        }

        if (!mergedRecord) {
            console.error('❌ 未找到记录');
            showMessage('未找到记录', 'error');
            return;
        }

        if (!mergedRecord.file_ids || mergedRecord.file_ids.length === 0) {
            console.error('❌ 合并记录中没有文件ID');
            showMessage('合并记录中没有文件ID', 'error');
            return;
        }

        console.log('📁 合并记录文件ID列表:', mergedRecord.file_ids);
        console.log('📄 合并记录文件名列表:', mergedRecord.file_names);

        // 更新UI状态为分析中 - 参考其他分析函数的实现
        const fileRow = document.querySelector(`tr[data-file-id="${numericRecordId}"]`);
        let statusCell = null;
        let analyzeBtn = null;

        if (fileRow) {
            statusCell = fileRow.querySelector('.status-badge');
            if (statusCell) {
                statusCell.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中';
                statusCell.className = 'status-badge status-processing';
            }

            // 禁用分析按钮
            analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
            if (analyzeBtn) {
                analyzeBtn.disabled = true;
                analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
            }
        }

        // 更新本地记录状态
        mergedRecord.status = 'processing';

        // 显示分析进度
        showMessage('正在开始账户开户合并分析...', 'info');

        // 使用专门的账户开户合并分析API
        fetch('/api/account-opening/analyze-merged', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                main_record_id: numericRecordId,
                file_ids: mergedRecord.file_ids,
                record_name: mergedRecord.name
            })
        })
        .then(response => {
            console.log('🔍 收到响应:', response);
            return response.json();
        })
        .then(data => {
            console.log('📊 解析的响应数据:', data);
            if (data.success) {
                console.log('✅ 账户开户合并分析完成');
                showMessage('分析完成！', 'success');

                // 更新合并记录状态和数据
                mergedRecord.status = 'pending_audit';

                // 同步准确率和其他数据
                if (data.data) {
                    if (data.data.accuracy_score !== undefined) {
                        mergedRecord.accuracy_score = data.data.accuracy_score;
                    }
                    if (data.data.created_at) {
                        mergedRecord.created_at = data.data.created_at;
                    }
                    if (data.data.id) {
                        mergedRecord.recordId = data.data.id;
                        mergedRecord.fileId = data.data.id;
                    }
                }

                // 恢复UI状态 - 参考其他分析函数的实现
                if (statusCell) {
                    statusCell.innerHTML = '<i class="bi bi-eye"></i> 待审核';
                    statusCell.className = 'status-badge status-pending-audit';
                }

                // 恢复按钮状态
                if (analyzeBtn) {
                    analyzeBtn.disabled = false;
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 重分析';
                    analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                }

                // 刷新文件列表显示
                setTimeout(() => {
                    displayCurrentFiles();
                    checkAnalyzeButton();
                }, 500);

                // 延迟同步服务器数据以确保准确率正确显示
                if (data.data && data.data.id) {
                    setTimeout(() => {
                        syncFileDataFromServer([data.data.id]);
                    }, 1000);
                }

            } else {
                console.error('❌ 分析失败:', data.message);
                showMessage(`分析失败: ${data.message}`, 'error');

                // 恢复UI状态为失败
                if (statusCell) {
                    statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                    statusCell.className = 'status-badge status-failed';
                }

                // 恢复按钮状态
                if (analyzeBtn) {
                    analyzeBtn.disabled = false;
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                    analyzeBtn.className = 'btn btn-analyze btn-sm';
                }

                // 更新本地记录状态
                mergedRecord.status = 'failed';
            }
        })
        .catch(error => {
            console.error('❌ 分析请求失败:', error);
            showMessage('分析请求失败，请重试', 'error');

            // 恢复UI状态为失败
            if (statusCell) {
                statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                statusCell.className = 'status-badge status-failed';
            }

            // 恢复按钮状态
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                analyzeBtn.className = 'btn btn-analyze btn-sm';
            }

            // 更新本地记录状态
            mergedRecord.status = 'failed';
        });
    }

    // 开始账户开户分析
    function startAccountOpeningAnalysis() {
        console.log('🔍 开始账户开户分析');

        const uploadedFiles = Object.values(accountOpeningStagedFiles).filter(file =>
            file && file.status === 'uploaded'
        );

        if (uploadedFiles.length === 0) {
            console.error('❌ 没有可分析的文件');
            showMessage('没有可分析的文件', 'warning');
            return;
        }

        console.log('📁 上传的文件列表:', uploadedFiles);

        // 显示分析进度
        showMessage('正在开始账户开户合并分析...', 'info');

        // 使用专门的账户开户合并分析API
        const fileIds = uploadedFiles.map(file => file.recordId);
        const mainRecordId = fileIds[0]; // 使用第一个文件ID作为主记录ID

        console.log('📋 主记录ID:', mainRecordId);
        console.log('📄 文件ID列表:', fileIds);

        fetch('/api/account-opening/analyze-merged', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                main_record_id: mainRecordId,
                file_ids: fileIds,
                record_name: accountOpeningRecordName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ 账户开户合并分析完成');
                showMessage('分析完成！', 'success');

                // 更新暂存文件状态
                uploadedFiles.forEach(fileData => {
                    accountOpeningStagedFiles[fileData.slotIndex].status = 'analyzed';
                });

                // 更新合并记录的状态
                const fileRecords = filesByType['account_opening'] || [];
                const mergedRecord = fileRecords.find(f => f.name === accountOpeningRecordName);
                if (mergedRecord) {
                    mergedRecord.status = 'pending_audit'; // 分析完成后状态为待审核
                }

                const recordList = fileRecordsByType['account_opening'] || [];
                const mergedRecordInList = recordList.find(f => f.name === accountOpeningRecordName);
                if (mergedRecordInList) {
                    mergedRecordInList.status = 'pending_audit';
                }

                // 隐藏分析按钮
                const analysisSection = document.getElementById('analysisSection');
                if (analysisSection) {
                    analysisSection.style.display = 'none';
                }

                // 刷新文件列表显示
                setTimeout(() => {
                    displayCurrentFiles();
                    checkAnalyzeButton();
                }, 500);

            } else {
                console.error('❌ 分析失败:', data.message);
                showMessage(`分析失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            console.error('❌ 分析请求失败:', error);
            showMessage('分析请求失败，请重试', 'error');
        });
    }

    // 页面可见性变化监听，用于自动刷新状态
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            // 账户开户暂存进行中时跳过刷新
            if (window.accountOpeningStagingInProgress) {
                console.log('账户开户暂存进行中，跳过文件列表刷新');
                return;
            }

            // 页面变为可见时，刷新文件状态
            console.log('页面变为可见，刷新文件状态');
            setTimeout(() => {
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                } else if (currentFileView === 'history') {
                    loadHistoryFiles();
                }
            }, 500);
        }
    });

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializePage();
        loadAnalysisTypes();
        setupEventListeners();

        // 从URL参数中获取预选的分析类型
        const urlParams = new URLSearchParams(window.location.search);
        const typeFromUrl = urlParams.get('type');

        // 如果URL中有分析类型参数，自动选择该类型
        if (typeFromUrl) {
            setTimeout(() => {
                const typeCard = document.querySelector(`.analysis-type-card[data-type="${typeFromUrl}"]`);
                if (typeCard) {
                    selectAnalysisType(typeFromUrl, typeCard);
                }
            }, 300); // 延迟一点时间确保页面加载完成
        }
    });

    // 初始化页面
    function initializePage() {
        // 添加页面加载动画
        document.body.classList.add('fade-in');

        // 初始化统一文件管理
        initializeUnifiedFileManager();
    }

    // ==================== 统一文件管理功能 ====================

    // 初始化统一文件管理器
    function initializeUnifiedFileManager() {
        // 初始化各分析类型的文件存储
        initializeFileStorageByType();

        // 恢复每页显示条数设置
        restoreDocAnalysisPerPageSetting();

        // 显示统一文件列表容器
        const container = document.querySelector('.unified-file-list-container');
        if (container) {
            container.style.display = 'block';
        }

        // 初始化分页控件状态
        hidePagination();

        // 默认显示当前上传视图
        setTimeout(() => {
            switchFileView('current');
        }, 0);

        // 初始化分析按钮状态
        checkAnalyzeButton();

        // 异步获取历史记录总数以更新计数
        getHistoryFilesTotalCount();
    }

    // 初始化各分析类型的文件存储
    function initializeFileStorageByType() {
        const analysisTypes = [
            'futures_account',
            'wealth_management',
            'broker_interest',
            'account_opening',
            'ningyin_fee',
            'non_standard_trade'
        ];

        analysisTypes.forEach(type => {
            if (!filesByType[type]) {
                filesByType[type] = [];
            }
            if (!fileRecordsByType[type]) {
                fileRecordsByType[type] = [];
            }
        });
    }

    // 切换文件视图（当前上传/历史记录）
    function switchFileView(view) {
        currentFileView = view;

        // 更新标签页状态
        const currentTab = document.getElementById('currentFilesTab');
        const historyTab = document.getElementById('historyFilesTab');
        const currentList = document.getElementById('currentFilesList');
        const historyList = document.getElementById('historyFilesList');

        if (view === 'current') {
            currentTab.classList.add('active');
            historyTab.classList.remove('active');
            currentList.style.display = 'block';
            historyList.style.display = 'none';

            // 显示当前上传的文件
            displayCurrentFiles();
            // 当前文件视图不需要分页
            hidePagination();
        } else {
            currentTab.classList.remove('active');
            historyTab.classList.add('active');
            currentList.style.display = 'none';
            historyList.style.display = 'block';

            // 加载并显示历史记录
            loadHistoryFiles().then(() => {
                // 显示分页
                showPagination();
            }).catch(error => {
                // 历史记录加载失败时的处理
            });
        }

        // 更新筛选器可见性
        updateFiltersVisibility();

        // 更新文件计数徽章
        updateFileCountBadges();
    }

    // 更新筛选器可见性
    function updateFiltersVisibility() {
        const filtersContainer = document.getElementById('fileFiltersContainer');
        if (filtersContainer) {
            // 两个视图都显示筛选器，提供更好的用户体验
            filtersContainer.style.display = 'block';
        }
    }

    // 更新文件计数徽章
    function updateFileCountBadges() {
        const totalBadge = document.getElementById('totalFileCountBadge');
        const currentBadge = document.getElementById('currentFileCount');
        const historyBadge = document.getElementById('historyFileCount');

        // 计算当前上传文件数量（根据筛选条件）
        let currentCount = 0;
        let allFiles = [];

        if (fileFilters.type) {
            // 如果有类型筛选，只计算该类型的文件
            allFiles = filesByType[fileFilters.type] || [];
        } else {
            // 如果没有类型筛选，计算所有类型的文件
            allFiles = Object.values(filesByType).flat();
        }

        // 根据文件状态筛选计算数量
        if (fileFilters.activeStatus && fileFilters.activeStatus !== 'all') {
            currentCount = allFiles.filter(file => {
                const fileStatus = file.file_status || 'active';
                return fileStatus === fileFilters.activeStatus;
            }).length;
        } else {
            currentCount = allFiles.length;
        }

        // 历史记录数量使用分页信息中的总数
        const historyCount = filePagination.totalItems || 0;

        // 总数根据当前视图显示对应的数量
        const totalCount = currentFileView === 'current' ? currentCount : historyCount;

        if (totalBadge) totalBadge.textContent = totalCount;
        if (currentBadge) currentBadge.textContent = currentCount;
        if (historyBadge) historyBadge.textContent = historyCount;
    }

    // 获取历史记录总数（用于初始化时更新计数）
    function getHistoryFilesTotalCount() {
        // 构建查询参数，只获取第一页来获取总数信息
        const params = {
            page: 1,
            per_page: 1  // 只需要获取总数，不需要实际数据
        };

        // 根据当前筛选条件添加参数
        if (fileFilters.type && fileFilters.type !== '') {
            params.analysis_type = fileFilters.type;
        }
        if (fileFilters.status) {
            params.status = fileFilters.status;
        }
        if (fileFilters.search) {
            params.search = fileFilters.search;
        }

        // 调用API获取总数
        API.get('/api/records', params)
            .then(response => {
                if (response && response.success && response.data) {
                    filePagination.totalItems = response.data.pagination?.total || 0;
                    filePagination.totalPages = response.data.pagination?.pages || 0;

                    // 更新文件计数徽章
                    updateFileCountBadges();
                }
            })
            .catch(error => {
                console.error('获取历史记录总数失败:', error);
                // 失败时设置默认值
                filePagination.totalItems = 0;
                filePagination.totalPages = 0;
                updateFileCountBadges();
            });
    }

    // 显示当前上传的文件
    function displayCurrentFiles() {
        // 账户开户暂存进行中时跳过文件列表显示
        if (window.accountOpeningStagingInProgress) {
            console.log('账户开户暂存进行中，跳过文件列表显示');
            return;
        }

        const tbody = document.getElementById('currentFilesTableBody');
        if (!tbody) return;

        // 获取文件数据 - 优先使用selectedAnalysisType，然后是fileFilters.type
        let allFiles = [];
        const displayType = selectedAnalysisType || fileFilters.type;

        if (displayType) {
            // 如果有选中的分析类型或类型筛选，只显示该类型的文件
            allFiles = filesByType[displayType] || [];
        } else {
            // 如果没有类型筛选（全部类型），显示所有类型的文件
            allFiles = Object.values(filesByType).flat();
        }

        // 调试：输出当前文件数据
        console.log('🔍 displayCurrentFiles 调试信息:');
        console.log('  - selectedAnalysisType:', selectedAnalysisType);
        console.log('  - fileFilters:', fileFilters);
        console.log('  - displayType:', displayType);
        console.log('  - filesByType:', filesByType);
        console.log('  - 当前类型文件数量:', allFiles.length);
        console.log('  - 当前上传文件数据:', allFiles);

        // 筛选文件
        filteredFiles = allFiles.filter(file => {
            let matches = true;

            // 按状态筛选
            if (fileFilters.status && file.status !== fileFilters.status) {
                matches = false;
            }

            // 按文件状态筛选（正常/废弃）
            if (fileFilters.activeStatus && fileFilters.activeStatus !== 'all') {
                const fileStatus = file.file_status || 'active'; // 默认为正常文件

                if (fileFilters.activeStatus === 'active' && fileStatus !== 'active') {
                    matches = false;
                } else if (fileFilters.activeStatus === 'deprecated' && fileStatus !== 'deprecated') {
                    matches = false;
                }
            }

            // 按搜索关键词筛选
            if (fileFilters.search && !file.name.toLowerCase().includes(fileFilters.search.toLowerCase())) {
                matches = false;
            }

            return matches;
        });

        if (filteredFiles.length === 0) {
            let emptyMessage, emptyHint;

            if (fileFilters.type) {
                // 有类型筛选
                emptyMessage = `暂无 ${getAnalysisTypeName(fileFilters.type)} 类型的文件`;
                emptyHint = '请上传对应类型的文件或更改筛选条件';
            } else if (Object.keys(filesByType).length === 0) {
                // 没有任何文件
                emptyMessage = '暂无上传文件';
                emptyHint = '请先选择分析类型并上传文件';
            } else {
                // 有文件但被筛选条件过滤掉了
                emptyMessage = '没有符合筛选条件的文件';
                emptyHint = '请调整筛选条件或上传新文件';
            }

            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <div class="mt-3 text-muted">${emptyMessage}</div>
                        <div class="text-muted">${emptyHint}</div>
                    </td>
                </tr>
            `;

            // 更新全选复选框状态
            updateSelectAllCheckbox();
            // 隐藏分页控件
            hidePagination();
            return;
        }

        tbody.innerHTML = '';
        filteredFiles.forEach(file => {
            const row = createFileItemHTML(file, 'current');
            tbody.appendChild(row);
        });

        // 更新全选复选框状态
        updateSelectAllCheckbox();
        // 隐藏分页控件（当前文件不需要分页）
        hidePagination();
    }

    // 获取当前选择类型的文件
    function getCurrentTypeFiles() {
        if (!selectedAnalysisType || !filesByType[selectedAnalysisType]) {
            return [];
        }
        return filesByType[selectedAnalysisType];
    }

    // 批量分析完成后的状态刷新
    function refreshAfterBatchAnalysis(recordIds) {
        console.log('批量分析完成，开始刷新状态，recordIds:', recordIds);

        // 立即刷新一次
        if (currentFileView === 'current') {
            displayCurrentFiles();
        } else if (currentFileView === 'history') {
            loadHistoryFiles();
        }

        // 如果有recordIds，同步服务器数据
        if (recordIds && recordIds.length > 0) {
            setTimeout(() => {
                syncFileDataFromServer(recordIds);
            }, 1000);
        }

        // 多次延迟刷新，确保所有状态都已更新
        const refreshIntervals = [2000, 4000, 6000]; // 2秒、4秒、6秒后分别刷新
        refreshIntervals.forEach(delay => {
            setTimeout(() => {
                console.log(`延迟${delay}ms刷新，确保状态完全同步`);
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                } else if (currentFileView === 'history') {
                    loadHistoryFiles();
                }
            }, delay);
        });
    }

    // 从服务器同步文件数据（包括准确率）
    function syncFileDataFromServer(recordIds) {
        if (!recordIds || recordIds.length === 0) return;

        console.log('开始同步文件数据，recordIds:', recordIds);

        // 为每个recordId获取最新数据
        const syncPromises = recordIds.map(recordId => {
            return fetch(`/api/debug/file/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const serverFile = data.data;
                        console.log(`文件 ${recordId} 的服务器数据:`, serverFile);
                        console.log(`服务器返回的完整数据结构:`, JSON.stringify(serverFile, null, 2));

                        // 更新本地文件数据 - 使用fileId而不是recordId进行匹配
                        const allCurrentFiles = getCurrentTypeFiles();
                        console.log(`查找文件 ${recordId}，当前文件列表:`, allCurrentFiles.map(f => ({fileId: f.fileId, recordId: f.recordId, name: f.name})));

                        const localFile = allCurrentFiles.find(f => f.fileId == recordId || f.recordId == recordId);
                        if (localFile) {
                            console.log(`找到本地文件 ${recordId}，更新前准确率:`, localFile.accuracy_score);
                            console.log(`服务器返回的准确率字段:`, {
                                accuracy_score_float: serverFile.accuracy_score_float,
                                accuracy_score_raw: serverFile.accuracy_score_raw,
                                accuracy_score_type: serverFile.accuracy_score_type
                            });

                            // 优先使用 accuracy_score_float，如果不存在则尝试其他字段
                            let accuracyValue = serverFile.accuracy_score_float;
                            if (accuracyValue === undefined || accuracyValue === null) {
                                accuracyValue = serverFile.accuracy_score;
                            }

                            localFile.accuracy_score = accuracyValue;
                            localFile.status = serverFile.status;
                            console.log(`更新后准确率:`, localFile.accuracy_score);
                        } else {
                            console.warn(`未找到本地文件 ${recordId}`);
                        }

                        // 更新fileRecords中的数据
                        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                        const fileRecord = fileRecords.find(r => r.fileId == recordId || r.recordId == recordId);
                        if (fileRecord) {
                            console.log(`更新fileRecord ${recordId} 的准确率:`, serverFile.accuracy_score_float);

                            // 优先使用 accuracy_score_float，如果不存在则尝试其他字段
                            let accuracyValue = serverFile.accuracy_score_float;
                            if (accuracyValue === undefined || accuracyValue === null) {
                                accuracyValue = serverFile.accuracy_score;
                            }

                            fileRecord.accuracy_score = accuracyValue;
                            fileRecord.status = serverFile.status;
                        } else {
                            console.warn(`未找到fileRecord ${recordId}`);
                        }
                    } else {
                        console.error(`获取文件 ${recordId} 数据失败:`, data.message);
                    }
                })
                .catch(error => {
                    console.error(`同步文件 ${recordId} 数据失败:`, error);
                });
        });

        // 等待所有同步完成后刷新显示
        Promise.all(syncPromises).then(() => {
            console.log('所有文件数据同步完成，刷新显示');
            if (currentFileView === 'current') {
                displayCurrentFiles();
            } else if (currentFileView === 'history') {
                loadHistoryFiles();
            }
        }).catch(error => {
            console.error('同步文件数据时出错:', error);
            // 即使同步失败，也要尝试刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            } else if (currentFileView === 'history') {
                loadHistoryFiles();
            }
        });
    }

    // 显示分析类型选择提醒模态框
    function showAnalysisTypeRequiredModal(files) {
        const fileCountText = document.getElementById('fileCountText');
        if (fileCountText) {
            fileCountText.textContent = files.length;
        }

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('analysisTypeRequiredModal'));
        modal.show();

        // 临时存储文件，等用户选择分析类型后再处理
        window.pendingFiles = files;
    }

    // 滚动到分析类型选择区域
    function scrollToAnalysisTypes() {
        const analysisTypesSection = document.getElementById('analysisTypes');
        if (analysisTypesSection) {
            analysisTypesSection.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 添加高亮效果
            analysisTypesSection.classList.add('highlight-section');
            setTimeout(() => {
                analysisTypesSection.classList.remove('highlight-section');
            }, 3000);
        }
    }

    // 获取类型显示名称
    function getTypeDisplayName(typeId) {
        // 文件列表中显示简洁的四字名称
        const typeNames = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeNames[typeId] || typeId;
    }

    // 加载历史记录文件
    function loadHistoryFiles() {
        const container = document.getElementById('historyFilesList');
        if (!container) return Promise.resolve();

        // 显示加载状态 - 只更新表格体，不替换整个容器
        const tbody = document.getElementById('historyFilesTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2 text-muted">正在加载历史记录...</div>
                    </td>
                </tr>
            `;
        }

        // 构建查询参数
        const params = {
            page: filePagination.currentPage,
            per_page: filePagination.itemsPerPage
        };

        // 根据筛选条件添加分析类型参数
        // 如果有类型筛选，则只显示该类型的历史记录
        // 如果没有类型筛选，则显示所有类型的历史记录
        if (fileFilters.type && fileFilters.type !== '') {
            params.analysis_type = fileFilters.type;
        }

        if (fileFilters.status) {
            params.status = fileFilters.status;
        }
        if (fileFilters.search) {
            params.search = fileFilters.search;
        }

        // 根据文件状态筛选设置参数（参考文件管理页面）
        if (fileFilters.activeStatus) {
            if (fileFilters.activeStatus === 'all') {
                params.include_deprecated = true; // 包含所有文件
            } else if (fileFilters.activeStatus === 'active') {
                params.file_status = 'active'; // 只显示正常文件
            } else if (fileFilters.activeStatus === 'deprecated') {
                params.file_status = 'deprecated'; // 只显示废弃文件
            }
        } else {
            // 默认包含所有文件
            params.include_deprecated = true;
        }

        // 调用API - 历史记录使用records端点
        return API.get('/api/records', params)
            .then(response => {
                if (response && response.success && response.data) {
                    historyFiles = response.data.records || [];
                    filePagination.totalItems = response.data.pagination?.total || 0;
                    filePagination.totalPages = response.data.pagination?.pages || 0;

                    displayHistoryFiles();
                    updateFilePagination(response.data.pagination);
                    updateFileCountBadges();

                    return historyFiles;
                } else {
                    showFileError('获取历史记录失败: ' + (response?.message || '服务器响应异常'));
                    return [];
                }
            })
            .catch(error => {
                console.error('加载历史记录失败:', error);
                let errorMessage = '网络错误，请重试';

                if (error.response) {
                    const status = error.response.status;
                    const data = error.response.data;

                    if (status === 401) {
                        errorMessage = '登录已过期，请重新登录';
                        // 可以考虑重定向到登录页面
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                    } else if (status === 403) {
                        errorMessage = '权限不足，无法查看历史记录';
                    } else if (status === 500) {
                        errorMessage = '服务器内部错误，请稍后重试';
                    } else {
                        errorMessage = `请求失败 (${status}): ${data?.message || error.message}`;
                    }
                }

                showFileError(errorMessage);
                return [];
            });
    }

    // 显示历史记录文件
    function displayHistoryFiles() {
        const tbody = document.getElementById('historyFilesTableBody');

        if (!tbody) {
            return;
        }

        if (historyFiles.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <i class="bi bi-folder2-open display-4 text-muted"></i>
                        <div class="mt-3 text-muted">暂无历史记录</div>
                        <div class="text-muted">上传文件后将在这里显示记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = '';
        historyFiles.forEach((file, index) => {
            const row = createFileItemHTML(file, 'history');
            tbody.appendChild(row);
        });
    }

    // 创建文件项HTML - 与文件管理页面完全一致
    function createFileItemHTML(file, source) {
        const isHistory = source === 'history';
        const fileId = isHistory ? file.id : file.fileId;
        const fileName = isHistory ? file.filename : file.name;
        const fileSize = isHistory ? (file.file_size || 0) : file.size;
        // 修复创建时间显示 - 使用formatDateTime函数确保一致性
        const createdAt = isHistory ?
            formatDateTime(file.created_at) :
            formatDateTime(file.uploadTime || file.created_at || new Date());
        const analysisType = isHistory ? file.analysis_type : selectedAnalysisType;
        const status = isHistory ? file.status : (file.status || 'uploaded');
        const fileStatus = isHistory ? file.file_status : (file.file_status || 'active'); // 文件状态（active/deprecated）

        // 检查是否为账户开户场景的合并记录
        const isMergedRecord = file.is_merged_record || false;
        const fileCount = file.file_count || 1;
        const fileNames = file.file_names || [];

        const typeText = getAnalysisTypeText(analysisType);
        const statusBadge = getCurrentFileStatusBadge(status);
        const iconClass = getFileIconClass(fileName);
        const formattedSize = formatFileSize(fileSize);

        // 文件操作按钮逻辑 - 修复逻辑
        const isDeprecated = fileStatus === 'deprecated';
        // 可以分析的条件：文件未废弃（待审核和待复核状态也可以重分析）
        const canAnalyze = !isDeprecated;
        // 有结果的条件：状态表示已完成分析
        const hasResult = ['completed', 'analyzed', 'pending_audit', 'pending_review'].includes(status);

        // 移除审核复核按钮逻辑，这些功能只在查看结果弹窗中提供

        // 操作按钮 - 修复变量作用域问题，支持合并记录
        const analyzeFunction = isMergedRecord ?
            `analyzeMergedRecord('${fileId}')` :
            `analyzeFile('${fileId}')`;

        const actionButtons = `
            <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                    onclick="${analyzeFunction}"
                    ${canAnalyze ? '' : 'disabled'}>
                <i class="bi bi-cpu"></i>
                ${hasResult ? '重分析' : '分析'}
            </button>
            <button class="btn btn-view-result btn-sm"
                    onclick="viewResult('${fileId}')"
                    ${hasResult ? '' : 'disabled'}>
                <i class="bi bi-eye"></i>
                查看结果
            </button>

            ${!isDeprecated ? `
                <button class="btn btn-deprecate btn-sm"
                        onclick="deprecateFile('${fileId}')">
                    <i class="bi bi-archive"></i>
                    废弃
                </button>
            ` : `
                <button class="btn btn-restore btn-sm"
                        onclick="restoreFile('${fileId}')">
                    <i class="bi bi-arrow-counterclockwise"></i>
                    恢复
                </button>
            `}
        `;

        // 创建表格行 - 与文件管理页面完全一致
        const tr = document.createElement('tr');
        tr.dataset.fileId = fileId;
        tr.dataset.source = source;

        // 如果文件已废弃，添加样式
        if (fileStatus === 'deprecated') {
            tr.classList.add('file-deprecated');
        }

        const statusClass = getStatusClass(status);
        const statusText = getStatusText(status);
        const analysisTypeName = getAnalysisTypeText(analysisType);
        // 更健壮的准确率处理 - 与文件管理页面一致
        let accuracyScore = '0.0';
        if (file.accuracy_score !== undefined && file.accuracy_score !== null && !isNaN(file.accuracy_score)) {
            // 如果accuracy_score已经是百分比形式（>1），直接使用；否则乘以100
            const rawScore = parseFloat(file.accuracy_score);
            accuracyScore = rawScore > 1 ? rawScore.toFixed(1) : (rawScore * 100).toFixed(1);
        }
        const accuracyClass = getAccuracyClass(file.accuracy_score || 0);

        tr.innerHTML = `
            <td>
                <input type="checkbox" class="tech-checkbox file-checkbox"
                       value="${fileId}"
                       ${canAnalyze ? '' : 'disabled'}
                       data-file-id="${fileId}"
                       ${isHistory ? '' : (file.selected ? 'checked' : '')}
                       onchange="handleFileSelection(this)">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    ${isMergedRecord ?
                        `<i class="bi bi-files me-2 text-primary" title="合并记录"></i>` :
                        `<i class="bi bi-file-earmark-text me-2 text-warning"></i>`
                    }
                    <div>
                        <span title="${fileName}">${truncateText(fileName, 30)}</span>
                        ${isMergedRecord ?
                            `<div class="small text-muted">包含 ${fileCount} 个文件</div>` :
                            ''
                        }
                    </div>
                </div>
            </td>
            <td>
                <span class="analysis-type-badge analysis-type-${analysisType}">
                    ${analysisTypeName}
                </span>
            </td>
            <td>
                <span class="status-badge ${statusClass}">
                    ${getStatusIcon(status)}
                    ${statusText}
                </span>
            </td>
            <td>
                <div class="accuracy-indicator">
                    <span class="small">${accuracyScore}%</span>
                    <div class="accuracy-bar">
                        <div class="accuracy-fill ${accuracyClass}"
                             style="width: ${accuracyScore}%"></div>
                    </div>
                </div>
            </td>
            <td>
                <span class="small text-muted">${createdAt}</span>
            </td>
            <td>
                <div class="file-actions">
                    ${actionButtons}
                </div>
            </td>
        `;

        return tr;
    }

    // 获取状态徽章（历史记录用）
    function getStatusBadge(status) {
        const badges = {
            'uploaded': '<span class="status-badge uploaded">已上传</span>',
            'pending': '<span class="status-badge pending">待处理</span>',
            'processing': '<span class="status-badge processing">处理中</span>',
            'analyzing': '<span class="status-badge processing">分析中</span>',
            'completed': '<span class="status-badge completed">已完成</span>',
            'failed': '<span class="status-badge failed">失败</span>',
            'analyzed': '<span class="status-badge analyzed">已分析</span>',
            'pending_audit': '<span class="status-badge pending-audit">待审核</span>',
            'pending_review': '<span class="status-badge pending-review">待复核</span>',
            'deprecated': '<span class="status-badge deprecated">已废弃</span>'
        };
        return badges[status] || `<span class="status-badge">${status}</span>`;
    }

    // 获取当前文件状态徽章 - 与文件管理页面一致
    function getCurrentFileStatusBadge(status) {
        const badges = {
            'uploaded': '<span class="status-badge uploaded">已上传</span>',
            'pending': '<span class="status-badge pending">待处理</span>',
            'processing': '<span class="status-badge processing">处理中</span>',
            'analyzing': '<span class="status-badge processing">分析中</span>',
            'completed': '<span class="status-badge completed">已完成</span>',
            'failed': '<span class="status-badge failed">失败</span>',
            'pending_audit': '<span class="status-badge pending-audit">待审核</span>',
            'pending_review': '<span class="status-badge pending-review">待复核</span>',
            'deprecated': '<span class="status-badge deprecated">已废弃</span>'
        };
        return badges[status] || `<span class="status-badge">${status}</span>`;
    }

    // 获取当前文件状态图标
    function getCurrentFileStatusIcon(status) {
        const icons = {
            'pending': '',
            'analyzing': '<div class="file-status-badge analyzing"><i class="bi bi-arrow-clockwise"></i></div>',
            'analyzed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'completed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'failed': '<div class="file-status-badge failed"><i class="bi bi-x"></i></div>',
            'deprecated': '<div class="file-status-badge deprecated"><i class="bi bi-archive"></i></div>'
        };
        return icons[status] || '';
    }

    // 获取当前文件状态文本
    function getCurrentFileStatusText(status) {
        const texts = {
            'uploaded': '已上传',
            'pending': '待分析',
            'analyzing': '分析中...',
            'analyzed': '已分析',
            'completed': '已完成',
            'failed': '分析失败',
            'deprecated': '已废弃',
            'pending_audit': '待审核',
            'pending_review': '待复核'
        };
        return texts[status] || status;  // 改为显示原始状态而不是"未知状态"
    }

    // 获取文件图标类
    function getFileIconClass(filename) {
        if (!filename) return 'file-image';
        const ext = filename.toLowerCase().split('.').pop();
        if (ext === 'pdf') {
            return 'file-pdf';
        }
        return 'file-image';
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 截断文件名
    function truncateFilename(filename, maxLength) {
        if (!filename || filename.length <= maxLength) return filename || '';
        const ext = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - ext.length - 4) + '...';
        return truncatedName + '.' + ext;
    }

    // 刷新文件列表
    function refreshFileList() {
        console.log('手动刷新文件列表');
        showMessage('正在刷新文件列表...', 'info');

        if (currentFileView === 'current') {
            displayCurrentFiles();
        } else {
            loadHistoryFiles();
        }

        // 延迟显示刷新完成消息
        setTimeout(() => {
            showMessage('文件列表已刷新', 'success');
        }, 500);
    }

    // 筛选文件
    function filterFiles() {
        const typeFilter = document.getElementById('fileTypeFilter');
        const statusFilter = document.getElementById('fileStatusFilter');
        const activeStatusFilter = document.getElementById('fileActiveStatusFilter');

        if (currentFileView === 'history') {
            if (typeFilter) {
                const newType = typeFilter.value;
                fileFilters.type = newType;

                // 反向同步到分析类型卡片选择
                // 总是调用同步函数，确保卡片状态正确更新
                syncAnalysisTypeFromFilter(newType);
            }
            if (statusFilter) {
                fileFilters.status = statusFilter.value;
            }
            if (activeStatusFilter) {
                fileFilters.activeStatus = activeStatusFilter.value;
            }
            filePagination.currentPage = 1; // 重置到第一页
            loadHistoryFiles();
        } else if (currentFileView === 'current') {
            // 当前上传文件也支持类型筛选
            if (typeFilter) {
                const newType = typeFilter.value;
                fileFilters.type = newType;

                // 反向同步到分析类型卡片选择
                // 总是调用同步函数，确保卡片状态正确更新
                syncAnalysisTypeFromFilter(newType);
            }
            if (statusFilter) {
                fileFilters.status = statusFilter.value;
            }
            if (activeStatusFilter) {
                fileFilters.activeStatus = activeStatusFilter.value;
            }
            displayCurrentFiles();
            updateFileCountBadges();
        }
    }

    // 从文件过滤器同步分析类型选择到分析类型卡片
    function syncAnalysisTypeFromFilter(typeId) {
        // 移除其他选中状态
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        if (!typeId) {
            // 如果是"全部类型"，清除选中状态
            selectedAnalysisType = '';

            // 更新隐藏的input
            let analysisTypeInput = document.getElementById('analysisType');
            if (analysisTypeInput) {
                analysisTypeInput.value = '';
            }
            return;
        }

        // 选中对应的分析类型卡片
        const typeCard = document.querySelector(`.analysis-type-card[data-type="${typeId}"]`);
        if (typeCard) {
            typeCard.classList.add('selected');
            selectedAnalysisType = typeId;

            // 更新隐藏的input
            let analysisTypeInput = document.getElementById('analysisType');
            if (!analysisTypeInput) {
                analysisTypeInput = document.createElement('input');
                analysisTypeInput.type = 'hidden';
                analysisTypeInput.id = 'analysisType';
                document.body.appendChild(analysisTypeInput);
            }
            analysisTypeInput.value = typeId;
        }
    }

    // 搜索文件
    function searchFiles() {
        fileFilters.search = document.getElementById('fileSearchInput').value.trim();

        if (currentFileView === 'history') {
            filePagination.currentPage = 1; // 重置到第一页
            // 防抖处理
            clearTimeout(window.fileSearchTimeout);
            window.fileSearchTimeout = setTimeout(() => {
                loadHistoryFiles();
            }, 500);
        } else {
            // 当前文件视图也支持搜索
            displayCurrentFiles();
            updateFileCountBadges();
        }
    }

    // 清除文件筛选
    function clearFileFilters() {
        document.getElementById('fileTypeFilter').value = '';
        document.getElementById('fileStatusFilter').value = '';
        document.getElementById('fileActiveStatusFilter').value = 'all';
        document.getElementById('fileSearchInput').value = '';
        fileFilters = { type: '', status: '', activeStatus: 'all', search: '' };
        filePagination.currentPage = 1;

        // 清除分析类型卡片选择
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });
        selectedAnalysisType = '';

        if (currentFileView === 'history') {
            loadHistoryFiles();
        } else {
            displayCurrentFiles();
            updateFileCountBadges();
        }
    }

    // 文件操作函数
    function toggleFileSelection(fileId) {
        const checkbox = document.querySelector(`[data-file-id="${fileId}"] .file-checkbox`);
        const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);

        if (checkbox && fileItem) {
            if (checkbox.checked) {
                fileItem.classList.add('selected');
                if (!selectedFiles.includes(fileId)) {
                    selectedFiles.push(fileId);
                }
            } else {
                fileItem.classList.remove('selected');
                const index = selectedFiles.indexOf(fileId);
                if (index > -1) {
                    selectedFiles.splice(index, 1);
                }
            }
        }

        updateSelectionUI();
    }

    // 切换当前文件选择状态
    function toggleCurrentFileSelection(fileId, isSelected) {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const record = currentTypeFiles.find(f => f.fileId === fileId);
        if (record && record.status !== 'deprecated') {
            record.selected = isSelected;

            // 同步更新 fileRecords
            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
            const fileRecord = fileRecords.find(r => r.fileId === fileId);
            if (fileRecord) {
                fileRecord.selected = isSelected;
            }

            updateSelectionUI();
            updateSelectAllCheckbox();
            checkAnalyzeButton();
        }
    }

    // 分析当前文件
    function analyzeCurrentFile(fileId) {
        if (!selectedAnalysisType) {
            Utils.showMessage('请先选择分析类型', 'warning');
            return;
        }

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        const record = currentTypeFiles.find(f => f.fileId === fileId);
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (!record || !fileRecord) {
            Utils.showMessage('文件不存在', 'warning');
            return;
        }

        // 更新状态为分析中
        record.status = 'analyzing';
        fileRecord.status = 'analyzing';

        // 刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('analysis_type', selectedAnalysisType);
        formData.append('files', fileRecord.file);

        // 发送分析请求
        fetch('/api/analysis/start', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                record.status = 'analyzed';
                fileRecord.status = 'analyzed';
                fileRecord.recordId = data.record_ids[0];
                showMessage(`文件 ${record.name} 分析完成`, 'success');
            } else {
                record.status = 'failed';
                fileRecord.status = 'failed';
                showMessage(`文件 ${record.name} 分析失败: ${data.message}`, 'error');
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        })
        .catch(error => {
            record.status = 'failed';
            fileRecord.status = 'failed';
            showMessage(`文件 ${record.name} 分析失败`, 'error');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        });
    }

    // 重新分析当前文件
    function reanalyzeCurrentFile(fileId) {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        const record = currentTypeFiles.find(f => f.fileId === fileId);
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (record && fileRecord) {
            record.status = 'pending';
            fileRecord.status = 'pending';
            fileRecord.analysisResult = null;
            fileRecord.recordId = null;

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            // 开始分析
            analyzeCurrentFile(fileId);
        }
    }

    // 查看当前文件结果
    function viewCurrentFileResult(fileId) {
        if (!selectedAnalysisType) return;

        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);
        if (fileRecord && fileRecord.recordId) {
            showAnalysisResultModal(fileRecord.recordId);
        } else {
            showMessage('暂无分析结果', 'warning');
        }
    }

    // 废弃当前文件
    function deprecateCurrentFile(fileId) {
        if (confirm('确定要废弃这个文件吗？')) {
            const record = currentFiles.find(f => f.fileId === fileId);
            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
            const fileRecord = fileRecords.find(r => r.fileId === fileId);

            if (record && fileRecord) {
                record.status = 'deprecated';
                fileRecord.status = 'deprecated';
                record.selected = false;
                fileRecord.selected = false;

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                updateSelectionUI();
                checkAnalyzeButton();
                showMessage(`文件 ${record.name} 已废弃`, 'info');
            }
        }
    }

    // 恢复当前文件
    function restoreCurrentFile(fileId) {
        const record = currentFiles.find(f => f.fileId === fileId);
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (record && fileRecord) {
            record.status = 'uploaded';
            fileRecord.status = 'pending';

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            showMessage(`文件 ${record.name} 已恢复`, 'success');
        }
    }

    // 移除当前文件
    function removeCurrentFile(fileId) {
        if (confirm('确定要移除这个文件吗？')) {
            // 从当前文件列表中移除
            currentFiles = currentFiles.filter(file => file.fileId !== fileId);

            // 从文件记录中移除
            if (fileRecordsByType[selectedAnalysisType]) {
                fileRecordsByType[selectedAnalysisType] = fileRecordsByType[selectedAnalysisType].filter(record => record.fileId !== fileId);
            }

            // 从选择列表中移除
            const index = selectedFiles.indexOf(fileId);
            if (index > -1) {
                selectedFiles.splice(index, 1);
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            updateFileCountBadges();
            updateSelectionUI();
            checkAnalyzeButton();
        }
    }

    // 这个函数已经不再需要，因为右上角的全选框已被删除
    // 全选功能现在通过表格头部的 toggleSelectAllCurrent 函数实现

    // 更新表格头部全选复选框状态
    function updateSelectAllCheckbox() {
        if (currentFileView === 'current') {
            const selectAllCurrent = document.getElementById('selectAllCurrent');
            const checkboxes = document.querySelectorAll('#currentFilesList .file-checkbox');
            const enabledCheckboxes = Array.from(checkboxes).filter(cb => !cb.disabled);

            if (selectAllCurrent && enabledCheckboxes.length > 0) {
                const checkedCount = enabledCheckboxes.filter(cb => cb.checked).length;

                if (checkedCount === 0) {
                    selectAllCurrent.checked = false;
                    selectAllCurrent.indeterminate = false;
                } else if (checkedCount === enabledCheckboxes.length) {
                    selectAllCurrent.checked = true;
                    selectAllCurrent.indeterminate = false;
                } else {
                    selectAllCurrent.checked = false;
                    selectAllCurrent.indeterminate = true;
                }
            } else if (selectAllCurrent) {
                selectAllCurrent.checked = false;
                selectAllCurrent.indeterminate = false;
            }
        }
    }

    // 表格头部全选功能
    function toggleSelectAllCurrent(checked) {
        if (currentFileView === 'current') {
            const checkboxes = document.querySelectorAll('#currentFilesList .file-checkbox');

            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = checked;
                    const fileId = checkbox.closest('tr').querySelector('.file-checkbox').dataset.fileId;
                    if (fileId) {
                        toggleCurrentFileSelection(fileId, checkbox.checked);
                    }
                }
            });
        }
    }

    // 历史记录表格头部全选功能
    function toggleSelectAllHistory(checked) {
        if (currentFileView === 'history') {
            const checkboxes = document.querySelectorAll('#historyFilesList .file-checkbox');

            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = checked;
                    // 历史记录的选择逻辑可以根据需要实现
                    // 目前历史记录主要用于查看，不需要复杂的选择逻辑
                }
            });
        }
    }

    function clearSelectedFiles() {
        if (currentFileView === 'current') {
            // 获取选中的文件ID
            const selectedFileIds = currentFiles
                .filter(file => file.selected && file.status !== 'deprecated')
                .map(file => file.fileId);

            if (selectedFileIds.length === 0) {
                showMessage('没有选中的文件', 'warning');
                return;
            }

            if (confirm(`确定要移除 ${selectedFileIds.length} 个选中的文件吗？`)) {
                selectedFileIds.forEach(fileId => {
                    removeCurrentFile(fileId);
                });
            }
        }
        // 历史记录视图不支持批量操作
    }

    function updateSelectionUI() {
        if (currentFileView === 'current') {
            // 更新当前文件的选择状态
            const selectedCount = currentFiles.filter(file => file.selected && file.status !== 'deprecated').length;

            // 更新文件项的选择状态显示
            currentFiles.forEach(file => {
                const fileItem = document.querySelector(`[data-file-id="${file.fileId}"]`);
                if (fileItem) {
                    if (file.selected) {
                        fileItem.classList.add('selected');
                    } else {
                        fileItem.classList.remove('selected');
                    }
                }
            });

            // 可以在这里添加选择计数显示
        }
    }

    // 文件操作
    function removeFile(fileId) {
        if (!selectedAnalysisType) return;

        // 从对应类型的文件列表中移除
        if (filesByType[selectedAnalysisType]) {
            filesByType[selectedAnalysisType] = filesByType[selectedAnalysisType].filter(file => file.fileId !== fileId);
        }

        // 从对应类型的文件记录中移除
        if (fileRecordsByType[selectedAnalysisType]) {
            fileRecordsByType[selectedAnalysisType] = fileRecordsByType[selectedAnalysisType].filter(record => record.fileId !== fileId);
        }

        // 更新显示
        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
    }

    function viewAnalysisResult(recordId) {
        showAnalysisResultModal(recordId);
    }

    function viewFileDetail(recordId) {
        window.open(`/records/${recordId}`, '_blank');
    }

    function reanalyzeFile(recordId) {
        if (confirm('确定要重新分析这个文件吗？')) {
            // 调用重新分析API
            API.post(`/api/files/${recordId}/reanalyze`)
                .then(response => {
                    if (response.success) {
                        showMessage('文件重新分析完成', 'success');
                        refreshFileList();
                    } else {
                        showMessage(response.message || '重新分析失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('重新分析失败:', error);
                    showMessage('重新分析失败，请重试', 'error');
                });
        }
    }

    // 重新分析历史文件
    function reanalyzeHistoryFile(recordId) {
        if (confirm('确定要重新分析这个文件吗？')) {
            // 调用重新分析API
            API.post(`/api/files/${recordId}/reanalyze`)
                .then(response => {
                    if (response.success) {
                        showMessage('文件已提交重新分析', 'success');
                        if (currentFileView === 'history') {
                            loadHistoryFiles();
                        }
                    } else {
                        showMessage(response.message || '重新分析失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('重新分析失败:', error);
                    showMessage('重新分析失败，请重试', 'error');
                });
        }
    }

    // 隐藏分页控件
    function hidePagination() {
        const paginationElement = document.getElementById('filePagination');
        if (paginationElement) {
            paginationElement.innerHTML = '';
            paginationElement.style.display = 'none';
        }
    }

    // 显示分页控件
    function showPagination() {
        const paginationElement = document.getElementById('filePagination');
        if (paginationElement) {
            paginationElement.style.display = 'flex';
        }
    }

    // 更新分页
    function updateFilePagination(pagination) {
        const paginationElement = document.getElementById('filePagination');
        if (!paginationElement || !pagination || currentFileView !== 'history') {
            hidePagination();
            return;
        }

        const { page, pages, total } = pagination;

        if (pages <= 1) {
            hidePagination();
            return;
        }

        showPagination();

        let paginationHTML = `
            <div class="pagination-container">
                <div class="pagination-info-section">
                    <div class="pagination-info-text">
                        第 ${page} 页，共 ${pages} 页，总计 ${total} 条记录
                    </div>
                    <div class="per-page-selector">
                        <label>每页显示</label>
                        <select id="docAnalysisPerPageSelect" onchange="changeDocAnalysisPerPage()">
                            <option value="10" ${filePagination.itemsPerPage === 10 ? 'selected' : ''}>10条</option>
                            <option value="20" ${filePagination.itemsPerPage === 20 ? 'selected' : ''}>20条</option>
                            <option value="50" ${filePagination.itemsPerPage === 50 ? 'selected' : ''}>50条</option>
                            <option value="100" ${filePagination.itemsPerPage === 100 ? 'selected' : ''}>100条</option>
                        </select>
                    </div>
                </div>
                <div class="pagination-buttons">
        `;

        // 上一页按钮
        if (page > 1) {
            paginationHTML += `
                <button class="btn btn-outline-secondary" onclick="goToFilePage(${page - 1})" title="上一页">
                    <i class="bi bi-chevron-left"></i>
                </button>
            `;
        }

        // 首页按钮（如果当前页距离首页较远）
        if (page > 3) {
            paginationHTML += `<button class="btn btn-outline-secondary" onclick="goToFilePage(1)" title="第1页">1</button>`;
            if (page > 4) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
        }

        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'btn-primary' : 'btn-outline-secondary';
            paginationHTML += `<button class="btn ${activeClass}" onclick="goToFilePage(${i})" title="第${i}页">${i}</button>`;
        }

        // 末页按钮（如果当前页距离末页较远）
        if (page < pages - 2) {
            if (page < pages - 3) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="btn btn-outline-secondary" onclick="goToFilePage(${pages})" title="第${pages}页">${pages}</button>`;
        }

        // 下一页按钮
        if (page < pages) {
            paginationHTML += `
                <button class="btn btn-outline-secondary" onclick="goToFilePage(${page + 1})" title="下一页">
                    <i class="bi bi-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += `
                </div>
            </div>
        `;

        paginationElement.innerHTML = paginationHTML;
    }

    function goToFilePage(page) {
        filePagination.currentPage = page;
        if (currentFileView === 'history') {
            loadHistoryFiles();
        }
    }

    // 恢复每页显示条数设置
    function restoreDocAnalysisPerPageSetting() {
        const savedPerPage = localStorage.getItem('globalPerPageSetting');
        if (savedPerPage) {
            filePagination.itemsPerPage = parseInt(savedPerPage);
        }
    }

    // 改变每页显示条数
    function changeDocAnalysisPerPage() {
        const perPageSelect = document.getElementById('docAnalysisPerPageSelect');
        if (perPageSelect) {
            filePagination.itemsPerPage = parseInt(perPageSelect.value);

            // 保存用户偏好到本地存储（使用全局键名，与文件管理页面同步）
            localStorage.setItem('globalPerPageSetting', filePagination.itemsPerPage);

            // 重新加载第一页
            filePagination.currentPage = 1;
            if (currentFileView === 'history') {
                loadHistoryFiles();
            }
        }
    }

    // 显示文件错误
    function showFileError(message) {
        if (currentFileView === 'current') {
            const container = document.getElementById('currentFilesList');
            if (container) {
                container.innerHTML = `
                    <div class="file-list-empty">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                        <div class="text-danger">${message}</div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="refreshFileList()">
                            <i class="bi bi-arrow-clockwise me-1"></i>重试
                        </button>
                    </div>
                `;
            }
        } else {
            // 历史记录视图 - 只更新表格体
            const tbody = document.getElementById('historyFilesTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <i class="bi bi-exclamation-triangle text-danger display-4"></i>
                            <div class="mt-3 text-danger">${message}</div>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="refreshFileList()">
                                <i class="bi bi-arrow-clockwise me-1"></i>重试
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
    }

    // 加载分析类型
    function loadAnalysisTypes() {
        const analysisTypes = [
            {
                id: 'futures_account',
                name: '期货账户/开户文件解析',
                icon: 'bi-graph-up',
                description: '分析期货账户开户、变更等相关文档，提取账户信息和交易记录'
            },
            {
                id: 'wealth_management',
                name: '理财产品说明书',
                icon: 'bi-piggy-bank',
                description: '分析理财产品说明书，提取产品信息、收益率、风险等级等要素'
            },
            {
                id: 'broker_interest',
                name: '券商账户计息变更',
                icon: 'bi-calculator',
                description: '处理券商计息相关变更文档，识别利率调整和计息规则变化'
            },
            {
                id: 'account_opening',
                name: '账户开户场景',
                icon: 'bi-person-plus',
                description: '分析账户开户相关文档，提取开户信息、账户类型、客户资料等数据'
            },
            {
                id: 'ningyin_fee',
                name: '宁银理财费用变更',
                icon: 'bi-receipt',
                description: '处理宁银理财费用相关变更文档，识别费用标准和收费项目'
            },
            {
                id: 'non_standard_trade',
                name: '非标交易确认单解析',
                icon: 'bi-file-text',
                description: '分析非标准化交易确认文档，提取交易详情和确认信息'
            }
        ];

        const container = document.getElementById('analysisTypes');
        if (!container) return;

        container.innerHTML = '';

        analysisTypes.forEach(type => {
            const typeCard = document.createElement('div');
            typeCard.className = 'analysis-type-card';
            typeCard.dataset.type = type.id;

            typeCard.innerHTML = `
                <div class="analysis-type-icon">
                    <i class="bi ${type.icon}"></i>
                </div>
                <h4 class="analysis-type-title">${type.name}</h4>
                <p class="analysis-type-desc">${type.description}</p>
            `;

            typeCard.addEventListener('click', () => selectAnalysisType(type.id, typeCard));
            container.appendChild(typeCard);
        });
    }

    // 选择分析类型
    function selectAnalysisType(typeId, element) {
        // 移除其他选中状态
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 设置当前选中
        element.classList.add('selected');
        selectedAnalysisType = typeId;

        // 创建或更新隐藏的input来存储选择的类型
        let analysisTypeInput = document.getElementById('analysisType');
        if (!analysisTypeInput) {
            analysisTypeInput = document.createElement('input');
            analysisTypeInput.type = 'hidden';
            analysisTypeInput.id = 'analysisType';
            document.body.appendChild(analysisTypeInput);
        }
        analysisTypeInput.value = typeId;

        // 初始化该类型的文件存储（如果还没有）
        if (!filesByType[typeId]) {
            filesByType[typeId] = [];
        }
        if (!fileRecordsByType[typeId]) {
            fileRecordsByType[typeId] = [];
        }

        // 刷新文件列表显示（显示当前类型的文件）
        displayCurrentFiles();
        updateFileCountBadges();

        // 同步文件管理的类型过滤器
        const fileTypeFilter = document.getElementById('fileTypeFilter');
        if (fileTypeFilter) {
            fileTypeFilter.value = typeId;

            // 更新文件过滤器状态
            if (typeof fileFilters !== 'undefined') {
                fileFilters.type = typeId;
            }
        }

        // 同步历史记录区域的类型过滤器
        const historyTypeFilter = document.getElementById('historyAnalysisType');
        if (historyTypeFilter) {
            historyTypeFilter.value = typeId;

        }

        // 如果当前在历史记录视图，刷新历史文件列表
        if (currentFileView === 'history') {

            loadHistoryFiles();

        } else {
            // 如果在当前文件视图，也要应用过滤器
            if (typeof filterFiles === 'function') {
                filterFiles();
            }
        }

        // 清除上传区域的提醒信息
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            const reminder = uploadArea.querySelector('.analysis-type-reminder');
            if (reminder) {
                reminder.remove();
            }
        }

        // 特殊处理账户开户场景
        if (typeId === 'account_opening') {
            showAccountOpeningInterface();
        } else {
            hideAccountOpeningInterface();
        }

        // 检查是否可以启用分析按钮
        checkAnalyzeButton();

        // 显示成功消息
        const titleElement = element.querySelector('.analysis-type-title') || element.querySelector('h6');
        const title = titleElement ? titleElement.textContent : '未知类型';

        // 自动滚动到文件上传区域（延迟执行确保DOM更新完成）
        setTimeout(() => {
            scrollToUploadArea();
        }, 100);

        // 处理待处理的文件（如果有的话）
        if (window.pendingFiles && window.pendingFiles.length > 0) {
            const pendingFiles = window.pendingFiles;
            window.pendingFiles = null; // 清除待处理文件

            // 显示提示消息
            showMessage(`已选择 ${title} 类型，正在处理 ${pendingFiles.length} 个文件...`, 'info');

            // 处理文件
            setTimeout(() => {
                processSelectedFiles(pendingFiles);
            }, 500);
            return;
        }

        // 检查是否开启自动分析且有单个文件
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        const isAutoAnalysisEnabled = autoAnalysisSwitch ? autoAnalysisSwitch.checked : false;

        // 获取当前已选择的文件数量
        const selectedCurrentFiles = getCurrentTypeFiles().filter(file => file.selected && file.status !== 'deprecated');

        if (selectedCurrentFiles.length === 1 && isAutoAnalysisEnabled) {
            showAutoAnalysisMessage(title, selectedCurrentFiles[0].name);
            setTimeout(() => {
                startAnalysis();
            }, 1500);
        } else {
            showMessage(`已选择分析类型：${title}`, 'success');
            if (selectedCurrentFiles.length === 1 && !isAutoAnalysisEnabled) {
                setTimeout(() => {
                    showMessage('💡 提示：您可以开启"自动分析"开关，单文件上传时自动开始分析', 'info', 3000);
                }, 1000);
            }
        }
    }

    // 滚动到文件上传区域
    function scrollToUploadArea() {
        // 尝试多个可能的上传区域元素
        const uploadTargets = [
            'uploadArea',           // 主要目标：上传区域
            'fileManagementArea',   // 文件管理区域
            'unified-file-manager', // 统一文件管理器
            'currentFilesList',     // 当前文件列表
            'fileUploadSection'     // 文件上传区域
        ];

        let uploadArea = null;
        for (const targetId of uploadTargets) {
            uploadArea = document.getElementById(targetId);
            if (uploadArea) {

                break;
            }
        }

        if (!uploadArea) {
            console.warn('No upload area found, trying alternative approach');
            // 尝试通过类名查找
            uploadArea = document.querySelector('.file-management-card, .upload-area, [data-upload-area]');
        }

        if (!uploadArea) {
            console.error('Upload area not found');
            return;
        }

        try {
            // 获取导航栏高度（考虑sticky定位）
            const navbar = document.querySelector('.top-navbar, .navbar, .fixed-top, .sticky-top');
            const navbarHeight = navbar ? navbar.offsetHeight : 70; // 默认70px

            // 获取上传区域的位置
            const uploadAreaRect = uploadArea.getBoundingClientRect();
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // 计算目标滚动位置（减去导航栏高度和一些额外的间距）
            const extraPadding = 20; // 额外的间距
            const targetScrollTop = Math.max(0, currentScrollTop + uploadAreaRect.top - navbarHeight - extraPadding);

            // 检查是否需要滚动
            if (Math.abs(uploadAreaRect.top - navbarHeight) < 50) {
            } else {
                // 使用平滑滚动
                window.scrollTo({
                    top: targetScrollTop,
                    behavior: 'smooth'
                });
            }

            // 添加高亮动画效果
            uploadArea.classList.add('highlight');

            // 动画结束后移除类
            setTimeout(() => {
                uploadArea.classList.remove('highlight');
            }, 600);

        } catch (error) {
            console.error('Scroll to upload area failed:', error);
            // 降级方案1：使用简单的scrollIntoView
            try {
                uploadArea.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            } catch (fallbackError) {
                console.error('Fallback scroll also failed:', fallbackError);
                // 降级方案2：直接滚动到元素位置
                const rect = uploadArea.getBoundingClientRect();
                const scrollTop = window.pageYOffset + rect.top - 100;
                window.scrollTo(0, scrollTop);
            }
        }
    }

    // 显示自动分析消息提醒
    function showAutoAnalysisMessage(analysisType, fileName) {
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        const switchLabel = autoAnalysisSwitch ? autoAnalysisSwitch.nextElementSibling : null;

        // 临时高亮开关
        if (switchLabel) {
            switchLabel.style.color = '#28a745';
            switchLabel.style.fontWeight = 'bold';
            setTimeout(() => {
                switchLabel.style.color = '';
                switchLabel.style.fontWeight = '';
            }, 2000);
        }

        // 显示详细的自动分析消息
        showMessage(
            `🤖 自动分析已启动<br>` +
            `📄 文件：${fileName}<br>` +
            `🎯 类型：${analysisType}<br>` +
            `⏱️ 预计用时：30-60秒`,
            'info',
            2000
        );
    }

    // 获取分析类型的显示名称
    function getAnalysisTypeName(typeId) {
        // 文件列表中显示简洁的四字名称
        const typeNames = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeNames[typeId] || typeId;
    }

    // 根据分析类型筛选文件列表
    function filterFilesByType(typeId) {
        // 如果当前在历史记录视图，切换到当前上传视图以便用户上传新文件
        if (currentFileView === 'history') {
            switchFileView('current');
        }

        // 不自动设置筛选器，避免干扰用户的筛选选择
        // 只是确保用户在正确的视图中进行文件上传
    }

    // 设置事件监听器
    function setupEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');

        if (uploadArea && fileInput) {
            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);
        }

        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', startAnalysis);
        }

        // 自动分析开关事件监听
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        if (autoAnalysisSwitch) {
            autoAnalysisSwitch.addEventListener('change', function() {
                const isEnabled = this.checked;
                const message = isEnabled ?
                    '✅ 自动分析已开启：单文件上传时将自动开始分析' :
                    '⏸️ 自动分析已关闭：需要手动点击"开始分析"按钮';
                showMessage(message, isEnabled ? 'success' : 'info', 3000);

                // 保存用户偏好到localStorage
                localStorage.setItem('autoAnalysisEnabled', isEnabled);

                // 同步到服务器
                API.post('/api/settings/auto-analysis', { enabled: isEnabled })
                    .then(response => {
                        if (!response.success) {
                            console.warn('自动分析设置同步失败:', response.message);
                        }
                    })
                    .catch(error => {
                        console.warn('自动分析设置同步失败:', error);
                    });
            });

            // 从服务器获取自动分析设置
            API.get('/api/settings/auto-analysis')
                .then(response => {
                    if (response.success && response.data) {
                        autoAnalysisSwitch.checked = response.data.enabled;
                        localStorage.setItem('autoAnalysisEnabled', response.data.enabled);
                    } else {
                        // 如果服务器获取失败，从localStorage恢复用户偏好
                        const savedPreference = localStorage.getItem('autoAnalysisEnabled');
                        if (savedPreference !== null) {
                            autoAnalysisSwitch.checked = savedPreference === 'true';
                        }
                    }
                })
                .catch(error => {
                    console.warn('获取自动分析设置失败:', error);
                    // 从localStorage恢复用户偏好
                    const savedPreference = localStorage.getItem('autoAnalysisEnabled');
                    if (savedPreference !== null) {
                        autoAnalysisSwitch.checked = savedPreference === 'true';
                    }
                });
        }

        // 账户开户场景事件监听器
        setupAccountOpeningEventListeners();
    }

    // 设置账户开户场景事件监听器
    function setupAccountOpeningEventListeners() {
        // 上传完毕按钮
        const completeUploadBtn = document.getElementById('completeUploadBtn');
        if (completeUploadBtn) {
            completeUploadBtn.addEventListener('click', handleCompleteUpload);
        }

        // 重新选择按钮
        const resetUploadBtn = document.getElementById('resetUploadBtn');
        if (resetUploadBtn) {
            resetUploadBtn.addEventListener('click', () => {
                resetAccountOpeningState();
                const productTypeSelection = document.getElementById('productTypeSelection');
                if (productTypeSelection) {
                    productTypeSelection.style.display = 'block';
                }
                const documentUploadSection = document.getElementById('documentUploadSection');
                if (documentUploadSection) {
                    documentUploadSection.style.display = 'none';
                }
            });
        }

        // 开始分析按钮
        const startAccountOpeningAnalysisBtn = document.getElementById('startAccountOpeningAnalysisBtn');
        if (startAccountOpeningAnalysisBtn) {
            startAccountOpeningAnalysisBtn.addEventListener('click', startAccountOpeningAnalysis);
        }
    }

    // 处理文件选择
    function handleFileSelect(event) {
        const files = Array.from(event.target.files);
        processSelectedFiles(files);
    }

    // 处理拖拽悬停
    function handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');

        // 如果没有选择分析类型，显示提醒
        if (!selectedAnalysisType) {
            const uploadArea = event.currentTarget;
            if (!uploadArea.querySelector('.analysis-type-reminder')) {
                const reminder = document.createElement('div');
                reminder.className = 'analysis-type-reminder alert alert-warning mt-2';
                reminder.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    请先选择分析类型再上传文件
                `;
                uploadArea.appendChild(reminder);
            }
        }
    }

    // 处理拖拽离开
    function handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');

        // 移除提醒信息
        const reminder = event.currentTarget.querySelector('.analysis-type-reminder');
        if (reminder) {
            reminder.remove();
        }
    }

    // 处理文件拖拽
    function handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');

        const files = Array.from(event.dataTransfer.files);
        processSelectedFiles(files);
    }

    // 处理选中的文件
    function processSelectedFiles(files) {
        console.log('🔍 processSelectedFiles 被调用:', {
            filesCount: files.length,
            selectedAnalysisType: selectedAnalysisType,
            currentFileView: currentFileView
        });

        // 验证文件
        const validFiles = files.filter(file => {
            const validTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff'];
            const maxSize = 200 * 1024 * 1024; // 200MB

            if (!validTypes.includes(file.type) && !file.name.match(/\.(pdf|png|jpg|jpeg|gif|bmp|tiff)$/i)) {
                showMessage(`文件 ${file.name} 格式不支持`, 'warning');
                return false;
            }

            if (file.size > maxSize) {
                showMessage(`文件 ${file.name} 超过200MB限制`, 'warning');
                return false;
            }

            return true;
        });

        if (validFiles.length > 0) {
            // 检查是否选择了分析类型
            if (!selectedAnalysisType) {
                showAnalysisTypeRequiredModal(validFiles);
                return;
            }

            // 上传每个文件到服务器
            validFiles.forEach(file => {
                uploadFileToServer(file, selectedAnalysisType);
            });

            // 更新统一文件管理器
            updateFileCountBadges();

            // 如果当前在当前上传视图，刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            // 分析按钮现在在文件管理栏中，无需单独显示

            checkAnalyzeButton();

            // 单文件自动分析逻辑
            const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
            const isAutoAnalysisEnabled = autoAnalysisSwitch ? autoAnalysisSwitch.checked : false;

            // 获取当前类型的文件数量
            const currentTypeFiles = getCurrentTypeFiles();

            if (validFiles.length === 1 && currentTypeFiles.length === 1 && selectedAnalysisType && isAutoAnalysisEnabled) {
                showAutoAnalysisMessage(getAnalysisTypeName(selectedAnalysisType), validFiles[0].name);
                // 延迟一点时间让用户看到消息，然后自动开始分析
                setTimeout(() => {
                    startAnalysis();
                }, 1500);
            } else {
                if (validFiles.length === 1) {
                    showMessage(`成功添加文件 ${validFiles[0].name}`, 'success');
                    // 如果是单文件且自动分析关闭，提醒用户可以手动分析
                    if (selectedAnalysisType && !isAutoAnalysisEnabled) {
                        setTimeout(() => {
                            showMessage('💡 提示：您可以点击"开始分析"按钮进行分析，或开启"自动分析"开关', 'info', 4000);
                        }, 1000);
                    }
                } else {
                    showMessage(`成功添加 ${validFiles.length} 个文件`, 'success');
                    if (selectedAnalysisType) {
                        setTimeout(() => {
                            showMessage('💡 提示：您可以点击"开始分析"按钮进行批量分析', 'info', 3000);
                        }, 1000);
                    }
                }
            }
        }
    }

    // 上传文件到服务器
    function uploadFileToServer(file, analysisType) {
        console.log('🔍 uploadFileToServer 被调用:', {
            fileName: file.name,
            analysisType: analysisType,
            fileSize: file.size
        });

        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', analysisType);

        // 创建临时的本地文件记录
        const tempFileId = `temp_${++fileIdCounter}`;
        const fileRecord = {
            fileId: tempFileId,
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'uploading', // uploading, uploaded, pending, analyzing, analyzed, failed
            uploadTime: new Date(),
            created_at: new Date(), // 初始化创建时间
            analysisResult: null,
            recordId: null,
            selected: true,
            analysisType: analysisType,
            accuracy_score: null // 初始化准确率字段
        };

        // 添加到对应类型的文件记录中
        if (!fileRecordsByType[analysisType]) {
            fileRecordsByType[analysisType] = [];
        }
        fileRecordsByType[analysisType].push(fileRecord);

        // 添加到对应类型的文件列表中（用于显示）
        const fileItem = {
            fileId: tempFileId,
            name: file.name,
            size: file.size,
            type: file.type,
            uploadTime: fileRecord.uploadTime,
            created_at: fileRecord.uploadTime, // 初始化创建时间
            status: 'uploading',
            file_status: 'active',
            file: file,
            selected: true,
            analysisType: analysisType,
            accuracy_score: null, // 初始化准确率字段
            recordId: null // 初始化记录ID字段
        };

        if (!filesByType[analysisType]) {
            filesByType[analysisType] = [];
        }
        filesByType[analysisType].push(fileItem);

        // 刷新显示
        updateFileCountBadges();
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 发送上传请求
        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 保存原有的selected状态
                const originalSelected = fileRecord.selected;

                // 更新文件记录为真实的记录ID和所有服务器返回的字段
                fileRecord.recordId = data.data.id;
                fileRecord.fileId = data.data.id; // 使用真实的记录ID作为fileId
                fileRecord.status = data.data.status;
                fileRecord.accuracy_score = data.data.accuracy_score; // 同步准确率
                fileRecord.created_at = data.data.created_at; // 同步创建时间
                fileRecord.selected = originalSelected; // 保持选中状态

                // 同步更新文件列表项
                fileItem.fileId = data.data.id;
                fileItem.status = data.data.status;
                fileItem.accuracy_score = data.data.accuracy_score; // 同步准确率
                fileItem.created_at = data.data.created_at; // 同步创建时间
                fileItem.uploadTime = data.data.created_at; // 兼容性字段
                fileItem.selected = originalSelected; // 保持选中状态

                showMessage(`文件 ${file.name} 上传成功`, 'success');
            } else {
                fileRecord.status = 'failed';
                fileItem.status = 'failed';
                showMessage(`文件 ${file.name} 上传失败: ${data.message}`, 'error');
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
            checkAnalyzeButton();
        })
        .catch(error => {
            console.error('文件上传失败:', error);
            fileRecord.status = 'failed';
            fileItem.status = 'failed';
            showMessage(`文件 ${file.name} 上传失败`, 'error');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        });
    }

    // 更新文件列表显示（统一文件管理器）
    function updateFileList() {
        // 更新统一文件管理器
        updateFileCountBadges();

        // 如果当前在当前上传视图，刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 检查分析按钮状态
        checkAnalyzeButton();
    }

    // 创建文件项
    function createFileItem(record) {
        const fileItem = document.createElement('div');
        fileItem.className = `file-item ${record.status}`;
        fileItem.dataset.fileId = record.id;

        // 选择框
        const fileCheckbox = document.createElement('div');
        fileCheckbox.className = 'file-checkbox';
        fileCheckbox.innerHTML = `
            <input type="checkbox"
                   id="file-${record.id}"
                   ${record.selected ? 'checked' : ''}
                   ${record.status === 'deprecated' ? 'disabled' : ''}
                   onchange="toggleFileSelection(${record.id}, this.checked)">
        `;

        // 文件图标和状态徽章
        const fileIcon = document.createElement('div');
        fileIcon.className = 'file-icon';

        const iconClass = record.type.includes('pdf') ? 'bi-file-pdf' : 'bi-file-image';
        const iconBg = record.type.includes('pdf') ? 'file-pdf' : 'file-image';

        fileIcon.innerHTML = `
            <div class="${iconBg}">
                <i class="bi ${iconClass}"></i>
            </div>
            ${getStatusBadge(record.status)}
        `;

        // 文件信息
        const fileInfo = document.createElement('div');
        fileInfo.className = 'file-info';

        const uploadTime = record.uploadTime.toLocaleString('zh-CN');
        const fileSize = (record.size / 1024 / 1024).toFixed(2);

        fileInfo.innerHTML = `
            <div class="file-name">${record.name}</div>
            <div class="file-meta">
                <span><i class="bi bi-hdd me-1"></i>${fileSize} MB</span>
                <span><i class="bi bi-clock me-1"></i>${uploadTime}</span>
                <span class="status-text">${getStatusText(record.status)}</span>
            </div>
        `;

        // 文件操作按钮
        const fileActions = document.createElement('div');
        fileActions.className = 'file-actions';
        fileActions.innerHTML = getActionButtons(record);

        fileItem.appendChild(fileCheckbox);
        fileItem.appendChild(fileIcon);
        fileItem.appendChild(fileInfo);
        fileItem.appendChild(fileActions);

        return fileItem;
    }

    // 获取状态徽章
    function getStatusBadge(status) {
        const badges = {
            'pending': '',
            'analyzing': '<div class="file-status-badge analyzing"><i class="bi bi-arrow-clockwise"></i></div>',
            'analyzed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'failed': '<div class="file-status-badge" style="background: var(--danger-color);"><i class="bi bi-x"></i></div>',
            'deprecated': '<div class="file-status-badge deprecated"><i class="bi bi-archive"></i></div>'
        };
        return badges[status] || '';
    }

    // 获取操作按钮
    function getActionButtons(record) {
        let buttons = '';

        if (record.status === 'pending') {
            buttons += `<button class="btn-file-action btn-analyze" onclick="analyzeFile(${record.id})">
                <i class="bi bi-cpu"></i>分析
            </button>`;
        }

        if (record.status === 'analyzed') {
            buttons += `<button class="btn-file-action btn-result" onclick="viewResult(${record.id})">
                <i class="bi bi-eye"></i>结果
            </button>`;
            buttons += `<button class="btn-file-action btn-reanalyze" onclick="reanalyzeFile(${record.id})">
                <i class="bi bi-arrow-clockwise"></i>重新分析
            </button>`;
        }

        if (record.status === 'failed') {
            buttons += `<button class="btn-file-action btn-reanalyze" onclick="reanalyzeFile(${record.id})">
                <i class="bi bi-arrow-clockwise"></i>重试
            </button>`;
        }

        if (record.status !== 'deprecated') {
            buttons += `<button class="btn-file-action btn-deprecate" onclick="deprecateFile(${record.id})">
                <i class="bi bi-archive"></i>废弃
            </button>`;
        } else {
            buttons += `<button class="btn-file-action btn-restore" onclick="restoreFile(${record.id})">
                <i class="bi bi-arrow-counterclockwise"></i>恢复
            </button>`;
        }

        return buttons;
    }

    // 切换文件选择状态
    function toggleFileSelection(fileId, isSelected) {
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const record = fileRecords.find(r => r.id === fileId);
        if (record && record.status !== 'deprecated') {
            record.selected = isSelected;
            updateSelectedFiles();
            updateSelectedCount();
            checkAnalyzeButton();
        }
    }

    // 更新选择计数显示
    function updateSelectedCount() {
        const selectedCount = document.getElementById('selectedCount');
        if (selectedCount) {
            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
            const count = fileRecords.filter(r => r.selected && r.status !== 'deprecated').length;
            selectedCount.textContent = `已选择 ${count} 个文件`;
        }
    }

    // 重新分析文件
    function reanalyzeFile(fileId) {
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const record = fileRecords.find(r => r.id === fileId);
        if (record) {
            record.status = 'pending';
            record.analysisResult = null;
            record.recordId = null;
            updateFileList();
            analyzeFile(fileId);
        }
    }

    // 更新本地文件状态 - 用于废弃/恢复操作的即时反馈
    function updateLocalFileStatus(fileId, newStatus) {
        const targetStatus = newStatus === 'deprecated' ? 'deprecated' : 'active';

        // 更新历史文件列表中的状态
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            historyFile.file_status = targetStatus;
        }

        // 更新当前文件列表中的状态
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) {
                file.file_status = targetStatus;
                break;
            }
        }

        // 立即更新页面显示
        updateFileRowStatus(fileId, targetStatus);
    }

    // 立即更新文件行的显示状态
    function updateFileRowStatus(fileId, fileStatus) {
        const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
        if (fileRow) {
            if (fileStatus === 'deprecated') {
                fileRow.classList.add('file-deprecated');
            } else {
                fileRow.classList.remove('file-deprecated');
            }

            // 重新渲染该行的按钮
            const actionsCell = fileRow.querySelector('td:last-child .file-actions');
            if (actionsCell) {
                const isDeprecated = fileStatus === 'deprecated';
                // 简单判断是否有结果（这里可以根据实际需要调整）
                const hasResult = false; // 暂时设为false，可以根据文件状态判断

                actionsCell.innerHTML = `
                    <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                            onclick="analyzeFile('${fileId}')"
                            ${!isDeprecated ? '' : 'disabled'}>
                        <i class="bi bi-cpu"></i>
                        ${hasResult ? '重分析' : '分析'}
                    </button>
                    <button class="btn btn-view-result btn-sm"
                            onclick="viewResult('${fileId}')"
                            ${hasResult ? '' : 'disabled'}>
                        <i class="bi bi-eye"></i>
                        查看结果
                    </button>
                    ${!isDeprecated ? `
                        <button class="btn btn-deprecate btn-sm"
                                onclick="deprecateFile('${fileId}')">
                            <i class="bi bi-archive"></i>
                            废弃
                        </button>
                    ` : `
                        <button class="btn btn-restore btn-sm"
                                onclick="restoreFile('${fileId}')">
                            <i class="bi bi-arrow-counterclockwise"></i>
                            恢复
                        </button>
                    `}
                `;
            }
        }
    }

    // 更新选中的文件列表
    function updateSelectedFiles() {
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        selectedFiles = fileRecords
            .filter(r => r.status !== 'deprecated' && r.selected)
            .map(r => r.file);
    }

    // 全选文件
    function selectAllFiles() {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = getCurrentTypeFiles();
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        currentTypeFiles.forEach(file => {
            if (file.status !== 'deprecated') {
                file.selected = true;
            }
        });

        fileRecords.forEach(record => {
            if (record.status !== 'deprecated') {
                record.selected = true;
            }
        });

        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
        showMessage('已全选所有可用文件', 'info');
    }

    // 取消全选文件
    function deselectAllFiles() {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = getCurrentTypeFiles();
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        currentTypeFiles.forEach(file => {
            file.selected = false;
        });

        fileRecords.forEach(record => {
            record.selected = false;
        });

        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
        showMessage('已取消选择所有文件', 'info');
    }

    // 检查分析按钮状态
    function checkAnalyzeButton() {
        const analyzeBtn = document.getElementById('analyzeBtn');

        if (analyzeBtn) {
            let availableFiles = [];
            let canAnalyze = false;

            if (currentFileView === 'history') {
                // 历史记录视图：检查选中的历史文件
                const selectedCheckboxes = document.querySelectorAll('#historyFilesTableBody .file-checkbox:checked');
                availableFiles = Array.from(selectedCheckboxes).map(checkbox => ({
                    fileId: checkbox.value,
                    name: checkbox.closest('tr').querySelector('td:nth-child(2)').textContent.trim()
                }));
                canAnalyze = availableFiles.length > 0;
            } else {
                // 当前文件视图：使用原有逻辑
                const currentTypeFiles = getCurrentTypeFiles();
                availableFiles = currentTypeFiles.filter(f => f.status !== 'deprecated' && f.selected);
                canAnalyze = selectedAnalysisType && availableFiles.length > 0;
            }

            analyzeBtn.disabled = !canAnalyze;

            // 根据文件数量更新按钮文本和样式
            if (canAnalyze) {
                if (availableFiles.length === 1) {
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu me-1"></i>开始分析';
                    analyzeBtn.title = '开始智能分析';
                } else {
                    analyzeBtn.innerHTML = `<i class="bi bi-cpu me-1"></i>批量分析 (${availableFiles.length})`;
                    analyzeBtn.title = `批量分析${availableFiles.length}个文件`;
                }
                analyzeBtn.className = 'btn btn-sm btn-success';
            } else {
                analyzeBtn.innerHTML = '<i class="bi bi-cpu me-1"></i>开始分析';
                if (currentFileView === 'history') {
                    analyzeBtn.title = '请先选择要分析的历史文件';
                } else {
                    analyzeBtn.title = '请先选择分析类型和上传文件';
                }
                analyzeBtn.className = 'btn btn-sm btn-outline-secondary';
            }
        }
    }

    // 开始分析
    function startAnalysis() {
        let availableFiles = [];

        if (currentFileView === 'history') {
            // 历史记录视图：获取选中的历史文件
            const selectedCheckboxes = document.querySelectorAll('#historyFilesTableBody .file-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                showMessage('请先选择要分析的历史文件', 'warning');
                return;
            }

            // 从选中的复选框获取文件信息
            availableFiles = Array.from(selectedCheckboxes).map(checkbox => {
                const row = checkbox.closest('tr');
                const fileId = checkbox.value;
                const fileName = row.querySelector('td:nth-child(2)').textContent.trim();
                const analysisType = row.querySelector('.analysis-type-badge').textContent.trim();

                return {
                    fileId: fileId,
                    name: fileName,
                    analysisType: analysisType
                };
            });
        } else {
            // 当前文件视图：使用原有逻辑
            if (!selectedAnalysisType) {
                showMessage('请选择分析类型', 'warning');
                return;
            }

            const currentTypeFiles = getCurrentTypeFiles();
            availableFiles = currentTypeFiles.filter(file => file.selected && file.status !== 'deprecated');
            if (availableFiles.length === 0) {
                showMessage('请先上传文件或选择可用文件', 'warning');
                return;
            }
        }

        // 禁用分析按钮并更新状态
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.disabled = true;

            const fileCount = availableFiles.length;
            if (fileCount === 1) {
                analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>正在分析...';
                showMessage(`正在分析文件：${availableFiles[0].name}`, 'info');
            } else {
                analyzeBtn.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>正在批量分析 (${fileCount}个文件)...`;
                showMessage(`正在批量分析${fileCount}个文件`, 'info');
            }
        }

        if (currentFileView === 'current') {
            // 当前文件视图：更新文件状态为分析中
            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
            availableFiles.forEach(file => {
                file.status = 'analyzing';
                // 同时更新fileRecords中对应的记录
                const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                if (fileRecord) {
                    fileRecord.status = 'analyzing';
                }
            });

            // 刷新显示
            displayCurrentFiles();
        }

        // 发送分析请求
        let analysisPromise;

        console.log('=== 准备发送分析请求 ===');
        console.log('文件数量:', availableFiles.length);
        console.log('当前视图:', currentFileView);

        if (currentFileView === 'history') {
            // 历史记录文件分析：直接使用文件ID进行分析
            if (availableFiles.length === 1) {
                const file = availableFiles[0];
                console.log('发送历史文件分析请求, fileId:', file.fileId, 'analysisType:', file.analysisType);
                analysisPromise = API.post(`/api/files/${file.fileId}/analyze`, {
                    analysis_type: file.analysisType,
                    enable_seal_recognition: false
                });
            } else {
                // 批量分析历史文件 - 改用批量API避免并发压力
                const fileIds = availableFiles.map(file => file.fileId);
                // 历史文件可能有不同的分析类型，使用第一个文件的分析类型
                const analysisType = availableFiles[0].analysisType;
                console.log('发送批量历史文件分析请求, fileIds:', fileIds, 'analysisType:', analysisType);
                analysisPromise = API.post('/api/files/batch-analyze', {
                    file_ids: fileIds,
                    analysis_type: analysisType
                });
            }
        } else {
            // 当前文件视图：使用原有逻辑
            if (availableFiles.length === 1) {
                console.log('发送单文件分析请求, fileId:', availableFiles[0].fileId);
                analysisPromise = API.post(`/api/files/${availableFiles[0].fileId}/analyze`, {
                    analysis_type: selectedAnalysisType,
                    enable_seal_recognition: false
                });
            } else {
                const fileIds = availableFiles.map(file => file.fileId);
                console.log('发送批量分析请求, fileIds:', fileIds);
                analysisPromise = API.post('/api/files/batch-analyze', {
                    file_ids: fileIds,
                    analysis_type: selectedAnalysisType
                });
            }
        }

        console.log('分析请求已发送，等待响应...');

        analysisPromise.then(data => {
            console.log('=== 进入分析结果处理 ===');
            const fileCount = availableFiles.length;

            // 调试：打印分析API的返回数据
            console.log('分析API返回数据:', data);
            console.log('当前视图:', currentFileView);
            console.log('文件数量:', fileCount);

            // 处理批量分析结果（统一格式）
            if (fileCount > 1 && data && data.success && data.record_ids) {
                console.log('处理批量分析结果');
                const successCount = data.data?.success_count || data.record_ids.length;
                const failedCount = data.data?.failed_count || 0;
                const recordIds = data.record_ids;

                console.log(`批量分析完成：成功 ${successCount} 个，失败 ${failedCount} 个`);

                if (successCount > 0) {
                    showMessage(`批量分析完成：成功 ${successCount} 个，失败 ${failedCount} 个`, 'success');

                    // 使用统一的刷新函数
                    refreshAfterBatchAnalysis(recordIds);
                } else {
                    showMessage('批量分析失败，所有文件都分析失败', 'error');

                    // 即使失败也要刷新显示
                    refreshAfterBatchAnalysis(null);
                }
            }
            // 处理单个结果（单文件分析或批量分析API）
            else if (data && typeof data === 'object' && !Array.isArray(data)) {
                console.log('处理单个分析结果');
                console.log('data.success:', data.success);
                console.log('data.record_ids:', data.record_ids);
                console.log('data.data:', data.data);
                console.log('data.data?.id:', data.data?.id);

                if (data.success) {
                    // 更新文件状态 - 分析完成后状态改为待审核
                    if (data.record_ids && data.record_ids.length > 0) {
                        // 批量分析返回格式
                        data.record_ids.forEach((recordId, index) => {
                            if (availableFiles[index]) {
                                availableFiles[index].status = 'pending_audit';
                                availableFiles[index].recordId = recordId;

                                // 同时更新fileRecords中对应的记录
                                const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                                const fileRecord = fileRecords.find(r => r.fileId === availableFiles[index].fileId);
                                if (fileRecord) {
                                    fileRecord.status = 'pending_audit';
                                    fileRecord.recordId = recordId;
                                }
                            }
                        });

                        // 批量分析完成后，使用统一的刷新函数
                        refreshAfterBatchAnalysis(data.record_ids);
                    } else if (data.data && data.data.id && availableFiles.length === 1) {
                        // 单文件分析返回格式
                        availableFiles[0].status = 'pending_audit';
                        availableFiles[0].recordId = data.data.id;
                        // 同步准确率
                        if (data.data.accuracy_score !== undefined) {
                            availableFiles[0].accuracy_score = data.data.accuracy_score;
                        }

                        // 同时更新fileRecords中对应的记录
                        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                        const fileRecord = fileRecords.find(r => r.fileId === availableFiles[0].fileId);
                        if (fileRecord) {
                            fileRecord.status = 'pending_audit';
                            fileRecord.recordId = data.data.id;
                            // 同步准确率
                            if (data.data.accuracy_score !== undefined) {
                                fileRecord.accuracy_score = data.data.accuracy_score;
                            }
                        }

                        // 单文件分析也需要从服务器同步最新数据
                        setTimeout(() => {
                            console.log('单文件分析完成，开始同步文件数据，record_id:', data.data.id);
                            syncFileDataFromServer([data.data.id]);
                        }, 3000);
                    } else {
                        // 兜底处理：没有recordId的情况
                        availableFiles.forEach(file => {
                            file.status = 'pending_audit';

                            // 同时更新fileRecords中对应的记录
                            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                            const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                            if (fileRecord) {
                                fileRecord.status = 'pending_audit';
                            }
                        });
                    }

                    // 使用统一的刷新函数
                    const recordIds = data.record_ids || (data.data && data.data.id ? [data.data.id] : null);
                    refreshAfterBatchAnalysis(recordIds);

                    if (fileCount === 1) {
                        showMessage('文件分析完成', 'success');
                    } else {
                        showMessage(`${fileCount}个文件批量分析完成`, 'success');
                    }
                } else {
                    // 更新文件状态为失败
                    availableFiles.forEach(file => {
                        file.status = 'failed';

                        // 同时更新fileRecords中对应的记录
                        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                        const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                        if (fileRecord) {
                            fileRecord.status = 'failed';
                        }
                    });

                    // 刷新显示
                    if (currentFileView === 'current') {
                        displayCurrentFiles();
                    }

                    showMessage(data.message || '分析失败', 'error');
                }
            }
            // 处理历史文件单个分析结果（Promise.all中的单个失败）
            else if (currentFileView === 'history' && Array.isArray(data)) {
                console.log('处理历史文件单个分析失败结果');
                showMessage('部分文件分析失败', 'warning');
            }

            // 恢复按钮状态
            const analyzeBtn = document.getElementById('analyzeBtn');
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
            }
            checkAnalyzeButton(); // 重新检查按钮状态
        })
        .catch(error => {
            console.log('=== 进入分析请求错误处理 ===');
            console.error('分析请求失败:', error);
            console.error('错误详情:', error.message, error.stack);

            // 更新文件状态为失败
            availableFiles.forEach(file => {
                file.status = 'failed';

                // 同时更新fileRecords中对应的记录
                const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
                const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                if (fileRecord) {
                    fileRecord.status = 'failed';
                }
            });

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            showMessage('分析请求失败，请重试', 'error');

            // 恢复按钮状态
            const analyzeBtn = document.getElementById('analyzeBtn');
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
            }
            checkAnalyzeButton(); // 重新检查按钮状态

            // 确保状态刷新
            setTimeout(() => {
                console.log('分析失败后刷新状态');
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                } else if (currentFileView === 'history') {
                    loadHistoryFiles();
                }
            }, 1000);
        });
    }

    // ==================== 历史记录功能 ====================

    // 获取当前分析类型
    function getCurrentAnalysisType() {
        // 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const urlType = urlParams.get('analysis_type');
        if (urlType) return urlType;

        // 从页面元素获取
        const analysisTypeSelect = document.getElementById('analysisType');
        if (analysisTypeSelect && analysisTypeSelect.value) return analysisTypeSelect.value;

        // 从全局变量获取
        if (typeof selectedAnalysisType !== 'undefined' && selectedAnalysisType) {
            return selectedAnalysisType;
        }

        // 从页面标题或其他元素推断
        const pageTitle = document.title;
        if (pageTitle.includes('期货账户/开户文件解析')) return 'futures_account';
        if (pageTitle.includes('全部状态')) return 'all_status';

        return '';
    }

    // 获取分析类型文本
    function getAnalysisTypeText(type) {
        // 文件列表中显示简洁的四字名称
        const typeMap = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeMap[type] || type;
    }

    // 获取文件图标类
    function getFileIconClass(filename) {
        if (filename.toLowerCase().includes('.pdf')) {
            return 'file-pdf';
        }
        return 'file-image';
    }

    // 截断文件名
    function truncateFilename(filename, maxLength) {
        if (filename.length <= maxLength) return filename;
        const ext = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - ext.length - 4) + '...';
        return truncatedName + '.' + ext;
    }

    // ==================== 分析结果弹窗功能 ====================

    let currentResultRecordId = null;

    // 显示分析结果弹窗
    function showAnalysisResultModal(recordId) {
        currentResultRecordId = recordId;
        showLoading('正在加载分析结果...');

        // 调用API获取数据
        fetch(`/api/files/${recordId}/result`)
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    showAnalysisResultModalWithData(data.data);
                } else {
                    showMessage('加载分析结果失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载分析结果失败:', error);
                showMessage('加载分析结果失败', 'error');
            });
    }

    // 显示分析结果模态框 - 与文件管理页面完全一致
    function showAnalysisResultModalWithData(resultData) {
        // 设置当前记录ID，用于后续API调用
        currentResultRecordId = resultData.id;
        // 重置原件展示状态
        isAnalysisOriginalFileCollapsed = false;
        const container = document.getElementById('analysisResultColumnsContainer');
        const originalArea = document.getElementById('analysisOriginalFileArea');
        const toggleBtn = document.getElementById('analysisToggleOriginalBtn');
        const expandBtn = document.getElementById('analysisExpandOriginalBtn');

        if (container && originalArea && toggleBtn && expandBtn) {
            container.style.gridTemplateColumns = '1fr 450px';
            originalArea.style.display = 'flex';
            originalArea.style.opacity = '1';
            toggleBtn.style.display = 'flex';
            expandBtn.style.display = 'none';
        }

        // 设置文件名
        document.getElementById('analysisModalFileName').textContent = resultData.filename;

        // 更新统计信息
        updateAnalysisStatsSection(resultData);

        // 使用新的字段结构化渲染
        renderFieldStructuredContent(resultData);

        // 渲染原件展示
        renderAnalysisOriginalFile(resultData);

        // 设置审核评论
        const auditCommentElement = document.getElementById('analysisAuditComment');
        if (auditCommentElement) {
            auditCommentElement.value = resultData.audit_comment || resultData.review_comment || '';
        }

        // 根据文件状态显示相应的按钮
        updateAnalysisActionButtons(resultData.status);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('analysisResultModal'));
        modal.show();
    }

    // 根据文件状态更新操作按钮显示
    function updateAnalysisActionButtons(status) {
        const auditButtons = document.querySelectorAll('.audit-buttons');
        const reviewButtons = document.querySelectorAll('.review-buttons');

        // 隐藏所有按钮
        auditButtons.forEach(btn => btn.style.display = 'none');
        reviewButtons.forEach(btn => btn.style.display = 'none');

        // 根据状态显示相应按钮
        if (status === 'pending_audit') {
            // 待审核状态：显示审核按钮
            auditButtons.forEach(btn => btn.style.display = 'inline-block');
        } else if (status === 'pending_review') {
            // 待复核状态：显示复核按钮
            reviewButtons.forEach(btn => btn.style.display = 'inline-block');
        }
        // 其他状态（completed, audit_rejected, review_rejected等）不显示操作按钮
    }

    // 计算对象中的字段数量（递归）
    function countFieldsInObject(obj, maxDepth = 3, currentDepth = 0) {
        if (!obj || typeof obj !== 'object' || currentDepth >= maxDepth) {
            return 0;
        }

        let count = 0;

        if (Array.isArray(obj)) {
            // 数组：计算所有元素的字段数
            for (const item of obj) {
                if (typeof item === 'object' && item !== null) {
                    count += countFieldsInObject(item, maxDepth, currentDepth + 1);
                } else {
                    count += 1; // 基本类型也算一个字段
                }
            }
        } else {
            // 对象：计算所有属性
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'object' && value !== null) {
                    // 嵌套对象或数组
                    const nestedCount = countFieldsInObject(value, maxDepth, currentDepth + 1);
                    count += nestedCount > 0 ? nestedCount : 1; // 至少算作1个字段
                } else {
                    // 基本类型字段
                    count += 1;
                }
            }
        }

        return count;
    }

    // 更新分析统计信息区域 - 与文件管理页面完全一致
    function updateAnalysisStatsSection(resultData) {
        // 初始化默认值
        let analysisTypeName = '未知类型';
        let fileAccuracy = 0;
        let fieldAccuracy = 0;
        let correctFields = 0;
        let totalFields = 0;

        // 1. 分析类型名称
        if (resultData.analysis_type_name) {
            analysisTypeName = resultData.analysis_type_name;
        } else if (resultData.analysis_type) {
            // 如果没有显示名称，使用标准映射 - 只支持标准类型
            const typeNames = {
                'futures_account': '期货账户/开户文件解析',
                'wealth_management': '理财产品说明书',
                'broker_interest': '券商账户计息变更',
                'account_opening': '账户开户场景',
                'ningyin_fee': '宁银理财费用变更',
                'non_standard_trade': '非标交易确认单解析'
            };
            analysisTypeName = typeNames[resultData.analysis_type] || resultData.analysis_type;
        }

        // 2. 优先使用API返回的统计信息
        if (resultData.stats) {
            fileAccuracy = resultData.stats.file_accuracy_rate || 0;
            fieldAccuracy = resultData.stats.field_accuracy_rate || 0;
            correctFields = resultData.stats.correct_fields || 0;
            totalFields = resultData.stats.total_fields || 0;
        } else {
            // 3. 兜底逻辑：从其他字段获取
            // 文件正确率
            if (resultData.accuracy_score !== undefined && resultData.accuracy_score !== null) {
                fileAccuracy = resultData.accuracy_score;
            }

            // 字段正确率 - 与文件管理页面完全一致的处理逻辑
            if (resultData.field_accuracy !== undefined && resultData.field_accuracy !== null) {
                if (typeof resultData.field_accuracy === 'number' && !isNaN(resultData.field_accuracy)) {
                    fieldAccuracy = resultData.field_accuracy;
                } else if (typeof resultData.field_accuracy === 'object') {
                    fieldAccuracy = resultData.field_accuracy.accuracy_score ||
                                   resultData.field_accuracy.overall_accuracy ||
                                   resultData.field_accuracy.match_rate ||
                                   resultData.field_accuracy.accuracy || 0;
                }
            } else if (resultData.overall_accuracy !== undefined && resultData.overall_accuracy !== null) {
                fieldAccuracy = resultData.overall_accuracy;
            }
        }

        // 4. 如果没有预期结果，尝试从AI结果中计算基本统计信息
        if (totalFields === 0 && correctFields === 0) {
            try {
                const aiResult = resultData.ai_result ?
                    (typeof resultData.ai_result === 'string' ? JSON.parse(resultData.ai_result) : resultData.ai_result) : null;
                const expectedResult = resultData.expected_result ?
                    (typeof resultData.expected_result === 'string' ? JSON.parse(resultData.expected_result) : resultData.expected_result) : null;

                if (aiResult && !expectedResult) {
                    // 没有预期结果时，计算AI结果的字段数量
                    const aiFieldCount = countFieldsInObject(aiResult);
                    totalFields = aiFieldCount;
                    // 没有预期结果时，无法计算正确字段数，显示为待评估
                    correctFields = 0;
                    fieldAccuracy = 0; // 设为0表示待评估
                } else if (aiResult && expectedResult) {
                    // 有预期结果时，如果前面没有获取到统计信息，尝试简单计算
                    const aiFieldCount = countFieldsInObject(aiResult);
                    const expectedFieldCount = countFieldsInObject(expectedResult);
                    totalFields = Math.max(aiFieldCount, expectedFieldCount);
                }
            } catch (e) {
                console.warn('计算AI结果字段数量失败:', e);
            }
        }

        // 3. 解析对比结果获取字段统计 - 与文件管理页面完全一致
        if (resultData.comparison_result) {
            try {
                const comparisonData = typeof resultData.comparison_result === 'string'
                    ? JSON.parse(resultData.comparison_result)
                    : resultData.comparison_result;
                // 尝试从不同的数据结构中获取字段统计
                if (comparisonData.total_fields !== undefined && comparisonData.matched_fields !== undefined) {
                    // 直接从根级别获取
                    totalFields = comparisonData.total_fields || 0;
                    correctFields = comparisonData.matched_fields || 0;
                } else if (comparisonData.field_comparison) {
                    totalFields = comparisonData.field_comparison.total_fields || 0;
                    correctFields = comparisonData.field_comparison.correct_fields || 0;
                } else if (comparisonData.summary) {
                    totalFields = comparisonData.summary.total_fields || 0;
                    correctFields = comparisonData.summary.correct_fields || 0;
                } else if (comparisonData.statistics) {
                    totalFields = comparisonData.statistics.total_fields || 0;
                    correctFields = comparisonData.statistics.correct_fields || 0;
                } else {
                    // 如果没有直接的统计信息，尝试从字段详情中计算
                    if (comparisonData.field_comparisons && Array.isArray(comparisonData.field_comparisons)) {
                        totalFields = comparisonData.field_comparisons.length;
                        correctFields = comparisonData.field_comparisons.filter(field => field.match === true || field.is_match === true).length;
                    } else if (comparisonData.fields && Array.isArray(comparisonData.fields)) {
                        totalFields = comparisonData.fields.length;
                        correctFields = comparisonData.fields.filter(field => field.match === true || field.is_match === true).length;
                    } else if (comparisonData.field_comparison && typeof comparisonData.field_comparison === 'object') {
                        // 对象形式的字段对比
                        totalFields = Object.keys(comparisonData.field_comparison).length;
                        correctFields = Object.values(comparisonData.field_comparison).filter(field => field.match === true).length;
                    }
                }

                // 如果获取到了字段统计，重新计算字段正确率
                if (totalFields > 0 && fieldAccuracy === 0) {
                    fieldAccuracy = correctFields / totalFields;
                }

            } catch (e) {
                console.warn('解析对比结果失败:', e);
            }
        }

        // 更新显示
        document.getElementById('analysisFileAccuracy').textContent = analysisTypeName;

        // 字段正确率显示逻辑
        if (fieldAccuracy > 0) {
            document.getElementById('analysisFieldAccuracy').textContent = `${(fieldAccuracy * 100).toFixed(1)}%`;
        } else if (totalFields > 0 && correctFields === 0) {
            // 有总字段数但没有正确字段数，说明没有预期结果
            document.getElementById('analysisFieldAccuracy').textContent = '待评估';
        } else {
            document.getElementById('analysisFieldAccuracy').textContent = '-';
        }

        // 正确字段数显示逻辑
        if (correctFields > 0) {
            document.getElementById('analysisCorrectFields').textContent = correctFields;
        } else if (totalFields > 0) {
            // 有总字段数但没有正确字段数，说明没有预期结果
            document.getElementById('analysisCorrectFields').textContent = '待评估';
        } else {
            document.getElementById('analysisCorrectFields').textContent = '-';
        }

        // 总字段数显示
        document.getElementById('analysisTotalFields').textContent = totalFields > 0 ? totalFields : '-';
    }

    // 渲染分析字段结构化内容 - 与文件管理页面完全一致
    function renderAnalysisFieldStructuredContent(resultData) {
        const container = document.getElementById('analysisFourColumnsContentContainer');
        if (!container) {
            console.error('找不到分析四列容器');
            return;
        }

        let html = '';

        try {
            // 解析数据
            const aiResult = resultData.ai_result ?
                (typeof resultData.ai_result === 'string' ? JSON.parse(resultData.ai_result) : resultData.ai_result) : null;
            const expectedResult = resultData.expected_result ?
                (typeof resultData.expected_result === 'string' ? JSON.parse(resultData.expected_result) : resultData.expected_result) : null;
            const comparisonResult = resultData.comparison_result ?
                (typeof resultData.comparison_result === 'string' ? JSON.parse(resultData.comparison_result) : resultData.comparison_result) : null;
            // 如果有对比结果，使用对比结果渲染
            if (comparisonResult && comparisonResult.field_comparison) {
                html = renderAnalysisComparisonFields(comparisonResult.field_comparison);
            }
            // 否则显示AI结果
            else if (aiResult && aiResult.result) {
                html = renderAnalysisAIOnlyFields(aiResult.result);
            }
            // 检查是否有其他可能的数据结构
            else if (aiResult) {
                // 尝试直接使用aiResult作为字段数据
                html = renderAnalysisAIOnlyFields(aiResult);
            }
            // 最后显示原始数据
            else {
                html = `
                    <div class="text-center p-4 text-muted">
                        <i class="bi bi-info-circle display-4"></i>
                        <p class="mt-2">暂无结构化数据可显示</p>
                        <p class="small">请查看原始分析结果</p>
                    </div>
                `;
            }

        } catch (error) {
            console.error('渲染字段时出错:', error);
            html = `
                <div class="text-center p-4 text-danger">
                    <i class="bi bi-exclamation-triangle display-4"></i>
                    <p class="mt-2">数据解析失败</p>
                    <p class="small">${error.message}</p>
                </div>
            `;
        }

        container.innerHTML = html;
    }

    // 渲染分析对比字段 - 与文件管理页面完全一致
    function renderAnalysisComparisonFields(fieldComparison) {
        let html = '';

        for (const [fieldName, fieldData] of Object.entries(fieldComparison)) {
            const isMatch = fieldData.match === true;

            // 处理可能是对象的值
            const formatValue = (value) => {
                if (value === null || value === undefined) return '/';
                if (typeof value === 'object') {
                    // 如果是对象，尝试转换为字符串
                    if (value.toString && value.toString() !== '[object Object]') {
                        return value.toString();
                    }
                    return JSON.stringify(value);
                }
                return String(value);
            };

            const aiValue = formatValue(fieldData.ai_value);
            const expectedValue = formatValue(fieldData.expected_value);

            html += `
                <div class="four-column-data-row" style="display: grid; grid-template-columns: 1.5fr 2fr 2fr 1fr; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <div class="field-row-aligned field-name" style="padding: 0.5rem; border-left: 3px solid #6366f1; background: var(--gray-50); border-radius: 4px; display: flex; align-items: center;">
                        <div style="font-weight: 500; word-break: break-word;">${fieldName}</div>
                    </div>
                    <div class="field-row-aligned ai-value" style="padding: 0.5rem; border-left: 3px solid #3b82f6; background: #e3f2fd; border-radius: 4px; display: flex; align-items: center;">
                        <div style="word-break: break-word;">${aiValue}</div>
                    </div>
                    <div class="field-row-aligned expected-value" style="padding: 0.5rem; border-left: 3px solid #10b981; background: #e8f5e8; border-radius: 4px; display: flex; align-items: center;">
                        <div style="word-break: break-word;">${expectedValue}</div>
                    </div>
                    <div class="field-row-aligned comparison-result" style="padding: 0.5rem; border-left: 3px solid #f59e0b; background: var(--gray-50); border-radius: 4px; display: flex; align-items: center;">
                        <div style="text-align: center; font-weight: 500; width: 100%;">
                            ${isMatch ?
                                '<span style="color: #059669;"><i class="bi bi-check-circle me-1"></i>匹配</span>' :
                                '<span style="color: #dc2626;"><i class="bi bi-x-circle me-1"></i>不匹配</span>'
                            }
                        </div>
                    </div>
                </div>
            `;
        }

        return html;
    }

    // 渲染分析AI字段（仅AI结果）
    function renderAnalysisAIOnlyFields(aiResult) {
        let html = '';

        // 处理可能是对象的值
        const formatValue = (value) => {
            if (value === null || value === undefined) return '/';
            if (typeof value === 'object') {
                // 如果是对象，尝试转换为字符串
                if (value.toString && value.toString() !== '[object Object]') {
                    return value.toString();
                }
                return JSON.stringify(value);
            }
            return String(value);
        };

        for (const [fieldName, fieldValue] of Object.entries(aiResult)) {
            const formattedValue = formatValue(fieldValue);

            html += `
                <div class="four-column-data-row" style="display: grid; grid-template-columns: 1.5fr 2fr 2fr 1fr; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <div class="field-row-aligned field-name" style="padding: 0.5rem; border-left: 3px solid #6366f1; background: var(--gray-50); border-radius: 4px; display: flex; align-items: center;">
                        <div style="font-weight: 500; word-break: break-word;">${fieldName}</div>
                    </div>
                    <div class="field-row-aligned ai-value" style="padding: 0.5rem; border-left: 3px solid #3b82f6; background: #e3f2fd; border-radius: 4px; display: flex; align-items: center;">
                        <div style="word-break: break-word;">${formattedValue}</div>
                    </div>
                    <div class="field-row-aligned expected-value" style="padding: 0.5rem; border-left: 3px solid #10b981; background: #e8f5e8; border-radius: 4px; display: flex; align-items: center;">
                        <div style="color: #6b7280;">-</div>
                    </div>
                    <div class="field-row-aligned comparison-result" style="padding: 0.5rem; border-left: 3px solid #f59e0b; background: var(--gray-50); border-radius: 4px; display: flex; align-items: center;">
                        <div style="text-align: center; font-weight: 500; width: 100%;">
                            <span style="color: #6b7280;">-</span>
                        </div>
                    </div>
                </div>
            `;
        }

        return html;
    }

    // 分析页面合并记录原件文件切换相关变量
    let analysisCurrentMergedFiles = [];
    let analysisCurrentFileIndex = 0;
    let analysisIsShowingMergedFiles = false;

    // 渲染分析原件展示
    function renderAnalysisOriginalFile(resultData) {
        // 设置全局变量
        currentResultRecordId = resultData.id;

        // 检查是否是账户开户场景，如果是则尝试加载合并文件
        if (resultData.analysis_type === 'account_opening') {
            loadAnalysisMergedFiles(resultData);
        } else {
            renderAnalysisSingleOriginalFile(resultData);
        }
    }

    // 渲染单个分析原件文件
    function renderAnalysisSingleOriginalFile(resultData) {
        const container = document.getElementById('analysisOriginalFileContent');
        if (!container) {
            console.error('找不到分析原件展示容器');
            return;
        }

        // 隐藏文件切换控件
        hideAnalysisFileSelectionControls();

        // 清空现有内容
        container.innerHTML = '';

        // 检查是否有文件信息
        const hasFileInfo = resultData.file_info && resultData.file_info.upload_path;
        const fileName = resultData.filename || '';
        const fileExtension = fileName.split('.').pop().toLowerCase();

        if (hasFileInfo || resultData.file_path || resultData.original_file_url) {

            // 显示加载指示器
            container.innerHTML = `
                <div class="text-center text-muted">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载原件文件...</p>
                </div>
            `;

            // 使用新的API获取文件数据
            fetch(`/api/files/${currentResultRecordId}/original-data`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fileData = data.data;
                        // 根据文件类型显示不同内容
                        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileData.file_type.replace('.', ''))) {
                            // 图片文件
                            const img = document.createElement('img');
                            img.src = fileData.data_url;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'contain';
                            img.style.display = 'block';
                            img.alt = '原件图片';
                            img.onload = function() {
                                // 图片加载完成
                            };
                            img.onerror = function() {
                                console.error('图片加载失败');
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                        <p class="mt-2">图片加载失败</p>
                                        <small class="text-muted">文件: ${fileName}</small>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(img);
                        } else if (fileData.file_type === '.pdf') {
                            // PDF文件 - 使用直接文件URL避免Base64大小限制
                            const iframe = document.createElement('iframe');
                            iframe.src = `/api/files/${currentResultRecordId}/original`;
                            iframe.style.width = '100%';
                            iframe.style.height = '100%';
                            iframe.style.border = 'none';
                            iframe.style.minHeight = '500px';

                            iframe.onload = function() {
                                // PDF加载完成
                            };

                            iframe.onerror = function() {
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: var(--danger-color);"></i>
                                        <p class="mt-2">PDF预览不可用</p>
                                        <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                        <a href="/api/files/${currentResultRecordId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-download me-1"></i>下载查看
                                        </a>
                                    </div>
                                `;
                            };

                            container.innerHTML = '';
                            container.appendChild(iframe);
                        } else {
                            // 其他文件类型
                            container.innerHTML = `
                                <div class="text-center text-muted">
                                    <i class="bi bi-file-earmark" style="font-size: 3rem; color: var(--info-color);"></i>
                                    <p class="mt-2">不支持预览此文件类型</p>
                                    <small class="text-muted">文件类型: ${fileData.file_type}</small>
                                </div>
                            `;
                        }
                    } else {
                        console.error('获取文件数据失败:', data.message);
                        container.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                <p class="mt-2">加载原件失败</p>
                                <small class="text-muted">${data.message}</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取文件数据失败:', error);
                    container.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-wifi-off" style="font-size: 3rem; color: var(--danger-color);"></i>
                            <p class="mt-2">网络错误</p>
                            <small class="text-muted">无法加载原件</small>
                        </div>
                    `;
                });
        } else {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-file-earmark-x" style="font-size: 3rem; color: var(--secondary-color);"></i>
                    <p class="mt-2">原件文件不可用</p>
                    <small class="text-muted">文件可能已被删除或移动</small>
                </div>
            `;
        }
    }

    // 格式化预期正确结果
    function formatExpectedResult(expectedResult, recordId) {
        if (!expectedResult || typeof expectedResult !== 'object') {
            return '<div class="text-muted text-center p-3">暂无预期结果</div>';
        }

        let html = '';
        for (const [key, value] of Object.entries(expectedResult)) {
            html += `
                <div class="result-field">
                    <div class="result-field-label">${key}：</div>
                    <div class="result-field-value">
                        <span class="editable-field" data-field="${key}" data-record="${recordId}">
                            ${formatValue(value)}
                        </span>
                    </div>
                </div>
            `;
        }
        return html;
    }

    // 格式化对比结果
    function formatComparisonResult(comparisonResult) {
        if (comparisonResult.overall_accuracy !== undefined) {
            let html = `
                <div class="result-field">
                    <div class="result-field-label">整体准确率：</div>
                    <div class="result-field-value">
                        <span class="result-status ${comparisonResult.overall_accuracy > 0.8 ? 'success' : comparisonResult.overall_accuracy > 0.5 ? 'warning' : 'error'}">
                            ${(comparisonResult.overall_accuracy * 100).toFixed(1)}%
                        </span>
                    </div>
                </div>
            `;

            if (comparisonResult.comparison && comparisonResult.comparison.children) {
                html += '<div class="mt-3"><h6>字段对比详情：</h6>';
                html += formatComparisonChildren(comparisonResult.comparison.children);
                html += '</div>';
            }

            return html;
        }

        return `<div class="result-json">${JSON.stringify(comparisonResult, null, 2)}</div>`;
    }

    // 格式化对比子项
    function formatComparisonChildren(children) {
        let html = '';
        for (const [key, value] of Object.entries(children)) {
            if (value.match !== undefined) {
                html += `
                    <div class="result-field">
                        <div class="result-field-label">${key}：</div>
                        <div class="result-field-value">
                            <span class="result-status ${value.match ? 'success' : 'error'}">
                                ${value.match ? '匹配' : '不匹配'}
                            </span>
                            ${value.ai_value ? `<br><small>AI值: ${value.ai_value}</small>` : ''}
                            ${value.expected_value ? `<br><small>期望值: ${value.expected_value}</small>` : ''}
                        </div>
                    </div>
                `;
            }
        }
        return html;
    }

    // 格式化值
    function formatValue(value) {
        if (value === null || value === undefined) {
            return '<span class="text-muted">无</span>';
        }
        if (typeof value === 'object') {
            return `<div class="result-json">${JSON.stringify(value, null, 2)}</div>`;
        }
        return String(value);
    }

    // 显示结果错误
    function showResultError(message) {
        const contentElement = document.getElementById('analysisResultContent');
        contentElement.innerHTML = `
            <div class="result-content">
                <div class="result-error">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            </div>
        `;
    }

    // 在新窗口打开结果
    function openResultInNewTab() {
        if (currentResultRecordId) {
            window.open(`/records/${currentResultRecordId}`, '_blank');
        }
    }

    // 下载结果
    function downloadResult() {
        if (currentResultRecordId) {
            // 这里可以实现下载功能
            showMessage('下载功能开发中...', 'info');
        }
    }

    // ==================== 预期结果编辑功能 ====================

    let isEditingExpectedResult = false;
    let originalExpectedResult = null;

    // 编辑预期结果
    function editExpectedResult(recordId) {
        if (isEditingExpectedResult) {
            showMessage('正在编辑中，请先保存或取消当前编辑', 'warning');
            return;
        }

        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        isEditingExpectedResult = true;

        // 获取当前数据
        fetch(`/api/files/${recordId}/result`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.expected_result) {
                    const expectedResult = typeof data.data.expected_result === 'string' ?
                        JSON.parse(data.data.expected_result) : data.data.expected_result;

                    originalExpectedResult = JSON.parse(JSON.stringify(expectedResult)); // 深拷贝
                    showEditableExpectedResult(expectedResult, recordId);
                } else {
                    showMessage('获取预期结果失败', 'error');
                    isEditingExpectedResult = false;
                }
            })
            .catch(error => {
                console.error('获取预期结果失败:', error);
                showMessage('获取预期结果失败', 'error');
                isEditingExpectedResult = false;
            });
    }

    // 显示可编辑的预期结果
    function showEditableExpectedResult(expectedResult, recordId) {
        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        let html = '<div class="editable-expected-result">';

        for (const [key, value] of Object.entries(expectedResult)) {
            html += `
                <div class="result-field">
                    <div class="result-field-label">${key}：</div>
                    <div class="result-field-value">
                        <input type="text" class="form-control form-control-sm editable-input"
                               data-field="${key}"
                               value="${escapeHtml(String(value))}"
                               placeholder="请输入${key}">
                    </div>
                </div>
            `;
        }

        html += `
            <div class="mt-3 d-flex gap-2">
                <button class="btn btn-sm btn-success" onclick="saveExpectedResult(${recordId})">
                    <i class="bi bi-check me-1"></i>保存
                </button>
                <button class="btn btn-sm btn-secondary" onclick="cancelEditExpectedResult(${recordId})">
                    <i class="bi bi-x me-1"></i>取消
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="addExpectedField(${recordId})">
                    <i class="bi bi-plus me-1"></i>添加字段
                </button>
            </div>
        </div>`;

        section.innerHTML = html;

        // 添加实时保存功能
        section.querySelectorAll('.editable-input').forEach(input => {
            input.addEventListener('blur', () => autoSaveExpectedResult(recordId));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    autoSaveExpectedResult(recordId);
                }
            });
        });
    }

    // 保存预期结果
    function saveExpectedResult(recordId) {
        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        const inputs = section.querySelectorAll('.editable-input');
        const newExpectedResult = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            const value = input.value.trim();
            if (field && value) {
                newExpectedResult[field] = value;
            }
        });

        // 发送保存请求
        fetch(`/api/files/${recordId}/expected-result`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expected_result: newExpectedResult
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('预期结果保存成功', 'success');
                isEditingExpectedResult = false;
                originalExpectedResult = null;

                // 刷新显示
                section.innerHTML = formatExpectedResult(newExpectedResult, recordId);

                // 更新弹窗标题中的编辑按钮
                const header = section.closest('.result-section').querySelector('.result-section-header button');
                if (header) {
                    header.style.display = 'inline-block';
                }
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            console.error('保存预期结果失败:', error);
            showMessage('保存失败，请重试', 'error');
        });
    }

    // 自动保存预期结果
    function autoSaveExpectedResult(recordId) {
        if (!isEditingExpectedResult) return;

        // 防抖处理
        clearTimeout(window.autoSaveTimeout);
        window.autoSaveTimeout = setTimeout(() => {
            saveExpectedResult(recordId);
        }, 1000);
    }

    // 取消编辑预期结果
    function cancelEditExpectedResult(recordId) {
        if (originalExpectedResult) {
            const section = document.getElementById(`expectedResultSection_${recordId}`);
            if (section) {
                section.innerHTML = formatExpectedResult(originalExpectedResult, recordId);
            }
        }

        isEditingExpectedResult = false;
        originalExpectedResult = null;

        // 显示编辑按钮
        const header = document.querySelector(`#expectedResultSection_${recordId}`).closest('.result-section').querySelector('.result-section-header button');
        if (header) {
            header.style.display = 'inline-block';
        }
    }

    // 添加预期字段
    function addExpectedField(recordId) {
        const fieldName = prompt('请输入字段名称:');
        if (!fieldName || !fieldName.trim()) return;

        const fieldValue = prompt('请输入字段值:');
        if (fieldValue === null) return; // 用户取消

        const section = document.getElementById(`expectedResultSection_${recordId}`);
        const editableDiv = section.querySelector('.editable-expected-result');
        const buttonsDiv = editableDiv.querySelector('.mt-3');

        // 在按钮前添加新字段
        const newFieldHtml = `
            <div class="result-field">
                <div class="result-field-label">${escapeHtml(fieldName.trim())}：</div>
                <div class="result-field-value">
                    <input type="text" class="form-control form-control-sm editable-input"
                           data-field="${escapeHtml(fieldName.trim())}"
                           value="${escapeHtml(fieldValue)}"
                           placeholder="请输入${escapeHtml(fieldName.trim())}">
                </div>
            </div>
        `;

        buttonsDiv.insertAdjacentHTML('beforebegin', newFieldHtml);

        // 为新字段添加事件监听
        const newInput = editableDiv.querySelector(`input[data-field="${fieldName.trim()}"]`);
        if (newInput) {
            newInput.addEventListener('blur', () => autoSaveExpectedResult(recordId));
            newInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    autoSaveExpectedResult(recordId);
                }
            });
            newInput.focus();
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 查看结果对比（在统一文件管理页面中调用）
    function viewResultComparison(recordId) {
        if (typeof resultComparison !== 'undefined') {
            resultComparison.showModal(recordId);
        } else {
            alert('结果对比功能未加载');
        }
    }

    // 更新文件分析状态
    function updateFileAnalysisStatus(fileId, newStatus) {
        // 更新历史文件列表中的状态
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            historyFile.status = newStatus;
        }

        // 更新当前文件列表中的状态
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) {
                file.status = newStatus;
                break;
            }
        }

        // 刷新文件列表显示
        refreshFileList();
    }

    // 检查是否为重分析
    function checkIfReanalyze(fileId) {
        // 在历史记录中查找
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            return ['completed', 'pending_audit', 'pending_review'].includes(historyFile.status);
        }

        // 在当前文件中查找
        const currentFile = getCurrentFileById(fileId);
        if (currentFile) {
            return ['completed', 'pending_audit', 'pending_review'].includes(currentFile.status);
        }

        return false;
    }

    // 获取当前文件
    function getCurrentFileById(fileId) {
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) return file;
        }
        return null;
    }

    // 与文件管理页面一致的辅助函数
    function getStatusClass(status) {
        const classes = {
            'pending': 'status-pending',
            'processing': 'status-processing',
            'analyzing': 'status-processing',
            'completed': 'status-completed',
            'analyzed': 'status-completed',
            'failed': 'status-failed',
            'uploaded': 'status-uploaded',
            'pending_audit': 'status-pending-audit',
            'pending_review': 'status-pending-review',
            'audit_rejected': 'status-failed',
            'review_rejected': 'status-failed',
            'deprecated': 'status-deprecated'
        };
        return classes[status] || 'status-pending';
    }

    function getStatusText(status) {
        const texts = {
            'pending': '待分析',
            'processing': '分析中',
            'analyzing': '分析中',
            'completed': '已完成',
            'analyzed': '已完成',
            'failed': '分析失败',
            'uploaded': '已上传',
            'pending_audit': '待审核',
            'pending_review': '待复核',
            'audit_rejected': '审核不通过',
            'review_rejected': '复核不通过',
            'deprecated': '已废弃'
        };
        return texts[status] || status;
    }

    function getStatusIcon(status) {
        const icons = {
            'pending': '<i class="bi bi-clock"></i>',
            'processing': '<i class="bi bi-hourglass-split"></i>',
            'analyzing': '<i class="bi bi-hourglass-split"></i>',
            'completed': '<i class="bi bi-check-circle"></i>',
            'analyzed': '<i class="bi bi-check-circle"></i>',
            'failed': '<i class="bi bi-x-circle"></i>',
            'uploaded': '<i class="bi bi-upload"></i>',
            'pending_audit': '<i class="bi bi-eye"></i>',
            'pending_review': '<i class="bi bi-shield-check"></i>',
            'audit_rejected': '<i class="bi bi-x-circle"></i>',
            'review_rejected': '<i class="bi bi-x-circle"></i>',
            'deprecated': '<i class="bi bi-archive"></i>'
        };
        return icons[status] || '<i class="bi bi-question-circle"></i>';
    }

    function getAccuracyClass(score) {
        if (!score) return 'accuracy-poor';
        if (score >= 0.95) return 'accuracy-excellent';
        if (score >= 0.8) return 'accuracy-good';
        if (score >= 0.6) return 'accuracy-fair';
        return 'accuracy-poor';
    }

    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    function formatDateTime(dateStr) {
        if (!dateStr) return '/';

        // 处理多种日期格式
        let date;
        if (typeof dateStr === 'string') {
            // 处理常见的日期格式
            if (dateStr.includes('T')) {
                // ISO格式：2025-08-12T08:24:11.244643
                date = new Date(dateStr);
            } else if (dateStr.includes('-')) {
                // 标准格式：2025-08-12 16:24:11
                date = new Date(dateStr);
            } else {
                // 其他格式
                date = new Date(dateStr);
            }
        } else {
            date = new Date(dateStr);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            console.warn('无效的日期格式:', dateStr);
            return '/';
        }

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function handleFileSelection(checkbox) {
        console.log('🔍 handleFileSelection 被调用', {
            fileId: checkbox.dataset.fileId,
            isChecked: checkbox.checked,
            currentFileView: currentFileView,
            selectedAnalysisType: selectedAnalysisType
        });

        const fileId = checkbox.dataset.fileId;
        const isChecked = checkbox.checked;

        if (currentFileView === 'current') {
            // 当前文件视图：更新文件选中状态
            if (selectedAnalysisType) {
                const currentTypeFiles = filesByType[selectedAnalysisType] || [];
                const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

                console.log('📁 当前类型文件:', currentTypeFiles);
                console.log('📋 文件记录:', fileRecords);

                const file = currentTypeFiles.find(f => f.fileId == fileId);
                const fileRecord = fileRecords.find(r => r.fileId == fileId);

                console.log('🎯 找到的文件:', file);
                console.log('🎯 找到的记录:', fileRecord);

                if (file) {
                    file.selected = isChecked;
                    console.log('✅ 更新文件选中状态:', file.selected);
                }
                if (fileRecord) {
                    fileRecord.selected = isChecked;
                    console.log('✅ 更新记录选中状态:', fileRecord.selected);
                }
            }
        }

        // 更新分析按钮状态
        checkAnalyzeButton();

        // 更新全选复选框状态
        updateSelectAllCheckbox();

        console.log('✅ handleFileSelection 完成');
    }

</script>

<!-- 固定比例布局的文档分析结果弹窗 -->
<div class="modal fade" id="analysisResultModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-fullscreen-lg-down" style="max-width: 98%; width: 1600px; height: 95vh; margin: 2.5vh auto;">
        <div class="modal-content" style="background: white; border: 1px solid var(--gray-200); color: var(--gray-900); height: 100%; display: flex; flex-direction: column; overflow: hidden;">

            <!-- 头部栏 - 固定比例 8% -->
            <div class="modal-header" style="
                border-bottom: 1px solid var(--gray-200);
                background: var(--gray-50);
                height: 8%;
                min-height: 60px;
                max-height: 80px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                padding: 0 1.5rem;
            ">
                <h5 class="modal-title" id="analysisResultModalLabel">
                    <i class="bi bi-clipboard-data me-2" style="color: var(--primary-color);"></i>
                    文件分析结果对比
                    <span class="badge bg-secondary ms-2" id="analysisModalFileName">文件名</span>
                </h5>
                <div class="modal-header-actions d-flex align-items-center gap-2">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
            </div>

            <!-- 统计信息区域 - 固定比例 12% -->
            <div class="stats-section" style="
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-200);
                height: 12%;
                min-height: 80px;
                max-height: 120px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
            ">
                <div class="row g-3 w-100">
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">分析类型</div>
                            <div class="stat-value" id="analysisFileAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--primary-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">全字段正确率</div>
                            <div class="stat-value" id="analysisFieldAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">正确字段</div>
                            <div class="stat-value" id="analysisCorrectFields" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">总字段数</div>
                            <div class="stat-value" id="analysisTotalFields" style="font-size: 1.5rem; font-weight: 600; color: var(--gray-600);">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 - 固定比例 70% (使用剩余空间) -->
            <div class="modal-body" style="
                padding: 1rem;
                height: 70%;
                overflow: hidden;
                flex-shrink: 0;
                box-sizing: border-box;
                position: relative;
            ">
                <!-- 四列字段渲染区域 -->
                <div class="result-columns" id="analysisResultColumnsContainer" style="
                    display: grid;
                    grid-template-columns: 1fr 450px;
                    gap: 1rem;
                    height: 100%;
                    transition: grid-template-columns 0.3s ease;
                    overflow: hidden;
                ">
                    <!-- 前四列统一滚动区域 -->
                    <div class="four-columns-container" style="display: flex; flex-direction: column; background: white; border-radius: var(--border-radius); border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); overflow: hidden; position: relative; height: 100%;">
                        <!-- 收起状态下的展开按钮 - 与收起按钮同一高度 -->
                        <button class="expand-original-btn" id="analysisExpandOriginalBtn" onclick="toggleAnalysisOriginalFile()" style="
                            position: absolute;
                            right: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="展开原件展示">
                            <i class="bi bi-chevron-right" style="font-size: 1rem;"></i>
                        </button>

                        <!-- 四列标题行 -->
                        <div class="four-columns-header" style="display: grid; grid-template-columns: 1.5fr 2fr 2fr 1fr; border-bottom: 1px solid var(--gray-200); flex-shrink: 0;">
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-list-ul me-2"></i>字段名称
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-robot me-2"></i>AI识别内容
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-target me-2"></i>预期正确结果
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem;">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-graph-up me-2"></i>对比结果
                                </h6>
                            </div>
                        </div>

                        <!-- 四列内容区域（统一滚动） -->
                        <div class="four-columns-content" style="flex: 1; overflow-y: auto; overflow-x: hidden; max-height: 100%;">
                            <div id="analysisFourColumnsContentContainer" style="padding-bottom: 1rem;">
                                <!-- 动态生成的四列内容将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 原件展示列（独立滚动，支持展开/收起） -->
                    <div class="result-column" id="analysisOriginalFileArea" style="background: white; border-radius: var(--border-radius); padding: 1rem; border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); display: flex; flex-direction: column; position: relative; transition: all 0.3s ease;">
                        <!-- 展开/收起按钮 - 放在左上角 -->
                        <button class="toggle-original-btn" id="analysisToggleOriginalBtn" onclick="toggleAnalysisOriginalFile()" style="
                            position: absolute;
                            left: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="收起原件展示">
                            <i class="bi bi-chevron-left" id="analysisToggleIcon" style="font-size: 1rem;"></i>
                        </button>

                        <h6 style="color: var(--primary-color); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid var(--gray-200); flex-shrink: 0; display: flex; align-items: center; justify-content: space-between; padding-left: 3rem;">
                            <span>
                                <i class="bi bi-file-earmark-image me-2"></i>原件展示
                                <!-- 多文件指示器 -->
                                <span id="analysisMultiFileIndicator" class="badge bg-info text-white ms-2" style="font-size: 0.75rem; display: none;">
                                    <i class="bi bi-files me-1"></i>
                                    <span id="analysisCurrentFileIndex">1</span>/<span id="analysisTotalFileCount">1</span>
                                </span>
                            </span>
                            <div class="d-flex gap-2">
                                <!-- 文件切换按钮组 -->
                                <div id="analysisFileNavigationControls" class="btn-group" style="display: none;">
                                    <button class="btn btn-sm btn-outline-secondary" id="analysisPrevFileBtn" onclick="switchToAnalysisPreviousFile()" title="上一个文件">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" id="analysisNextFileBtn" onclick="switchToAnalysisNextFile()" title="下一个文件">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </div>
                                <!-- 文件选择下拉菜单 -->
                                <div class="dropdown" id="analysisFileSelectDropdown" style="display: none;">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-list me-1"></i>选择文件
                                    </button>
                                    <ul class="dropdown-menu" id="analysisFileSelectMenu">
                                        <!-- 文件列表将在这里动态生成 -->
                                    </ul>
                                </div>

                                <button type="button" class="btn btn-sm btn-outline-secondary" id="analysisFullscreenBtn" onclick="toggleAnalysisFullscreen()" title="全屏查看">
                                    <i class="bi bi-arrows-fullscreen" id="analysisFullscreenIcon"></i>
                                </button>
                            </div>
                        </h6>
                        <div class="result-content" id="analysisOriginalFileContent" style="flex: 1; overflow: auto; background: var(--gray-50); position: relative; padding: 0.5rem;">
                            <!-- 原件将在这里显示 -->
                            <div class="text-center text-muted" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <i class="bi bi-file-earmark-image" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-2">原件加载中...</p>
                                <p class="small text-primary mt-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    点击左上角按钮可收起此区域
                                </p>
                            </div>


                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部栏 - 固定比例 10% -->
            <div class="modal-footer-custom" style="
                background: var(--gray-50);
                border-top: 1px solid var(--gray-200);
                padding: 1rem 1.5rem;
                height: 10%;
                min-height: 70px;
                max-height: 100px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                position: relative;
                z-index: 1000;
            ">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- 左侧：审核说明和自动切换 -->
                    <div class="footer-left d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center gap-2">
                            <label class="form-label mb-0" style="font-size: 0.875rem; color: var(--gray-600); white-space: nowrap;">审核说明：</label>
                            <input type="text" class="form-control form-control-sm" id="analysisAuditComment" placeholder="输入审核说明..." style="width: 200px;">
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="analysisAutoNextSwitch" onchange="toggleAnalysisAutoNext()">
                            <label class="form-check-label ms-2" for="analysisAutoNextSwitch" style="font-size: 0.875rem; color: var(--gray-600);">
                                自动切换下一个
                            </label>
                        </div>
                    </div>

                    <!-- 右侧：动态按钮区域 -->
                    <div class="footer-right">
                        <div class="d-flex gap-2" id="analysisActionButtons">
                            <!-- 审核按钮 - 仅在待审核状态显示 -->
                            <button type="button" class="btn btn-success audit-buttons" onclick="submitAnalysisAudit('pass')" style="display: none;">
                                <i class="bi bi-check-circle me-1"></i>审核通过
                            </button>
                            <button type="button" class="btn btn-danger audit-buttons" onclick="submitAnalysisAudit('fail')" style="display: none;">
                                <i class="bi bi-x-circle me-1"></i>审核不通过
                            </button>

                            <!-- 复核按钮 - 仅在待复核状态显示 -->
                            <button type="button" class="btn btn-success review-buttons" onclick="submitAnalysisReview('pass')" style="display: none;">
                                <i class="bi bi-shield-check me-1"></i>复核通过
                            </button>
                            <button type="button" class="btn btn-danger review-buttons" onclick="submitAnalysisReview('fail')" style="display: none;">
                                <i class="bi bi-shield-x me-1"></i>复核不通过
                            </button>

                            <!-- 导航按钮 - 始终显示 -->
                            <button type="button" class="btn btn-primary" onclick="goToAnalysisPreviousFile()">
                                <i class="bi bi-arrow-left me-1"></i>上一个文件
                            </button>
                            <button type="button" class="btn btn-primary" onclick="goToAnalysisNextFile()">
                                <i class="bi bi-arrow-right me-1"></i>下一个文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 分析类型选择提醒模态框 -->
<div class="modal fade" id="analysisTypeRequiredModal" tabindex="-1" aria-labelledby="analysisTypeRequiredModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="analysisTypeRequiredModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    请选择分析类型
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="bi bi-upload text-muted" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3">
                    您已选择了 <span id="fileCountText" class="fw-bold text-primary"></span> 个文件，但还未选择分析类型。
                </p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    请先选择一个分析类型，然后再上传文件。不同的分析类型会使用不同的AI模型进行处理。
                </div>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="scrollToAnalysisTypes()" data-bs-dismiss="modal">
                        <i class="bi bi-arrow-down me-2"></i>
                        去选择分析类型
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入结果对比模板 -->
{% include 'result_comparison_modal.html' %}

<script>
    // 等待main.js加载完成后再执行
    document.addEventListener('DOMContentLoaded', function() {
        // 确保API和Utils可用
        if (typeof window.API === 'undefined') {
            console.error('API对象未定义，请检查main.js是否正确加载');
        }

        if (typeof window.Utils === 'undefined') {
            console.error('Utils对象未定义，请检查main.js是否正确加载');
        }

        // 创建showMessage别名，方便使用
        if (typeof window.showMessage === 'undefined') {
            window.showMessage = function(message, type, duration) {
                if (window.Utils && window.Utils.showMessage) {
                    return window.Utils.showMessage(message, type, duration);
                } else {
                    alert(message);
                }
            };
        }

    });

    // 将关键函数定义在全局作用域，确保onclick事件可以访问
    window.analyzeFile = function(fileId) {
        // 检查是否是历史记录中的文件 - 基于当前视图而不是ID格式
        const isHistoryFile = currentFileView === 'history';

        if (isHistoryFile) {
            // 历史记录文件，使用recordId调用重新分析API

            // 检查是否为重分析
            const historyFile = historyFiles.find(f => f.id == fileId);
            if (!historyFile) {
                showMessage('未找到历史记录文件', 'error');
                return;
            }

            const hasResult = ['completed', 'analyzed', 'pending_audit', 'pending_review'].includes(historyFile.status);
            const actionText = hasResult ? '重分析' : '分析';

            if (hasResult && !confirm(`确定要重新分析这个文件吗？这将覆盖现有的分析结果。`)) {
                return;
            }

            // 更新UI状态为分析中
            const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
            if (fileRow) {
                const statusCell = fileRow.querySelector('.status-badge');
                if (statusCell) {
                    statusCell.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中';
                    statusCell.className = 'status-badge status-processing';
                }

                // 禁用分析按钮
                const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                if (analyzeBtn) {
                    analyzeBtn.disabled = true;
                    analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
                }
            }

            showMessage(`正在${actionText}历史文件...`, 'info');

            // 发送重新分析请求 - 历史记录也使用files端点，因为record.id就是file_id
            // 使用历史记录本身的分析类型，而不是当前页面选择的类型
            API.post(`/api/files/${fileId}/analyze`, {
                analysis_type: historyFile.analysis_type,  // 使用记录本身的分析类型
                enable_seal_recognition: false,  // 禁用印章识别，避免处理失败
                use_mock: false  // 使用真实分析
            })
            .then(data => {
                if (data.success) {
                    // 更新本地状态 - 分析完成后状态改为待审核
                    if (historyFile) {
                        historyFile.status = 'pending_audit';
                    }

                    // 恢复UI状态
                    if (fileRow) {
                        const statusCell = fileRow.querySelector('.status-badge');
                        if (statusCell) {
                            statusCell.innerHTML = '<i class="bi bi-eye"></i> 待审核';
                            statusCell.className = 'status-badge status-pending-audit';
                        }

                        // 恢复按钮状态
                        const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                        if (analyzeBtn) {
                            analyzeBtn.disabled = false;
                            analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 重分析';
                            analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                        }
                    }

                    showMessage(`历史文件${actionText}完成`, 'success');

                    // 刷新历史记录列表
                    if (currentFileView === 'history') {
                        loadHistoryFiles();
                    }
                } else {
                    // 检查是否是文件不存在的错误
                    const isFileNotFound = data.message && (
                        data.message.includes('文件路径不存在') ||
                        data.message.includes('文件已被删除') ||
                        data.message.includes('File not found')
                    );

                    if (isFileNotFound) {
                        // 文件不存在，询问用户是否使用模拟数据
                        if (confirm('原始文件已不存在，是否使用模拟数据进行分析？\n\n注意：模拟数据仅用于演示，不代表真实分析结果。')) {
                            // 使用模拟数据重新分析
                            API.post(`/api/files/${fileId}/analyze`, {
                                analysis_type: historyFile.analysis_type,  // 使用记录本身的分析类型
                                use_mock: true  // 使用模拟数据
                            })
                            .then(mockData => {
                                if (mockData.success) {
                                    // 更新本地状态 - 分析完成后状态改为待审核
                                    if (historyFile) {
                                        historyFile.status = 'pending_audit';
                                    }

                                    // 恢复UI状态
                                    if (fileRow) {
                                        const statusCell = fileRow.querySelector('.status-badge');
                                        if (statusCell) {
                                            statusCell.innerHTML = '<i class="bi bi-eye"></i> 待审核';
                                            statusCell.className = 'status-badge status-pending-audit';
                                        }

                                        // 恢复按钮状态
                                        const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                                        if (analyzeBtn) {
                                            analyzeBtn.disabled = false;
                                            analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 重分析';
                                            analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                                        }
                                    }

                                    showMessage(`历史文件${actionText}完成（使用模拟数据）`, 'success');

                                    // 刷新历史记录列表
                                    if (currentFileView === 'history') {
                                        loadHistoryFiles();
                                    }
                                } else {
                                    // 模拟分析也失败了，执行常规失败处理
                                    handleAnalysisFailure();
                                }
                            })
                            .catch(error => {
                                console.error('模拟分析失败:', error);
                                handleAnalysisFailure();
                            });

                            return; // 不执行下面的失败处理
                        }
                    }

                    // 常规失败处理
                    function handleAnalysisFailure() {
                        // 更新本地状态为失败
                        if (historyFile) {
                            historyFile.status = 'failed';
                        }

                        // 恢复UI状态
                        if (fileRow) {
                            const statusCell = fileRow.querySelector('.status-badge');
                            if (statusCell) {
                                statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                                statusCell.className = 'status-badge status-failed';
                            }

                            // 恢复按钮状态
                            const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                            if (analyzeBtn) {
                                analyzeBtn.disabled = false;
                                analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                                analyzeBtn.className = 'btn btn-analyze btn-sm';
                            }
                        }

                        const errorMsg = isFileNotFound ?
                            '原始文件不存在，无法进行分析' :
                            `历史文件${actionText}失败: ${data.message || '未知错误'}`;
                        showMessage(errorMsg, 'error');
                    }

                    handleAnalysisFailure();
                }
            })
            .catch(error => {
                console.error('历史文件分析失败:', error);

                // 检查是否是文件不存在的错误
                const isFileNotFound = error.message && (
                    error.message.includes('文件路径不存在') ||
                    error.message.includes('文件已被删除') ||
                    error.message.includes('File not found')
                );

                if (isFileNotFound) {
                    // 文件不存在，询问用户是否使用模拟数据
                    if (confirm('原始文件已不存在，是否使用模拟数据进行分析？\n\n注意：模拟数据仅用于演示，不代表真实分析结果。')) {
                        // 使用模拟数据重新分析
                        API.post(`/api/files/${fileId}/analyze`, {
                            analysis_type: historyFile.analysis_type,  // 使用记录本身的分析类型
                            use_mock: true  // 使用模拟数据
                        })
                        .then(data => {
                            if (data.success) {
                                // 更新本地状态 - 分析完成后状态改为待审核
                                if (historyFile) {
                                    historyFile.status = 'pending_audit';
                                }

                                // 恢复UI状态
                                if (fileRow) {
                                    const statusCell = fileRow.querySelector('.status-badge');
                                    if (statusCell) {
                                        statusCell.innerHTML = '<i class="bi bi-eye"></i> 待审核';
                                        statusCell.className = 'status-badge status-pending-audit';
                                    }

                                    // 恢复按钮状态
                                    const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                                    if (analyzeBtn) {
                                        analyzeBtn.disabled = false;
                                        analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 重分析';
                                        analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                                    }
                                }

                                showMessage(`历史文件${actionText}完成（使用模拟数据）`, 'success');

                                // 刷新历史记录列表
                                if (currentFileView === 'history') {
                                    loadHistoryFiles();
                                }
                            } else {
                                // 模拟分析也失败了
                                if (historyFile) {
                                    historyFile.status = 'failed';
                                }

                                if (fileRow) {
                                    const statusCell = fileRow.querySelector('.status-badge');
                                    if (statusCell) {
                                        statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                                        statusCell.className = 'status-badge status-failed';
                                    }

                                    const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                                    if (analyzeBtn) {
                                        analyzeBtn.disabled = false;
                                        analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                                        analyzeBtn.className = 'btn btn-analyze btn-sm';
                                    }
                                }

                                showMessage(`模拟${actionText}也失败了: ${data.message || '未知错误'}`, 'error');
                            }
                        })
                        .catch(mockError => {
                            console.error('模拟分析失败:', mockError);

                            // 恢复UI状态
                            if (fileRow) {
                                const statusCell = fileRow.querySelector('.status-badge');
                                if (statusCell) {
                                    statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                                    statusCell.className = 'status-badge status-failed';
                                }

                                const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                                if (analyzeBtn) {
                                    analyzeBtn.disabled = false;
                                    analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                                    analyzeBtn.className = 'btn btn-analyze btn-sm';
                                }
                            }

                            showMessage('模拟分析失败，请联系管理员', 'error');
                        });

                        return; // 不执行下面的失败处理
                    }
                }

                // 更新本地状态为失败
                if (historyFile) {
                    historyFile.status = 'failed';
                }

                // 恢复UI状态
                if (fileRow) {
                    const statusCell = fileRow.querySelector('.status-badge');
                    if (statusCell) {
                        statusCell.innerHTML = '<i class="bi bi-x-circle"></i> 分析失败';
                        statusCell.className = 'status-badge status-failed';
                    }

                    // 恢复按钮状态
                    const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
                    if (analyzeBtn) {
                        analyzeBtn.disabled = false;
                        analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                        analyzeBtn.className = 'btn btn-analyze btn-sm';
                    }
                }

                const errorMsg = isFileNotFound ?
                    '原始文件不存在，无法进行分析' :
                    `${actionText}历史文件失败，请重试`;
                showMessage(errorMsg, 'error');
            });
            return;
        }

        // 当前上传的文件处理逻辑 - 完全参考开始分析按钮的逻辑

        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        // 获取当前类型的文件 - 与开始分析按钮使用相同的数据源
        const currentTypeFiles = getCurrentTypeFiles();

        const targetFile = currentTypeFiles.find(f => f.fileId == fileId); // 使用 == 进行类型转换比较

        if (!targetFile) {
            showMessage('未找到文件记录', 'error');
            return;
        }

        // 同时获取fileRecord用于状态同步 - 与开始分析按钮保持一致
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        const fileRecord = fileRecords.find(r => r.fileId == fileId); // 使用 == 进行类型转换比较

        if (!fileRecord) {
            showMessage('未找到文件记录', 'error');
            return;
        }

        // 检查是否为重分析 - 基于文件状态
        const hasResult = targetFile.status === 'analyzed';
        const actionText = hasResult ? '重分析' : '分析';

        if (hasResult && !confirm(`确定要重新分析这个文件吗？这将覆盖现有的分析结果。`)) {
            return;
        }

        // 更新文件状态为分析中 - 完全参考开始分析按钮的逻辑
        targetFile.status = 'analyzing';
        fileRecord.status = 'analyzing';

        // 刷新显示 - 与开始分析按钮保持一致
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        showMessage(`正在${actionText}文件：${targetFile.name}`, 'info');

        // 检查fileId是否有效 - 与开始分析按钮保持一致
        if (!targetFile.fileId || targetFile.fileId.toString().startsWith('temp_')) {
            showMessage('文件尚未上传完成，请稍后再试', 'warning');
            targetFile.status = 'pending';
            fileRecord.status = 'pending';
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
            return;
        }

        // 发送分析请求 - 使用与开始分析按钮完全相同的API调用方式，并禁用印章识别
        API.post(`/api/files/${targetFile.fileId}/analyze`, {
            analysis_type: selectedAnalysisType,
            enable_seal_recognition: false  // 禁用印章识别，避免处理失败
        })
        .then(data => {
            if (data.success) {
                // 更新文件状态 - 分析完成后状态改为待审核
                targetFile.status = 'pending_audit';
                fileRecord.status = 'pending_audit';

                // 处理recordId - 与开始分析按钮完全一致的逻辑
                if (data.data && data.data.id) {
                    // 单文件分析返回格式 - 与开始分析按钮保持一致
                    targetFile.recordId = data.data.id;
                    fileRecord.recordId = data.data.id;
                } else if (data.record_ids && data.record_ids.length > 0) {
                    // 批量分析返回格式
                    targetFile.recordId = data.record_ids[0];
                    fileRecord.recordId = data.record_ids[0];
                } else if (data.record_id) {
                    // 兼容格式
                    targetFile.recordId = data.record_id;
                    fileRecord.recordId = data.record_id;
                }

                // 同步准确率：优先使用接口直接返回的 accuracy_score；若无则调用同步接口
                const returnedAccuracy = data?.data?.accuracy_score;
                if (returnedAccuracy !== undefined && returnedAccuracy !== null && !isNaN(returnedAccuracy)) {
                    targetFile.accuracy_score = returnedAccuracy;
                    fileRecord.accuracy_score = returnedAccuracy;
                } else {
                    // 使用recordId从服务器获取最新的准确率
                    const rid = (data?.data?.id) || (data?.record_id) || (data?.record_ids && data.record_ids[0]) || targetFile.fileId;
                    if (rid) {
                        syncFileDataFromServer([rid]);
                    }
                }

                // 刷新显示 - 与开始分析按钮保持一致
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                // 显示成功消息 - 与开始分析按钮保持一致
                showMessage(`文件 ${targetFile.name} ${actionText}完成`, 'success');
            } else {
                // 更新文件状态为失败 - 完全参考开始分析按钮的逻辑
                targetFile.status = 'failed';
                fileRecord.status = 'failed';

                // 刷新显示 - 与开始分析按钮保持一致
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                showMessage(`文件 ${targetFile.name} ${actionText}失败: ${data.message || '未知错误'}`, 'error');
            }
        })
        .catch(error => {
            console.error(`${actionText}文件失败:`, error);

            // 更新文件状态为失败 - 完全参考开始分析按钮的逻辑
            targetFile.status = 'failed';
            fileRecord.status = 'failed';

            // 刷新显示 - 与开始分析按钮保持一致
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            showMessage(`${actionText}文件失败，请重试`, 'error');
        });
    };

    // 全局变量
    let currentRecordId = null;
    let autoNextEnabled = false;
    let analysisAutoNextEnabled = false; // 分析页面专用的自动切换状态
    let currentFileList = []; // 当前文件列表
    let currentPage = 1; // 当前页码
    let totalPages = 1; // 总页数

    // 查看结果
    window.viewResult = function(fileId) {
        currentRecordId = fileId;
        showLoading('正在加载分析结果...');

        fetch(`/api/files/${fileId}/result`)
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    showAnalysisResultModalWithData(data.data);
                } else {
                    showMessage('加载分析结果失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载分析结果失败:', error);
                showMessage('加载分析结果失败', 'error');
            });
    };

    // 渲染字段结构化内容
    function renderFieldStructuredContent(resultData) {
        const fieldStructure = getFieldStructureByType(resultData.analysis_type);

        if (!fieldStructure) {
            // 如果没有字段结构定义，使用原来的JSON渲染方式
            renderJSONContent('analysisFourColumnsContentContainer', resultData);
            return;
        }

        // 检查是否为动态字段类型（如理财产品说明书）
        if (fieldStructure.isDynamic) {
            console.log('检测到动态字段类型:', fieldStructure.name);
            // 动态生成字段
            const dynamicFields = generateDynamicFieldsForAnalysis(fieldStructure, resultData.ai_result, resultData.expected_result);
            console.log('动态生成的字段数量:', dynamicFields.length);
            renderFourColumnsMergedGroups(dynamicFields, resultData.ai_result, resultData.expected_result, resultData.comparison_result);
            return;
        }

        // 使用新的四列合并分组渲染方法
        renderFourColumnsMergedGroups(fieldStructure.fields, resultData.ai_result, resultData.expected_result, resultData.comparison_result);
    }

    // 获取文件类型对应的字段结构定义
    function getFieldStructureByType(analysisType) {
        // 安全检查
        if (!analysisType || typeof analysisType !== 'string') {
            console.warn('分析类型无效:', analysisType);
            return null;
        }

        // 类型映射：将实际的分析类型映射到字段结构定义
        const typeMapping = {
            'futures_account': 'FUTURES_ACCOUNT',
            'wealth_management': 'WEALTH_PRODUCT',
            'non_standard_trade': 'NON_STANDARD_TRADE',
            'broker_interest': 'BROKER_INTEREST',
            'ningyin_fee': 'NINGBO_FEE',
            'account_opening': 'ACCOUNT_OPENING'
        };

        const mappedType = typeMapping[analysisType] || analysisType.toUpperCase();
        const fieldStructures = {
            'FUTURES_ACCOUNT': {
                name: '期货账户/开户文件解析',
                fields: [
                    { name: '产品名称', path: '产品名称', type: 'String', group: '基本信息' },
                    { name: '资金账号', path: '资金账号', type: 'String', group: '基本信息' },
                    { name: '会员号_上期所', path: '会员号.上期所', type: 'String', group: '会员号信息' },
                    { name: '会员号_大商所', path: '会员号.大商所', type: 'String', group: '会员号信息' },
                    { name: '会员号_郑商所', path: '会员号.郑商所', type: 'String', group: '会员号信息' },
                    { name: '会员号_中金所', path: '会员号.中金所', type: 'String', group: '会员号信息' },
                    { name: '会员号_上能所', path: '会员号.上能所', type: 'String', group: '会员号信息' },
                    { name: '交易编码_上期所_投机', path: '交易编码.上期所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上期所_套利', path: '交易编码.上期所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上期所_套保', path: '交易编码.上期所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_投机', path: '交易编码.大商所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_套利', path: '交易编码.大商所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_套保', path: '交易编码.大商所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_投机', path: '交易编码.郑商所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_套利', path: '交易编码.郑商所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_套保', path: '交易编码.郑商所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_投机', path: '交易编码.中金所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_套利', path: '交易编码.中金所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_套保', path: '交易编码.中金所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_投机', path: '交易编码.上能所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_套利', path: '交易编码.上能所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_套保', path: '交易编码.上能所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_投机', path: '交易编码.广期所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_套利', path: '交易编码.广期所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_套保', path: '交易编码.广期所.套保', type: 'String', group: '交易编码信息' },
                    { name: '开始时间', path: '开始时间', type: 'Date', group: '时间信息' },
                    { name: '结束时间', path: '结束时间', type: 'String', group: '时间信息' }
                ]
            },
            'WEALTH_PRODUCT': {
                name: '理财产品说明书',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'NON_STANDARD_TRADE': {
                name: '非标交易确认单',
                fields: [
                    { name: '投资者名称', path: '0.investor_name', type: 'String', group: '投资者信息' },
                    { name: '投资者账号', path: '0.investor_account', type: 'String', group: '投资者信息' },
                    { name: '业务日期', path: '0.business_date', type: 'String', group: '交易信息' },
                    { name: '业务类型', path: '0.business_type', type: 'String', group: '交易信息' },
                    { name: '投资标的名称', path: '0.investment_target_name', type: 'String', group: '投资标的信息' },
                    { name: '投资标的代码', path: '0.investment_target_code', type: 'String', group: '投资标的信息' },
                    { name: '投资标的金额', path: '0.investment_amount', type: 'Decimal', group: '投资标的信息' },
                    { name: '投资标的数量', path: '0.investment_quantity', type: 'Decimal', group: '投资标的信息' },
                    { name: '交易费用', path: '0.transaction_fee', type: 'String', group: '交易信息' }
                ]
            },
            'BROKER_INTEREST': {
                name: '券商账户计息变更',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'NINGBO_FEE': {
                name: '宁银费用变更',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'ACCOUNT_OPENING': {
                name: '账户开户场景解析 V1.2',
                fields: [
                    // 管理机构信息
                    { name: '管理机构名称', path: 'manager_info.name', type: 'String', group: '管理机构信息' },
                    { name: '管理机构地址', path: 'manager_info.address', type: 'String', group: '管理机构信息' },
                    { name: '管理机构联系方式', path: 'manager_info.contact', type: 'String', group: '管理机构信息' },

                    // 投资者信息
                    { name: '投资者名称', path: 'investor_info.name', type: 'String', group: '投资者信息' },
                    { name: '投资者类型', path: 'investor_info.type', type: 'String', group: '投资者信息' },
                    { name: '账户性质', path: 'investor_info.account_nature', type: 'String', group: '投资者信息' },

                    // 联系人信息 - 支持多个联系人
                    { name: '联系人信息', path: 'contact_info', type: 'Array', group: '联系人信息',
                      arrayFields: [
                          { name: '联系人', path: 'contact_person', type: 'String' },
                          { name: '联系电话', path: 'phone', type: 'String' }
                      ]
                    },

                    // 检测结果
                    { name: '印章完整性', path: 'seal_integrity', type: 'String', group: '检测结果' },
                    { name: '页面连续性', path: 'page_continuity', type: 'String', group: '检测结果' }
                ]
            }
        };

        return fieldStructures[mappedType] || null;
    }

    // 解析JSON数据
    function parseJSONData(data) {
        if (!data) return null;
        if (typeof data === 'string') {
            try {
                return JSON.parse(data);
            } catch (e) {
                console.error('JSON解析失败:', e);
                return null;
            }
        }
        return data;
    }

    // 根据路径获取值
    function getValueByPath(obj, path) {
        if (!obj || !path) return null;

        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return null;
            }
        }

        return current;
    }

    // 添加合并的分组标题行（跨四列）
    function addMergedGroupHeader(container, groupName) {
        const groupRow = document.createElement('div');
        groupRow.className = 'merged-group-header-row';
        groupRow.style.cssText = `
            display: grid;
            grid-template-columns: 1.5fr 2fr 2fr 1fr;
            margin: 0.5rem 0 0.25rem 0;
        `;

        // 创建跨四列的分组标题
        const groupHeader = document.createElement('div');
        groupHeader.className = 'merged-group-header';
        groupHeader.textContent = groupName;
        groupHeader.style.cssText = `
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--primary-color), #4f46e5);
            color: white;
            padding: 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: 0 1rem;
        `;

        groupRow.appendChild(groupHeader);
        container.appendChild(groupRow);
    }

    // 添加四列数据行
    function addFourColumnRow(container, data) {
        const dataRow = document.createElement('div');
        dataRow.className = 'four-column-data-row';
        dataRow.style.cssText = `
            display: grid;
            grid-template-columns: 1.5fr 2fr 2fr 1fr;
            margin: 0.25rem 0;
        `;

        // 字段名称列
        const fieldNameCell = document.createElement('div');
        fieldNameCell.className = 'field-row-aligned field-name';
        fieldNameCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;
        const fieldNameContent = document.createElement('div');
        fieldNameContent.textContent = data.fieldName;
        fieldNameContent.style.cssText = `
            height: 32px;
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            display: flex;
            align-items: center;
            font-weight: 500;
        `;
        fieldNameCell.appendChild(fieldNameContent);

        // AI识别内容列
        const aiValueCell = document.createElement('div');
        aiValueCell.className = 'field-row-aligned ai-value';
        aiValueCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;
        const aiValueContent = document.createElement('div');
        aiValueContent.textContent = data.aiValue || '/';

        // 检查内容长度，决定是否需要自适应高度
        const aiValueLength = (data.aiValue || '').toString().length;
        const isRemarksField = data.fieldName && data.fieldName.includes('备注');
        const needsAutoHeight = isRemarksField || aiValueLength > 50;

        aiValueContent.style.cssText = `
            ${needsAutoHeight ? 'min-height: 32px; height: auto;' : 'height: 32px;'}
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            ${needsAutoHeight ? '' : 'display: flex; align-items: center;'}
            ${needsAutoHeight ? 'white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word;' : 'overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'}
            font-family: 'Consolas', 'Monaco', monospace;
        `;
        aiValueCell.appendChild(aiValueContent);

        // 预期结果列（可编辑）
        const expectedValueCell = document.createElement('div');
        expectedValueCell.className = 'field-row-aligned expected-value';
        expectedValueCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;
        // 所有字段都使用input输入框，保持一致性
        const expectedInput = document.createElement('input');
        expectedInput.type = 'text';
        expectedInput.className = 'form-control form-control-sm editable-field';
        expectedInput.value = data.expectedValue || '';
        expectedInput.dataset.path = data.fieldPath;
        expectedInput.style.cssText = `
            height: 32px;
            border: 1px solid #dee2e6;
            background: #f8f9ff;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            border-color: #10b981;
            font-family: 'Consolas', 'Monaco', monospace;
        `;
        expectedValueCell.appendChild(expectedInput);

        // 对比结果列
        const comparisonCell = document.createElement('div');
        comparisonCell.className = 'field-row-aligned comparison-result';
        comparisonCell.style.cssText = `
            padding: 1rem;
        `;
        const comparisonContent = document.createElement('div');
        comparisonContent.textContent = data.comparisonResult;
        comparisonContent.style.cssText = `
            height: 32px;
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-family: 'Consolas', 'Monaco', monospace;
        `;
        comparisonCell.appendChild(comparisonContent);

        // 添加所有列到行
        dataRow.appendChild(fieldNameCell);
        dataRow.appendChild(aiValueCell);
        dataRow.appendChild(expectedValueCell);
        dataRow.appendChild(comparisonCell);

        container.appendChild(dataRow);
    }

    // 动态生成字段（根据实际数据）- 分析页面版本
    function generateDynamicFieldsForAnalysis(fieldStructure, aiResult, expectedResult) {
        const fields = [];

        try {
            // 解析AI结果和预期结果
            const aiData = parseJSONData(aiResult);
            const expectedData = parseJSONData(expectedResult);

            // 根据字段结构类型处理不同的动态字段生成
            if (fieldStructure.name === '理财产品说明书') {
                // 理财产品说明书：处理销售机构数组
                const aiArray = aiData?.销售机构 || [];
                const expectedArray = expectedData?.销售机构 || [];

                // 取两者中较大的长度
                const maxLength = Math.max(
                    Array.isArray(aiArray) ? aiArray.length : 0,
                    Array.isArray(expectedArray) ? expectedArray.length : 0
                );

                // 如果没有数据，至少显示一个字段
                const finalLength = maxLength > 0 ? maxLength : 1;

                // 动态生成字段
                for (let i = 0; i < finalLength; i++) {
                    fields.push({
                        name: `销售机构 ${i + 1}`, // 从1开始计数，更美观
                        path: `销售机构.${i}`,
                        type: 'String',
                        group: '销售机构'
                    });
                }
            } else if (fieldStructure.name === '券商账户计息变更') {
                // 券商账户计息变更：动态处理产品数组
                const aiArray = Array.isArray(aiData) ? aiData : [];
                const expectedArray = Array.isArray(expectedData) ? expectedData : [];

                // 取两者中较大的长度
                const maxLength = Math.max(aiArray.length, expectedArray.length);

                // 如果没有数据，至少显示一个产品
                const finalLength = maxLength > 0 ? maxLength : 1;

                // 为每个产品动态生成字段
                for (let i = 0; i < finalLength; i++) {
                    const productIndex = i + 1;
                    const chineseIndex = numberToChinese(productIndex);
                    const aiItem = aiArray[i] || {};
                    const expectedItem = expectedArray[i] || {};

                    // 合并两个数据源的所有字段（包括空值字段）
                    const allFields = new Set();

                    // 收集AI结果中的所有字段（包括空值）
                    Object.keys(aiItem).forEach(key => {
                        allFields.add(key);
                    });

                    // 收集期望结果中的所有字段（包括空值）
                    Object.keys(expectedItem).forEach(key => {
                        allFields.add(key);
                    });

                    // 确保基础字段始终存在（即使数据中没有）
                    const requiredFields = ['product_name', 'product_type', 'interest_rate', 'start_time', 'end_time', 'interest_days', 'remarks'];
                    requiredFields.forEach(field => {
                        allFields.add(field);
                    });

                    // 动态添加基础字段
                    if (allFields.has('product_name')) {
                        fields.push({ name: `产品名称`, path: `${i}.product_name`, type: 'String', group: `产品${chineseIndex}` });
                    }
                    if (allFields.has('product_type')) {
                        fields.push({ name: `产品类型`, path: `${i}.product_type`, type: 'String', group: `产品${chineseIndex}` });
                    }

                    // 动态处理利率字段
                    if (allFields.has('interest_rate')) {
                        const aiInterestRate = aiItem.interest_rate || {};
                        const expectedInterestRate = expectedItem.interest_rate || {};

                        // 收集所有利率字段，但排除父级对象字段
                        const interestFields = new Set();

                        // 检查AI结果的利率字段（包括空值）
                        Object.keys(aiInterestRate).forEach(key => {
                            // 如果是嵌套对象，只收集子字段，不收集父级字段
                            if (typeof aiInterestRate[key] === 'object' && aiInterestRate[key] !== null) {
                                Object.keys(aiInterestRate[key]).forEach(subKey => {
                                    interestFields.add(`${key}.${subKey}`);
                                });
                            } else {
                                // 只有非对象类型的字段才直接添加
                                interestFields.add(key);
                            }
                        });

                        // 检查期望结果的利率字段（包括空值）
                        Object.keys(expectedInterestRate).forEach(key => {
                            // 如果是嵌套对象，只收集子字段，不收集父级字段
                            if (typeof expectedInterestRate[key] === 'object' && expectedInterestRate[key] !== null) {
                                Object.keys(expectedInterestRate[key]).forEach(subKey => {
                                    interestFields.add(`${key}.${subKey}`);
                                });
                            } else {
                                // 只有非对象类型的字段才直接添加
                                interestFields.add(key);
                            }
                        });

                        // 添加利率字段
                        interestFields.forEach(field => {
                            let fieldName = '';
                            let fieldPath = `${i}.interest_rate.${field}`;

                            if (field === 'all') {
                                fieldName = `利率(年化)`;
                            } else if (field === 'individual') {
                                fieldName = `个人利率`;
                            } else if (field.startsWith('non_individual.')) {
                                const subField = field.replace('non_individual.', '');
                                fieldName = formatNonIndividualFieldName(subField, productIndex);
                            } else if (field.startsWith('all.')) {
                                const subField = field.replace('all.', '');
                                fieldName = `利率(年化)_${subField}`;
                            } else {
                                fieldName = `${field}`;
                            }

                            fields.push({ name: fieldName, path: fieldPath, type: 'String', group: `产品${chineseIndex}` });
                        });
                    }

                    // 动态添加其他字段
                    if (allFields.has('start_time')) {
                        fields.push({ name: `开始时间`, path: `${i}.start_time`, type: 'Date', group: `产品${chineseIndex}` });
                    }
                    if (allFields.has('end_time')) {
                        fields.push({ name: `截止时间`, path: `${i}.end_time`, type: 'String', group: `产品${chineseIndex}` });
                    }
                    if (allFields.has('interest_days')) {
                        fields.push({ name: `计息天数`, path: `${i}.interest_days`, type: 'Integer', group: `产品${chineseIndex}` });
                    }
                    if (allFields.has('remarks')) {
                        fields.push({ name: `备注`, path: `${i}.remarks`, type: 'String', group: `产品${chineseIndex}` });
                    }
                }
            } else if (fieldStructure.name === '宁银费用变更') {
                // 宁银费用变更：动态处理产品信息和费用变更数组

                // 添加基本信息字段
                fields.push(
                    { name: '发行机构', path: 'issuer', type: 'String', group: '基本信息' },
                    { name: '生效日期', path: 'effective_date', type: 'String', group: '基本信息' },
                    { name: '通知日期', path: 'announcement_date', type: 'String', group: '基本信息' }
                );

                // 处理产品信息数组
                const productInfoArray = aiData?.product_info || expectedData?.product_info || [];
                const productInfoLength = Array.isArray(productInfoArray) ? productInfoArray.length : 0;

                for (let i = 0; i < Math.max(productInfoLength, 1); i++) {
                    const chineseIndex = numberToChinese(i + 1);
                    fields.push(
                        { name: '产品名称', path: `product_info.${i}.product_name`, type: 'String', group: `产品信息${chineseIndex}` },
                        { name: '产品代码', path: `product_info.${i}.product_code`, type: 'String', group: `产品信息${chineseIndex}` }
                    );
                }

                // 处理费用变更数组
                const feeChangesArray = aiData?.fee_changes || expectedData?.fee_changes || [];
                const feeChangesLength = Array.isArray(feeChangesArray) ? feeChangesArray.length : 0;

                for (let i = 0; i < Math.max(feeChangesLength, 1); i++) {
                    const chineseIndex = numberToChinese(i + 1);
                    fields.push(
                        { name: '费用类型', path: `fee_changes.${i}.fee_type`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '原始费率', path: `fee_changes.${i}.original_rate`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '新费率', path: `fee_changes.${i}.new_rate`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '生效开始', path: `fee_changes.${i}.effective_start`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '生效结束', path: `fee_changes.${i}.effective_end`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '备注', path: `fee_changes.${i}.notes`, type: 'String', group: `费用变更${chineseIndex}` }
                    );
                }
            }
        } catch (error) {
            console.error('动态生成字段时出错:', error);
            // 出错时返回默认字段
            if (fieldStructure.name === '券商账户计息变更') {
                fields.push(
                    { name: '产品名称', path: '0.product_name', type: 'String', group: '产品一' },
                    { name: '产品类型', path: '0.product_type', type: 'String', group: '产品一' },
                    { name: '个人利率', path: '0.interest_rate.individual', type: 'String', group: '产品一' },
                    { name: '非个人利率_START:2024-11-11', path: '0.interest_rate.non_individual.START:2024-11-11', type: 'String', group: '产品一' },
                    { name: '非个人利率_2024-11-11:END', path: '0.interest_rate.non_individual.2024-11-11:END', type: 'String', group: '产品一' },
                    { name: '非个人利率_START:2024-11-01', path: '0.interest_rate.non_individual.START:2024-11-01', type: 'String', group: '产品一' },
                    { name: '利率(年化)', path: '0.interest_rate.all', type: 'String', group: '产品一' },
                    { name: '开始时间', path: '0.start_time', type: 'Date', group: '产品一' },
                    { name: '截止时间', path: '0.end_time', type: 'String', group: '产品一' },
                    { name: '计息天数', path: '0.interest_days', type: 'Integer', group: '产品一' },
                    { name: '备注', path: '0.remarks', type: 'String', group: '产品一' }
                );
            } else if (fieldStructure.name === '宁银费用变更') {
                fields.push(
                    { name: '发行机构', path: 'issuer', type: 'String', group: '基本信息' },
                    { name: '生效日期', path: 'effective_date', type: 'String', group: '基本信息' },
                    { name: '通知日期', path: 'announcement_date', type: 'String', group: '基本信息' },
                    { name: '产品名称', path: 'product_info.0.product_name', type: 'String', group: '产品信息一' },
                    { name: '产品代码', path: 'product_info.0.product_code', type: 'String', group: '产品信息一' },
                    { name: '费用类型', path: 'fee_changes.0.fee_type', type: 'String', group: '费用变更一' },
                    { name: '原始费率', path: 'fee_changes.0.original_rate', type: 'String', group: '费用变更一' },
                    { name: '新费率', path: 'fee_changes.0.new_rate', type: 'String', group: '费用变更一' },
                    { name: '生效开始', path: 'fee_changes.0.effective_start', type: 'String', group: '费用变更一' },
                    { name: '生效结束', path: 'fee_changes.0.effective_end', type: 'String', group: '费用变更一' },
                    { name: '备注', path: 'fee_changes.0.notes', type: 'String', group: '费用变更一' }
                );
            } else {
                fields.push({
                    name: '销售机构 1',
                    path: '销售机构.0',
                    type: 'String',
                    group: '销售机构'
                });
            }
        }

        return fields;
    }

    // 四列合并分组渲染方法
    function renderFourColumnsMergedGroups(fields, aiResult, expectedResult, comparisonResult) {
        const container = document.getElementById('analysisFourColumnsContentContainer');
        if (!container) {
            console.error('找不到analysisFourColumnsContentContainer容器');
            return;
        }
        container.innerHTML = '';

        // 解析JSON数据
        const aiData = parseJSONData(aiResult);
        const expectedData = parseJSONData(expectedResult);

        if (!aiData && !expectedData) {
            container.innerHTML = '<div class="text-muted">暂无数据</div>';
            return;
        }

        // 按分组组织字段
        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            const groupFields = groups[groupName];

            // 添加合并的分组标题行
            addMergedGroupHeader(container, groupName);

            // 添加该分组的所有字段行
            groupFields.forEach(field => {
                if (field.type === 'Array' && field.arrayFields) {
                    // 处理数组字段
                    const aiArrayData = getValueByPath(aiData, field.path) || [];
                    const expectedArrayData = getValueByPath(expectedData, field.path) || [];
                    const maxLength = Math.max(aiArrayData.length, expectedArrayData.length);

                    if (maxLength === 0) {
                        // 如果数组为空，显示空数组提示
                        addFourColumnRow(container, {
                            fieldName: field.name,
                            aiValue: '[]',
                            expectedValue: '[]',
                            comparisonResult: getComparisonIcon('[]', '[]'),
                            fieldPath: field.path
                        });
                    } else {
                        // 为每个数组项创建子字段
                        for (let index = 0; index < maxLength; index++) {
                            const aiItem = aiArrayData[index] || {};
                            const expectedItem = expectedArrayData[index] || {};

                            // 渲染数组项的子字段
                            field.arrayFields.forEach(subField => {
                                const aiValue = getValueByPath(aiItem, subField.path);
                                const expectedValue = getValueByPath(expectedItem, subField.path);
                                // 特殊处理联系人信息的字段名称显示
                                const fieldDisplayName = field.path === 'contact_info' ?
                                    `联系人${index + 1} ${subField.name}` :
                                    `${field.name}[${index}].${subField.name}`;

                                addFourColumnRow(container, {
                                    fieldName: fieldDisplayName,
                                    aiValue: formatFieldValue(aiValue, subField.type, subField.path),
                                    expectedValue: formatFieldValue(expectedValue, subField.type, subField.path),
                                    comparisonResult: getComparisonIcon(aiValue, expectedValue),
                                    fieldPath: `${field.path}[${index}].${subField.path}`
                                });
                            });
                        }
                    }
                } else {
                    // 处理普通字段
                    const fieldPath = field.originalPath || field.originalName || field.path;
                    const aiValue = getValueByPath(aiData, fieldPath);
                    const expectedValue = getValueByPath(expectedData, fieldPath);

                    addFourColumnRow(container, {
                        fieldName: field.displayName || field.name,
                        aiValue: formatFieldValue(aiValue, field.type, fieldPath),
                        expectedValue: formatFieldValue(expectedValue, field.type, fieldPath),
                        comparisonResult: getComparisonIcon(aiValue, expectedValue),
                        fieldPath: fieldPath
                    });
                }
            });
        });
    }

    // 按分组组织字段
    function groupFieldsByGroup(fields) {
        const groups = {};
        fields.forEach(field => {
            const groupName = field.group || '基本信息';
            if (!groups[groupName]) {
                groups[groupName] = [];
            }
            groups[groupName].push(field);
        });
        return groups;
    }

    // 添加合并的分组标题行
    function addMergedGroupHeader(container, groupName) {
        const groupRow = document.createElement('div');
        groupRow.className = 'merged-group-header-row';
        groupRow.style.cssText = `
            margin: 0.5rem 0;
        `;

        // 创建跨四列的分组标题
        const groupHeader = document.createElement('div');
        groupHeader.className = 'merged-group-header';
        groupHeader.textContent = groupName;
        groupHeader.style.cssText = `
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--primary-color), #4f46e5);
            color: white;
            padding: 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: 0 1rem;
        `;

        groupRow.appendChild(groupHeader);
        container.appendChild(groupRow);
    }

    // 添加四列数据行
    function addFourColumnRow(container, data) {
        // 检查是否是备注字段
        const isRemarksField = (data.fieldName && data.fieldName.includes('备注')) ||
                              (data.fieldPath && data.fieldPath.includes('remarks'));

        const dataRow = document.createElement('div');
        dataRow.className = 'four-column-data-row';
        dataRow.style.cssText = `
            display: grid;
            grid-template-columns: 1.5fr 2fr 2fr 1fr;
            margin: 0.25rem 0;
            ${isRemarksField ? 'align-items: start;' : 'align-items: center;'}
        `;

        // 字段名称列
        const fieldNameDiv = document.createElement('div');
        fieldNameDiv.className = `field-row-aligned field-name${isRemarksField ? ' remarks-field' : ''}`;
        fieldNameDiv.style.cssText = `
            ${isRemarksField ? 'min-height: 38px !important; height: auto !important; max-height: none !important;' : 'height: 38px !important; min-height: 38px !important; max-height: 38px !important;'}
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 3px solid #6366f1;
            background: var(--gray-50);
            display: flex;
            align-items: ${isRemarksField ? 'flex-start' : 'center'};
            position: relative;
            transition: all 0.2s ease;
            padding: ${isRemarksField ? '0.5rem 0.75rem' : '0 0.75rem'};
            font-weight: 500;
            font-size: 0.875rem;
            color: var(--gray-800);
            ${isRemarksField ? 'word-wrap: break-word; white-space: pre-wrap; overflow-wrap: break-word;' : 'overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'}
        `;
        fieldNameDiv.textContent = data.fieldName;

        // AI识别内容列
        const aiValueDiv = document.createElement('div');
        aiValueDiv.className = `field-row-aligned ai-value${isRemarksField ? ' remarks-field' : ''}`;

        // 检查内容长度，决定是否需要自适应高度
        const aiValueLength = (data.aiValue || '').toString().length;
        const needsAutoHeight = isRemarksField || aiValueLength > 50;

        aiValueDiv.style.cssText = `
            ${needsAutoHeight ? 'min-height: 38px !important; height: auto !important; max-height: none !important;' : 'height: 38px !important; min-height: 38px !important; max-height: 38px !important;'}
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 3px solid #3b82f6;
            background: var(--gray-50);
            ${needsAutoHeight ? 'display: block; align-items: flex-start;' : 'display: flex; align-items: center;'}
            position: relative;
            transition: all 0.2s ease;
            padding: ${needsAutoHeight ? '0.5rem 0.75rem' : '0 0.75rem'};
            font-size: 0.875rem;
            color: var(--gray-700);
            ${needsAutoHeight ? 'white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word;' : 'overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'}
        `;
        aiValueDiv.textContent = data.aiValue || '-';

        // 预期正确结果列
        const expectedValueDiv = document.createElement('div');
        expectedValueDiv.className = `field-row-aligned expected-value${isRemarksField ? ' remarks-field' : ''}`;

        // 所有字段使用统一的单行样式
        expectedValueDiv.style.cssText = `
            height: 38px !important;
            min-height: 38px !important;
            max-height: 38px !important;
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 3px solid #10b981;
            background: var(--gray-50);
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.2s ease;
            padding: 0 0.75rem;
            font-size: 0.875rem;
            color: var(--gray-700);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        `;

        // 如果是可编辑的，创建输入框
        if (data.fieldPath) {
            const input = document.createElement('input');
            input.type = 'text';
            input.value = data.expectedValue || '';
            input.className = 'form-control form-control-sm';
            input.style.cssText = `
                border: none;
                background: transparent;
                padding: 0;
                font-size: 0.875rem;
                color: var(--gray-700);
                width: 100%;
                height: 100%;
                outline: none;
            `;
            input.setAttribute('data-field-path', data.fieldPath);
            input.addEventListener('change', function() {
                // 这里可以添加保存逻辑
            });
            expectedValueDiv.appendChild(input);
        } else {
            expectedValueDiv.textContent = data.expectedValue || '-';
        }

        // 对比结果列
        const comparisonDiv = document.createElement('div');
        comparisonDiv.className = `field-row-aligned comparison-result${isRemarksField ? ' remarks-field' : ''}`;
        comparisonDiv.style.cssText = `
            ${isRemarksField ? 'min-height: 38px !important; height: auto !important; max-height: none !important;' : 'height: 38px !important; min-height: 38px !important; max-height: 38px !important;'}
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 3px solid #f59e0b;
            background: var(--gray-50);
            display: flex;
            align-items: ${isRemarksField ? 'flex-start' : 'center'};
            justify-content: center;
            position: relative;
            transition: all 0.2s ease;
            padding: ${isRemarksField ? '0.5rem 0.75rem' : '0 0.75rem'};
            font-size: 0.875rem;
            text-align: center;
        `;
        comparisonDiv.innerHTML = data.comparisonResult;

        // 添加悬停效果
        [fieldNameDiv, aiValueDiv, expectedValueDiv, comparisonDiv].forEach(div => {
            div.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(var(--primary-color-rgb), 0.05)';
                this.style.transform = 'translateX(2px)';
            });
            div.addEventListener('mouseleave', function() {
                this.style.background = 'var(--gray-50)';
                this.style.transform = 'translateX(0)';
            });
        });

        dataRow.appendChild(fieldNameDiv);
        dataRow.appendChild(aiValueDiv);
        dataRow.appendChild(expectedValueDiv);
        dataRow.appendChild(comparisonDiv);

        container.appendChild(dataRow);
    }

    // 产品类型中文翻译映射
    const productTypeTranslations = {
        'single_product': '单一产品',
        'company_wide_product': '公司级产品',
        'multiple_products': '多产品',
        'portfolio_product': '组合产品'
    };

    // 翻译产品类型
    function translateProductType(value) {
        if (!value) return value;
        return productTypeTranslations[value] || value;
    }

    // 数字转中文大写
    function numberToChinese(num) {
        const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
        if (num <= 10) {
            return chineseNumbers[num];
        } else if (num < 20) {
            return '十' + chineseNumbers[num - 10];
        } else if (num < 100) {
            const tens = Math.floor(num / 10);
            const ones = num % 10;
            return chineseNumbers[tens] + '十' + (ones > 0 ? chineseNumbers[ones] : '');
        } else {
            // 对于更大的数字，可以继续扩展
            return num.toString();
        }
    }

    // 格式化非个人利率字段名称
    function formatNonIndividualFieldName(subField, productIndex) {
        const chineseIndex = numberToChinese(productIndex);

        // 解析时间段格式，如 "START:2024-11-11", "2024-11-11:END", "START:2024-11-01"
        if (subField.includes('START:') && subField.includes('END')) {
            // 格式如 "START:2024-11-11:END" 或 "2024-11-11:2024-12-31"
            const parts = subField.split(':');
            if (parts.length >= 3) {
                const startDate = parts[1];
                const endDate = parts[2] === 'END' ? '至今' : parts[2];
                return `非个人利率(${startDate}至${endDate})`;
            }
        } else if (subField.startsWith('START:')) {
            // 格式如 "START:2024-11-11"
            const startDate = subField.replace('START:', '');
            return `非个人利率(${startDate}起)`;
        } else if (subField.endsWith(':END')) {
            // 格式如 "2024-11-11:END"
            const endDate = subField.replace(':END', '');
            return `非个人利率(至${endDate})`;
        } else if (subField.includes(':')) {
            // 格式如 "2024-11-01:2024-11-10"
            const [startDate, endDate] = subField.split(':');
            return `非个人利率(${startDate}至${endDate})`;
        } else {
            // 其他格式，保持原样但更友好
            return `非个人利率_${subField}`;
        }
    }

    // 根据字段路径和值进行特殊处理
    function processFieldValue(fieldPath, value) {
        if (!value) return value;

        // 如果是产品类型字段，进行翻译
        if (fieldPath && fieldPath.includes('product_type')) {
            return translateProductType(value);
        }

        // 印章完整性字段的中文映射
        if (fieldPath && fieldPath.includes('seal_integrity')) {
            const sealIntegrityMap = {
                'complete': '完整',
                'incomplete': '不完整',
                'unknown': '未知'
            };
            return sealIntegrityMap[value] || value;
        }

        // 页面连续性字段的中文映射
        if (fieldPath && fieldPath.includes('page_continuity')) {
            const pageContinuityMap = {
                'continuous': '连续',
                'discontinuous': '不连续',
                'unknown': '未知'
            };
            return pageContinuityMap[value] || value;
        }

        return value;
    }

    // 格式化字段值
    function formatFieldValue(value, type, fieldPath = null) {
        if (value === null || value === undefined || value === '') {
            return '/';
        }

        let processedValue = value;

        // 如果有字段路径，进行特殊处理（如翻译）
        if (fieldPath) {
            processedValue = processFieldValue(fieldPath, value);
        }

        if (type === 'date' && processedValue) {
            // 尝试格式化日期
            const date = new Date(processedValue);
            if (!isNaN(date.getTime())) {
                return date.toLocaleDateString('zh-CN');
            }
        }

        return String(processedValue);
    }

    // 获取对比图标
    function getComparisonIcon(aiValue, expectedValue) {
        const aiStr = String(aiValue || '').trim();
        const expectedStr = String(expectedValue || '').trim();
        const isMatch = aiStr === expectedStr;

        if (isMatch) {
            return '<span class="badge bg-success" style="font-size: 0.75rem;"><i class="bi bi-check-circle me-1"></i>匹配</span>';
        } else {
            return '<span class="badge bg-danger" style="font-size: 0.75rem;"><i class="bi bi-x-circle me-1"></i>不匹配</span>';
        }
    }

    // 简化的JSON内容渲染（备用方案）
    function renderJSONContent(containerId, resultData) {
        const container = document.getElementById(containerId);
        if (!container) return;
        container.innerHTML = `
            <div style="padding: 1rem;">
                <div style="margin-bottom: 1.5rem;">
                    <h6 style="color: var(--primary-color); margin-bottom: 0.5rem;">
                        <i class="bi bi-robot me-2"></i>AI识别结果
                    </h6>
                    <pre style="background: var(--gray-50); padding: 1rem; border-radius: 6px; border: 1px solid var(--gray-200); font-size: 0.875rem; max-height: 200px; overflow-y: auto;">${JSON.stringify(resultData.ai_result, null, 2)}</pre>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h6 style="color: var(--primary-color); margin-bottom: 0.5rem;">
                        <i class="bi bi-target me-2"></i>预期结果
                    </h6>
                    <pre style="background: var(--gray-50); padding: 1rem; border-radius: 6px; border: 1px solid var(--gray-200); font-size: 0.875rem; max-height: 200px; overflow-y: auto;">${JSON.stringify(resultData.expected_result, null, 2)}</pre>
                </div>

                <div>
                    <h6 style="color: var(--primary-color); margin-bottom: 0.5rem;">
                        <i class="bi bi-graph-up me-2"></i>对比结果
                    </h6>
                    <pre style="background: var(--gray-50); padding: 1rem; border-radius: 6px; border: 1px solid var(--gray-200); font-size: 0.875rem; max-height: 200px; overflow-y: auto;">${JSON.stringify(resultData.comparison_result, null, 2)}</pre>
                </div>
            </div>
        `;
    }

    // 原件展示区域展开/收起功能
    let isOriginalFileCollapsed = false;
    let isAnalysisOriginalFileCollapsed = false;

    function toggleOriginalFile() {
        const container = document.getElementById('resultColumnsContainer');
        const originalArea = document.getElementById('originalFileArea');
        const toggleBtn = document.getElementById('toggleOriginalBtn');
        const expandBtn = document.getElementById('expandOriginalBtn');
        const toggleIcon = document.getElementById('toggleIcon');
        const statusText = document.getElementById('originalFileStatus');

        if (isOriginalFileCollapsed) {
            // 展开原件展示
            container.style.gridTemplateColumns = '1fr 450px';
            originalArea.style.opacity = '0';
            originalArea.style.display = 'flex';

            // 延迟显示内容，确保动画流畅
            setTimeout(() => {
                originalArea.style.opacity = '1';
            }, 50);

            // 显示收起按钮，隐藏展开按钮
            toggleBtn.style.display = 'flex';
            expandBtn.style.display = 'none';

            toggleIcon.className = 'bi bi-chevron-left';
            toggleBtn.title = '收起原件展示';
            if (statusText) statusText.textContent = '展开状态';
            isOriginalFileCollapsed = false;
        } else {
            // 收起原件展示
            originalArea.style.opacity = '0';

            setTimeout(() => {
                originalArea.style.display = 'none';
                container.style.gridTemplateColumns = '1fr';

                // 隐藏收起按钮，显示展开按钮
                toggleBtn.style.display = 'none';
                expandBtn.style.display = 'flex';

                if (statusText) statusText.textContent = '收起状态';
            }, 300);

            isOriginalFileCollapsed = true;
        }
    }

    // 分析页面专用的原件展示区域展开/收起功能
    function toggleAnalysisOriginalFile() {
        const container = document.getElementById('analysisResultColumnsContainer');
        const originalArea = document.getElementById('analysisOriginalFileArea');
        const toggleBtn = document.getElementById('analysisToggleOriginalBtn');
        const expandBtn = document.getElementById('analysisExpandOriginalBtn');
        const toggleIcon = document.getElementById('analysisToggleIcon');

        if (!container || !originalArea || !toggleBtn || !expandBtn || !toggleIcon) {
            console.warn('分析页面原件展示切换：找不到必要的DOM元素');
            return;
        }

        if (isAnalysisOriginalFileCollapsed) {
            // 展开原件展示
            originalArea.style.display = 'flex';
            container.style.gridTemplateColumns = '1fr 450px';

            setTimeout(() => {
                originalArea.style.opacity = '1';

                // 显示收起按钮，隐藏展开按钮
                toggleBtn.style.display = 'flex';
                expandBtn.style.display = 'none';

                // 更新图标
                toggleIcon.className = 'bi bi-chevron-left';
                toggleBtn.title = '收起原件展示';
            }, 50);

            isAnalysisOriginalFileCollapsed = false;
        } else {
            // 收起原件展示
            originalArea.style.opacity = '0';

            setTimeout(() => {
                originalArea.style.display = 'none';
                container.style.gridTemplateColumns = '1fr';

                // 隐藏收起按钮，显示展开按钮
                toggleBtn.style.display = 'none';
                expandBtn.style.display = 'flex';

                // 更新图标
                toggleIcon.className = 'bi bi-chevron-right';
                expandBtn.title = '展开原件展示';
            }, 300);

            isAnalysisOriginalFileCollapsed = true;
        }
    }



    // 分析页面全屏查看功能
    function toggleAnalysisFullscreen() {
        const modal = new bootstrap.Modal(document.getElementById('analysisFullscreenModal'));
        const fullscreenContent = document.getElementById('analysisFullscreenContent');
        const originalContent = document.getElementById('analysisOriginalFileContent');

        // 复制原件内容到全屏模态框
        const fileElement = originalContent.querySelector('iframe, img');
        if (fileElement) {
            const clonedElement = fileElement.cloneNode(true);
            clonedElement.style.width = '100%';
            clonedElement.style.height = '100%';
            clonedElement.style.objectFit = 'contain';

            fullscreenContent.innerHTML = '';
            fullscreenContent.appendChild(clonedElement);

            modal.show();
        } else {
            showMessage('没有可全屏查看的内容', 'warning');
        }
    }



    // 分析页面专用的审核功能
    function submitAnalysisAudit(status) {
        const comment = document.getElementById('analysisAuditComment').value.trim();
        // 验证当前记录ID
        if (!currentResultRecordId) {
            console.error('错误：currentResultRecordId 为空或未定义');
            showMessage('错误：未找到当前文件记录', 'error');
            return;
        }

        if (!confirm(`确定要${status === 'pass' ? '通过' : '不通过'}这个文件的审核吗？`)) {
            return;
        }

        showLoading('正在提交审核结果...');

        // 构建请求数据
        const requestData = {
            audit_status: status,
            audit_comment: comment
        };
        fetch(`/api/files/${currentResultRecordId}/audit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();

            if (data.success) {
                // 根据审核结果更新本地状态
                const newStatus = status === 'pass' ? 'pending_review' : 'audit_rejected';

                // 更新历史文件列表中的状态
                if (historyFiles && currentResultRecordId) {
                    const historyFile = historyFiles.find(f => f.id == currentResultRecordId);
                    if (historyFile) {
                        historyFile.status = newStatus;
                    }
                }

                // 更新当前文件列表中的状态
                if (filesByType && currentResultRecordId) {
                    for (const typeFiles of Object.values(filesByType)) {
                        const file = typeFiles.find(f => f.recordId == currentResultRecordId);
                        if (file) {
                            file.status = newStatus;
                            break;
                        }
                    }
                }

                const statusText = status === 'pass' ? '通过，已进入待复核状态' : '不通过';
                showMessage(`审核${statusText}`, 'success');

                // 关闭弹窗
                const modal = bootstrap.Modal.getInstance(document.getElementById('analysisResultModal'));
                if (modal) {
                    modal.hide();
                }

                // 如果开启了自动切换，切换到下一个
                if (analysisAutoNextEnabled) {
                    setTimeout(() => {
                        findAnalysisNextFileInCurrentList();
                    }, 500);
                } else {
                    // 刷新文件列表
                    setTimeout(() => {
                        if (currentFileView === 'history') {
                            loadHistoryFiles();
                        } else if (currentFileView === 'current') {
                            displayCurrentFiles();
                        }
                    }, 500);
                }
            } else {
                showMessage('审核失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('分析审核失败:', error);
            showMessage('审核失败: ' + error.message, 'error');
        });
    }

    // 分析页面专用的复核功能
    function submitAnalysisReview(status) {
        const comment = document.getElementById('analysisAuditComment').value.trim();
        // 验证当前记录ID
        if (!currentResultRecordId) {
            console.error('错误：currentResultRecordId 为空或未定义');
            showMessage('错误：未找到当前文件记录', 'error');
            return;
        }

        if (!confirm(`确定要${status === 'pass' ? '通过' : '不通过'}这个文件的复核吗？`)) {
            return;
        }

        showLoading('正在提交复核结果...');

        // 构建请求数据
        const requestData = {
            review_status: status,
            review_comment: comment
        };
        fetch(`/api/files/${currentResultRecordId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();

            if (data.success) {
                // 根据复核结果更新本地状态
                const newStatus = status === 'pass' ? 'completed' : 'review_rejected';

                // 更新历史文件列表中的状态
                if (historyFiles && currentResultRecordId) {
                    const historyFile = historyFiles.find(f => f.id == currentResultRecordId);
                    if (historyFile) {
                        historyFile.status = newStatus;
                    }
                }

                // 更新当前文件列表中的状态
                if (filesByType && currentResultRecordId) {
                    for (const typeFiles of Object.values(filesByType)) {
                        const file = typeFiles.find(f => f.recordId == currentResultRecordId);
                        if (file) {
                            file.status = newStatus;
                            break;
                        }
                    }
                }

                const statusText = status === 'pass' ? '通过，文件已完成' : '不通过';
                showMessage(`复核${statusText}`, 'success');

                // 关闭弹窗
                const modal = bootstrap.Modal.getInstance(document.getElementById('analysisResultModal'));
                if (modal) {
                    modal.hide();
                }

                // 如果开启了自动切换，切换到下一个
                if (analysisAutoNextEnabled) {
                    setTimeout(() => {
                        findAnalysisNextFileInCurrentList();
                    }, 500);
                } else {
                    // 刷新文件列表
                    setTimeout(() => {
                        if (currentFileView === 'history') {
                            loadHistoryFiles();
                        } else if (currentFileView === 'current') {
                            displayCurrentFiles();
                        }
                    }, 500);
                }
            } else {
                showMessage('复核失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('分析复核失败:', error);
            showMessage('复核失败: ' + error.message, 'error');
        });
    }

    // 一键自动审核功能
    function autoAuditFiles() {
        if (!confirm('确定要自动审核所有100%识别率的待审核文件吗？')) {
            return;
        }

        showLoading('正在查找符合条件的文件...');

        // 获取所有待审核的文件
        fetch('/api/files/pending-audit')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    // 筛选出100%识别率的文件
                    const perfectFiles = data.data.filter(file =>
                        file.accuracy_score && parseFloat(file.accuracy_score) >= 1.0
                    );

                    if (perfectFiles.length === 0) {
                        hideLoading();
                        showMessage('没有找到100%识别率的待审核文件', 'info');
                        return;
                    }
                    // 批量审核这些文件
                    batchAuditFiles(perfectFiles);
                } else {
                    hideLoading();
                    showMessage('没有找到待审核的文件', 'info');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('获取待审核文件失败:', error);
                showMessage('获取待审核文件失败', 'error');
            });
    }

    // 批量审核文件
    function batchAuditFiles(files) {
        let completedCount = 0;
        let successCount = 0;
        let failedCount = 0;

        const updateProgress = () => {
            const progress = Math.round((completedCount / files.length) * 100);
            showLoading(`正在自动审核... (${completedCount}/${files.length}) ${progress}%`);
        };

        const processFile = (file, index) => {
            return fetch(`/api/files/${file.id}/audit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    audit_status: 'pass',
                    audit_comment: '一键自动审核通过（100%识别率）'
                })
            })
            .then(response => response.json())
            .then(data => {
                completedCount++;
                if (data.success) {
                    successCount++;
                } else {
                    failedCount++;
                    console.error(`文件 ${file.filename} 审核失败:`, data.message);
                }
                updateProgress();
            })
            .catch(error => {
                completedCount++;
                failedCount++;
                console.error(`文件 ${file.filename} 审核失败:`, error);
                updateProgress();
            });
        };

        // 并发处理，但限制并发数量
        const batchSize = 3; // 每次最多处理3个文件
        const processBatch = async (startIndex) => {
            const batch = files.slice(startIndex, startIndex + batchSize);
            const promises = batch.map((file, index) => processFile(file, startIndex + index));
            await Promise.all(promises);

            if (startIndex + batchSize < files.length) {
                await processBatch(startIndex + batchSize);
            }
        };

        processBatch(0).then(() => {
            hideLoading();

            if (successCount > 0) {
                showMessage(`自动审核完成！成功审核${successCount}个文件，${failedCount}个失败`, 'success');

                // 刷新文件列表
                setTimeout(() => {
                    if (currentFileView === 'history') {
                        loadHistoryFiles();
                    } else if (currentFileView === 'current') {
                        displayCurrentFiles();
                    }
                }, 1000);
            } else {
                showMessage('自动审核失败，没有文件被成功审核', 'error');
            }
        });
    }

    // 提交审核结果（从弹窗）
    function submitAudit(status) {
        const comment = document.getElementById('auditComment').value.trim();

        // 验证当前记录ID
        if (!currentRecordId) {
            showMessage('错误：未找到当前文件记录', 'error');
            return;
        }

        showLoading('正在提交审核结果...');

        fetch(`/api/files/${currentRecordId}/audit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: status,
                comment: comment
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`审核${status === 'pass' ? '通过' : '不通过'}成功`, 'success');

                // 关闭弹窗
                const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
                if (modal) {
                    modal.hide();
                }

                // 如果开启了自动切换，切换到下一个
                if (autoNextEnabled) {
                    setTimeout(() => {
                        findNextFileInCurrentList();
                    }, 500);
                }

                // 刷新当前页面的文件列表
                if (currentFileView === 'history') {
                    loadHistoryFiles();
                } else if (currentFileView === 'current') {
                    displayCurrentFiles();
                }
            } else {
                showMessage('审核提交失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('审核提交失败:', error);
            showMessage('审核提交失败', 'error');
        });
    }

    // 分析页面专用的自动切换开关
    function toggleAnalysisAutoNext() {
        const switchElement = document.getElementById('analysisAutoNextSwitch');
        analysisAutoNextEnabled = switchElement.checked;
        // 保存用户偏好到本地存储
        localStorage.setItem('analysisAutoNextEnabled', analysisAutoNextEnabled);
    }

    // 分析页面专用的文件切换功能
    function goToAnalysisNextFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('analysisResultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找下一个文件
        setTimeout(() => {
            findAnalysisNextFileInCurrentList();
        }, 300);
    }

    function goToAnalysisPreviousFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('analysisResultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找上一个文件
        setTimeout(() => {
            findAnalysisPreviousFileInCurrentList();
        }, 300);
    }

    // 手动切换到下一个文件
    function goToNextFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找下一个文件
        setTimeout(() => {
            findNextFileInCurrentList();
        }, 300);
    }

    // 手动切换到上一个文件
    function goToPreviousFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找上一个文件
        setTimeout(() => {
            findPreviousFileInCurrentList();
        }, 300);
    }

    // 关闭模态框并切换到下一个
    function closeModalAndNext() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        if (autoNextEnabled) {
            // 查找下一个文件（基于当前列表）
            setTimeout(() => {
                findNextFileInCurrentList();
            }, 300);
        }
    }

    // 分析页面专用的文件查找函数
    function findAnalysisNextFileInCurrentList() {
        // 更新当前文件列表
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到文件列表', 'warning');
            return;
        }

        // 查找当前文件在列表中的位置
        const currentIndex = currentFileList.findIndex(file => file.id === currentResultRecordId);
        if (currentIndex === -1) {
            showMessage('当前文件不在列表中', 'warning');
            return;
        }

        // 查找下一个文件
        let nextFile = null;
        for (let i = currentIndex + 1; i < currentFileList.length; i++) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                nextFile = file;
                break;
            }
        }

        if (nextFile) {
            showLoading('正在切换到下一个文件...');
            setTimeout(() => {
                hideLoading();
                showAnalysisResultModal(nextFile.id);
            }, 300);
        } else {
            showMessage('没有更多文件了，已经是最后一个文件', 'warning');
        }
    }

    function findAnalysisPreviousFileInCurrentList() {
        // 更新当前文件列表
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到文件列表', 'warning');
            return;
        }

        // 查找当前文件在列表中的位置
        const currentIndex = currentFileList.findIndex(file => file.id === currentResultRecordId);
        if (currentIndex === -1) {
            showMessage('当前文件不在列表中', 'warning');
            return;
        }

        // 查找上一个文件
        let previousFile = null;
        for (let i = currentIndex - 1; i >= 0; i--) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                previousFile = file;
                break;
            }
        }

        if (previousFile) {
            showLoading('正在切换到上一个文件...');
            setTimeout(() => {
                hideLoading();
                showAnalysisResultModal(previousFile.id);
            }, 300);
        } else {
            showMessage('没有更多文件了，已经是第一个文件', 'warning');
        }
    }

    // 基于当前列表查找下一个文件
    function findNextFileInCurrentList() {
        // 更新当前文件列表
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            findNextReviewFile();
            return;
        }

        // 查找当前文件在列表中的索引
        const currentIndex = currentFileList.findIndex(file => file.id === currentRecordId);
        if (currentIndex === -1) {
            findNextReviewFile();
            return;
        }

        // 查找下一个文件
        let nextFile = null;
        for (let i = currentIndex + 1; i < currentFileList.length; i++) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                nextFile = file;
                break;
            }
        }

        if (nextFile) {
            showLoading('正在切换到下一个文件...');
            setTimeout(() => {
                hideLoading();
                viewResult(nextFile.id);
            }, 300);
        } else {
            // 当前页没有下一个文件，检查是否有下一页
            if (currentPage < totalPages) {
                showLoading('正在加载下一页...');
                loadFileList(currentPage + 1);
                setTimeout(() => {
                    hideLoading();
                    // 加载下一页后，查找第一个有结果的文件
                    findFirstFileWithResult();
                }, 1000);
            } else {
                showMessage('已经是最后一个文件了，没有更多文件可查看', 'warning');
                loadFileList(currentPage);
            }
        }
    }

    // 基于当前列表查找上一个文件
    function findPreviousFileInCurrentList() {
        // 更新当前文件列表
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            findPreviousReviewFile();
            return;
        }

        // 查找当前文件在列表中的索引
        const currentIndex = currentFileList.findIndex(file => file.id === currentRecordId);
        if (currentIndex === -1) {
            findPreviousReviewFile();
            return;
        }

        // 查找上一个文件
        let previousFile = null;
        for (let i = currentIndex - 1; i >= 0; i--) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                previousFile = file;
                break;
            }
        }

        if (previousFile) {
            showLoading('正在切换到上一个文件...');
            setTimeout(() => {
                hideLoading();
                viewResult(previousFile.id);
            }, 300);
        } else {
            // 当前页没有上一个文件，检查是否有上一页
            if (currentPage > 1) {
                showLoading('正在加载上一页...');
                loadFileList(currentPage - 1);
                setTimeout(() => {
                    hideLoading();
                    // 加载上一页后，查找最后一个有结果的文件
                    findLastFileWithResult();
                }, 1000);
            } else {
                showMessage('已经是第一个文件了，没有更多文件可查看', 'warning');
                loadFileList(currentPage);
            }
        }
    }

    // 更新当前文件列表
    function updateCurrentFileList() {
        if (currentFileView === 'history' && historyFiles) {
            currentFileList = historyFiles;
        } else if (currentFileView === 'current') {
            // 从当前文件类型中获取文件列表
            if (selectedAnalysisType && filesByType[selectedAnalysisType]) {
                currentFileList = filesByType[selectedAnalysisType];
            } else {
                currentFileList = [];
            }
        } else {
            currentFileList = [];
        }
    }

    // 查找第一个有结果的文件
    function findFirstFileWithResult() {
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到可查看的文件', 'warning');
            return;
        }

        // 查找第一个有结果的文件
        let firstFile = null;
        for (let i = 0; i < currentFileList.length; i++) {
            const file = currentFileList[i];
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                firstFile = file;
                break;
            }
        }

        if (firstFile) {
            viewResult(firstFile.id);
        } else {
            showMessage('当前页没有可查看的文件', 'warning');
        }
    }

    // 查找最后一个有结果的文件
    function findLastFileWithResult() {
        updateCurrentFileList();

        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到可查看的文件', 'warning');
            return;
        }

        // 查找最后一个有结果的文件
        let lastFile = null;
        for (let i = currentFileList.length - 1; i >= 0; i--) {
            const file = currentFileList[i];
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                lastFile = file;
                break;
            }
        }

        if (lastFile) {
            viewResult(lastFile.id);
        } else {
            showMessage('当前页没有可查看的文件', 'warning');
        }
    }

    // 加载文件列表（分页）
    function loadFileList(page) {
        if (currentFileView === 'history') {
            // 加载历史文件列表
            loadHistoryFiles(page);
        } else if (currentFileView === 'current') {
            // 重新加载当前文件
            if (selectedAnalysisType) {
                loadCurrentFiles(selectedAnalysisType, page);
            }
        }

        currentPage = page;
    }

    // 回退到原有逻辑的函数
    function findNextReviewFile() {
        showMessage('正在查找下一个文件...', 'info');

        // 这里可以实现基于API的查找逻辑
        // 暂时显示提示信息
        setTimeout(() => {
            showMessage('没有找到下一个文件', 'warning');
        }, 1000);
    }

    function findPreviousReviewFile() {
        showMessage('正在查找上一个文件...', 'info');

        // 这里可以实现基于API的查找逻辑
        // 暂时显示提示信息
        setTimeout(() => {
            showMessage('没有找到上一个文件', 'warning');
        }, 1000);
    }

    // 切换自动下一个功能
    function toggleAutoNext() {
        autoNextEnabled = document.getElementById('autoNextSwitch').checked;
        // 可以保存到localStorage
        localStorage.setItem('autoNextEnabled', autoNextEnabled);
    }

    // 恢复自动切换状态
    function restoreAutoNextState() {
        const saved = localStorage.getItem('autoNextEnabled');
        if (saved !== null) {
            autoNextEnabled = saved === 'true';
            const switchElement = document.getElementById('autoNextSwitch');
            if (switchElement) {
                switchElement.checked = autoNextEnabled;
            }
        }
    }

    // 恢复分析页面自动切换状态
    function restoreAnalysisAutoNextState() {
        const savedState = localStorage.getItem('analysisAutoNextEnabled');
        if (savedState !== null) {
            analysisAutoNextEnabled = savedState === 'true';
            const switchElement = document.getElementById('analysisAutoNextSwitch');
            if (switchElement) {
                switchElement.checked = analysisAutoNextEnabled;
            }
        }
    }

    // 初始化分析页面事件监听器
    function initAnalysisEventListeners() {
        try {
            // 分析页面全屏模态框关闭事件
            const analysisFullscreenModal = document.getElementById('analysisFullscreenModal');
            if (analysisFullscreenModal) {
                // 全屏模态框关闭事件（如果需要其他处理可以在这里添加）
            } else {
                console.error('分析页面全屏模态框元素未找到');
            }
        } catch (error) {
            console.error('初始化分析页面事件监听器时出错:', error);
        }
    }

    // 页面加载时恢复自动切换状态
    document.addEventListener('DOMContentLoaded', function() {
        restoreAutoNextState();
        restoreAnalysisAutoNextState();
        initAnalysisEventListeners();
    });

    window.deprecateFile = function(fileId) {

        if (!confirm('确定要废弃这个文件吗？')) {
            return;
        }

        // 调用后端API废弃文件
        API.post(`/api/files/${fileId}/deprecate`, {
            reason: '用户手动废弃'
        })
        .then(response => {
            if (response.success) {
                showMessage('文件已废弃', 'success');

                // 更新本地数据
                updateLocalFileStatus(fileId, 'deprecated');

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                } else {
                    loadHistoryFiles();
                }
                updateFileCountBadges();
            } else {
                showMessage('废弃文件失败: ' + response.message, 'error');
            }
        })
        .catch(error => {
            console.error('废弃文件失败:', error);
            showMessage('废弃文件失败', 'error');
        });
    };

    window.restoreFile = function(fileId) {

        // 调用后端API恢复文件
        API.post(`/api/files/${fileId}/restore`, {
            reason: '用户手动恢复'
        })
        .then(response => {
            if (response.success) {
                showMessage('文件已恢复', 'success');

                // 更新本地数据
                updateLocalFileStatus(fileId, 'active');

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                } else {
                    loadHistoryFiles();
                }
                updateFileCountBadges();
            } else {
                showMessage('恢复文件失败: ' + response.message, 'error');
            }
        })
        .catch(error => {
            console.error('恢复文件失败:', error);
            showMessage('恢复文件失败', 'error');
        });
    };

    // 更新本地文件状态
    function updateLocalFileStatus(fileId, fileStatus) {
        // 更新当前文件数据
        for (const type of Object.keys(filesByType)) {
            const files = filesByType[type] || [];
            const fileItem = files.find(f => f.fileId == fileId);
            if (fileItem) {
                fileItem.file_status = fileStatus;
            }
        }

        // 更新文件记录数据
        for (const type of Object.keys(fileRecordsByType)) {
            const records = fileRecordsByType[type] || [];
            const fileRecord = records.find(r => r.fileId == fileId);
            if (fileRecord) {
                fileRecord.file_status = fileStatus;
            }
        }

        // 更新历史文件数据
        const historyFile = historyFiles.find(f => f.id == fileId);
        if (historyFile) {
            historyFile.file_status = fileStatus;
        }
    }

    // 加载和隐藏遮罩层
    function showLoading(text = '正在处理...') {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // 消息提示
    function showMessage(message, type = 'info') {
        // 创建Toast提示
        const toastContainer = getOrCreateToastContainer();
        const toast = createToast(message, type);
        toastContainer.appendChild(toast);

        // 显示Toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 自动移除Toast元素
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    // 获取或创建Toast容器
    function getOrCreateToastContainer() {
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    // 创建Toast元素
    function createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const typeConfig = {
            'success': { icon: 'bi-check-circle-fill', color: 'text-success', title: '成功' },
            'error': { icon: 'bi-exclamation-triangle-fill', color: 'text-danger', title: '错误' },
            'warning': { icon: 'bi-exclamation-triangle-fill', color: 'text-warning', title: '警告' },
            'info': { icon: 'bi-info-circle-fill', color: 'text-info', title: '信息' }
        };

        const config = typeConfig[type] || typeConfig['info'];

        toast.innerHTML = `
            <div class="toast-header">
                <i class="bi ${config.icon} ${config.color} me-2"></i>
                <strong class="me-auto">${config.title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="关闭"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        return toast;
    }

    window.deprecateFile = function(fileId) {
        try {
            if (window.updateLocalFileStatus) {
                window.updateLocalFileStatus(fileId, 'deprecated');
            }
            if (window.showMessage) {
                window.showMessage('文件已废弃', 'success');
            } else {
                alert('文件已废弃');
            }
        } catch (error) {
            console.error('废弃文件失败:', error);
            if (window.showMessage) {
                window.showMessage('废弃文件失败', 'error');
            } else {
                alert('废弃文件失败: ' + error.message);
            }
        }
    };

    window.restoreFile = function(fileId) {
        try {
            if (window.updateLocalFileStatus) {
                window.updateLocalFileStatus(fileId, 'active');
            }
            if (window.showMessage) {
                window.showMessage('文件已恢复', 'success');
            } else {
                alert('文件已恢复');
            }
        } catch (error) {
            console.error('恢复文件失败:', error);
            if (window.showMessage) {
                window.showMessage('恢复文件失败', 'error');
            } else {
                alert('恢复文件失败: ' + error.message);
            }
        }
    };

    // 简化版本的辅助函数，直接在这里定义
    window.updateLocalFileStatus = function(fileId, newStatus) {
        try {
            const targetStatus = newStatus === 'deprecated' ? 'deprecated' : 'active';

            // 更新历史文件列表中的状态
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    historyFile.file_status = targetStatus;
                }
            }

            // 更新当前文件列表中的状态
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        file.file_status = targetStatus;
                        break;
                    }
                }
            }

            // 立即更新页面显示
            window.updateFileRowStatus(fileId, targetStatus);
        } catch (error) {
            console.error('updateLocalFileStatus error:', error);
        }
    };

    window.updateFileAnalysisStatus = function(fileId, newStatus) {
        try {
            // 更新历史文件列表中的状态
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    historyFile.status = newStatus;
                }
            }

            // 更新当前文件列表中的状态
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        file.status = newStatus;
                        break;
                    }
                }
            }

            // 刷新文件列表显示
            if (window.refreshFileList) {
                window.refreshFileList();
            }
        } catch (error) {
            console.error('updateFileAnalysisStatus error:', error);
        }
    };

    window.updateFileRowStatus = function(fileId, fileStatus) {
        try {
            const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
            if (fileRow) {
                if (fileStatus === 'deprecated') {
                    fileRow.classList.add('file-deprecated');
                } else {
                    fileRow.classList.remove('file-deprecated');
                }

                // 重新渲染该行的按钮
                const actionsCell = fileRow.querySelector('td:last-child .file-actions');
                if (actionsCell) {
                    const isDeprecated = fileStatus === 'deprecated';
                    const hasResult = false; // 暂时设为false

                    actionsCell.innerHTML = `
                        <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                                onclick="analyzeFile(${fileId})"
                                ${!isDeprecated ? '' : 'disabled'}>
                            <i class="bi bi-cpu"></i>
                            ${hasResult ? '重分析' : '分析'}
                        </button>
                        <button class="btn btn-view-result btn-sm"
                                onclick="viewResult(${fileId})"
                                ${hasResult ? '' : 'disabled'}>
                            <i class="bi bi-eye"></i>
                            查看结果
                        </button>
                        ${!isDeprecated ? `
                            <button class="btn btn-deprecate btn-sm"
                                    onclick="deprecateFile(${fileId})">
                                <i class="bi bi-archive"></i>
                                废弃
                            </button>
                        ` : `
                            <button class="btn btn-restore btn-sm"
                                    onclick="restoreFile(${fileId})">
                                <i class="bi bi-arrow-counterclockwise"></i>
                                恢复
                            </button>
                        `}
                    `;
                }
            }
        } catch (error) {
            console.error('updateFileRowStatus error:', error);
        }
    };

    window.checkIfReanalyze = function(fileId) {
        try {
            // 在历史记录中查找
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    return ['completed', 'pending_audit', 'pending_review'].includes(historyFile.status);
                }
            }

            // 在当前文件中查找
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        return ['completed', 'pending_audit', 'pending_review'].includes(file.status);
                    }
                }
            }

            return false;
        } catch (error) {
            console.error('checkIfReanalyze error:', error);
            return false;
        }
    };

    // 动态加载result_comparison.js
    const script = document.createElement('script');
    script.src = "{{ url_for('static', filename='js/result_comparison.js') }}";
    script.onload = function() {
        // 确保resultComparison实例可用
        if (typeof ResultComparison !== 'undefined') {
            window.resultComparison = new ResultComparison();
        }
    };
    document.head.appendChild(script);

    // ===== 分析页面合并文件切换功能 =====

    // 加载分析页面合并文件数据
    function loadAnalysisMergedFiles(resultData) {
        const container = document.getElementById('analysisOriginalFileContent');

        // 显示加载指示器
        container.innerHTML = `
            <div class="text-center text-muted">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在检查合并文件...</p>
            </div>
        `;

        // 尝试获取合并文件数据
        fetch(`/api/files/${currentResultRecordId}/merged-files`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.files && data.data.files.length > 1) {
                    // 多文件情况，显示文件选择功能
                    analysisCurrentMergedFiles = data.data.files;
                    analysisCurrentFileIndex = 0;
                    analysisIsShowingMergedFiles = true;
                    setupAnalysisMergedFileDisplay();
                    displayAnalysisCurrentFile();
                } else {
                    // 单文件情况，使用原有逻辑
                    analysisIsShowingMergedFiles = false;
                    renderAnalysisSingleOriginalFile(resultData);
                }
            })
            .catch(error => {
                console.error('获取合并文件失败:', error);
                // 回退到单文件逻辑
                analysisIsShowingMergedFiles = false;
                renderAnalysisSingleOriginalFile(resultData);
            });
    }

    // 设置分析页面合并文件显示界面
    function setupAnalysisMergedFileDisplay() {
        // 显示文件选择控件
        const fileSelectDropdown = document.getElementById('analysisFileSelectDropdown');
        const multiFileIndicator = document.getElementById('analysisMultiFileIndicator');

        if (fileSelectDropdown) {
            fileSelectDropdown.style.display = 'block';
            setupAnalysisFileSelectMenu();
        }

        if (multiFileIndicator) {
            multiFileIndicator.style.display = 'inline-block';
            updateAnalysisFileIndicator();
        }
    }

    // 隐藏分析页面文件选择控件
    function hideAnalysisFileSelectionControls() {
        const fileSelectDropdown = document.getElementById('analysisFileSelectDropdown');
        const multiFileIndicator = document.getElementById('analysisMultiFileIndicator');
        const fileNavigationControls = document.getElementById('analysisFileNavigationControls');

        if (fileSelectDropdown) fileSelectDropdown.style.display = 'none';
        if (multiFileIndicator) multiFileIndicator.style.display = 'none';
        if (fileNavigationControls) fileNavigationControls.style.display = 'none';
    }

    // 设置分析页面文件选择菜单
    function setupAnalysisFileSelectMenu() {
        const menu = document.getElementById('analysisFileSelectMenu');
        if (!menu) return;

        menu.innerHTML = '';
        analysisCurrentMergedFiles.forEach((file, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <a class="dropdown-item ${index === analysisCurrentFileIndex ? 'active' : ''}" href="#" onclick="switchToAnalysisFile(${index})">
                    <i class="bi bi-file-earmark${file.is_main ? '-check' : ''} me-2"></i>
                    ${file.display_name}
                    ${file.is_main ? '<span class="badge bg-primary ms-2">主文件</span>' : ''}
                </a>
            `;
            menu.appendChild(li);
        });
    }

    // 更新分析页面文件指示器
    function updateAnalysisFileIndicator() {
        const currentIndexElement = document.getElementById('analysisCurrentFileIndex');
        const totalCountElement = document.getElementById('analysisTotalFileCount');

        if (currentIndexElement) {
            currentIndexElement.textContent = analysisCurrentFileIndex + 1;
        }
        if (totalCountElement) {
            totalCountElement.textContent = analysisCurrentMergedFiles.length;
        }
    }

    // 显示分析页面当前文件
    function displayAnalysisCurrentFile() {
        if (!analysisCurrentMergedFiles || analysisCurrentMergedFiles.length === 0) return;

        const currentFile = analysisCurrentMergedFiles[analysisCurrentFileIndex];

        // 渲染该文件的原件
        renderAnalysisOriginalFileContent(currentFile.id);

        // 更新文件选择菜单的活动状态
        setupAnalysisFileSelectMenu();

        // 更新文件指示器
        updateAnalysisFileIndicator();
    }

    // 分析页面文件切换函数
    function switchToAnalysisFile(index) {
        if (index >= 0 && index < analysisCurrentMergedFiles.length) {
            analysisCurrentFileIndex = index;
            displayAnalysisCurrentFile();
        }
    }

    // 切换到上一个分析文件
    function switchToAnalysisPreviousFile() {
        if (analysisCurrentFileIndex > 0) {
            switchToAnalysisFile(analysisCurrentFileIndex - 1);
        }
    }

    // 切换到下一个分析文件
    function switchToAnalysisNextFile() {
        if (analysisCurrentFileIndex < analysisCurrentMergedFiles.length - 1) {
            switchToAnalysisFile(analysisCurrentFileIndex + 1);
        }
    }

    // 渲染分析页面原件文件内容
    function renderAnalysisOriginalFileContent(fileId) {
        const container = document.getElementById('analysisOriginalFileContent');
        const fileName = analysisCurrentMergedFiles[analysisCurrentFileIndex]?.filename || '';

        // 显示加载状态
        container.innerHTML = `
            <div class="text-center text-muted">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载原件...</p>
            </div>
        `;

        try {
            // 使用API获取文件数据
            fetch(`/api/files/${fileId}/original-data`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fileData = data.data;
                        // 根据文件类型显示不同内容
                        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileData.file_type.replace('.', ''))) {
                            // 图片文件
                            const img = document.createElement('img');
                            img.src = fileData.data_url;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'contain';
                            img.style.display = 'block';
                            img.alt = '原件图片';
                            img.onload = function() {
                                // 图片加载完成
                            };
                            img.onerror = function() {
                                console.error('图片加载失败');
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                        <p class="mt-2">图片加载失败</p>
                                        <small class="text-muted">文件: ${fileName}</small>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(img);
                        } else if (fileData.file_type === '.pdf') {
                            // PDF文件
                            const iframe = document.createElement('iframe');
                            iframe.src = `/api/files/${fileId}/original`;
                            iframe.style.width = '100%';
                            iframe.style.height = '100%';
                            iframe.style.border = 'none';
                            iframe.style.minHeight = '500px';
                            iframe.onload = function() {
                                // PDF加载完成
                            };
                            iframe.onerror = function() {
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: var(--danger-color);"></i>
                                        <p class="mt-2">PDF预览不可用</p>
                                        <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                        <a href="/api/files/${fileId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-download me-1"></i>下载查看
                                        </a>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(iframe);
                        } else {
                            // 其他文件类型
                            container.innerHTML = `
                                <div class="text-center text-muted">
                                    <i class="bi bi-file-earmark" style="font-size: 3rem; color: var(--info-color);"></i>
                                    <p class="mt-2">${fileName}</p>
                                    <p class="small">不支持预览此文件类型</p>
                                    <a href="/api/files/${fileId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download me-1"></i>下载文件
                                    </a>
                                </div>
                            `;
                        }
                    } else {
                        container.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                <p class="mt-2">文件加载失败</p>
                                <small class="text-muted">${data.message}</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取文件数据异常:', error);
                    container.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--danger-color);"></i>
                            <p class="mt-2">网络错误</p>
                            <small class="text-muted">无法加载原件文件</small>
                        </div>
                    `;
                });
        } catch (error) {
            console.error('渲染原件时发生错误:', error);
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-bug" style="font-size: 3rem; color: var(--danger-color);"></i>
                    <p class="mt-2">渲染错误</p>
                    <p class="small">请刷新页面后重试</p>
                </div>
            `;
        }
    }
</script>

<!-- 固定比例布局的文件分析结果对比弹窗 -->
<div class="modal fade" id="resultModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-fullscreen-lg-down" style="max-width: 98%; width: 1600px; height: 95vh; margin: 2.5vh auto;">
        <div class="modal-content" style="background: white; border: 1px solid var(--gray-200); color: var(--gray-900); height: 100%; display: flex; flex-direction: column; overflow: hidden;">

            <!-- 头部栏 - 固定比例 8% -->
            <div class="modal-header" style="
                border-bottom: 1px solid var(--gray-200);
                background: var(--gray-50);
                height: 8%;
                min-height: 60px;
                max-height: 80px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                padding: 0 1.5rem;
            ">
                <h5 class="modal-title">
                    <i class="bi bi-clipboard-data me-2" style="color: var(--primary-color);"></i>
                    文件分析结果对比
                    <span class="badge bg-secondary ms-2" id="modalFileName">文件名</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>

            <!-- 统计信息区域 - 固定比例 12% -->
            <div class="stats-section" style="
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-200);
                height: 12%;
                min-height: 80px;
                max-height: 120px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
            ">
                <div class="row g-3 w-100">
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">分析类型</div>
                            <div class="stat-value" id="fileAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--primary-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">全字段正确率</div>
                            <div class="stat-value" id="fieldAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">正确字段</div>
                            <div class="stat-value" id="correctFields" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">总字段数</div>
                            <div class="stat-value" id="totalFields" style="font-size: 1.5rem; font-weight: 600; color: var(--gray-600);">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 - 固定比例 70% (使用剩余空间) -->
            <div class="modal-body" style="
                padding: 1rem;
                height: 70%;
                overflow: hidden;
                flex-shrink: 0;
                box-sizing: border-box;
                position: relative;
            ">
                <!-- 四列字段渲染区域 -->
                <div class="result-columns" id="resultColumnsContainer" style="
                    display: grid;
                    grid-template-columns: 1fr 450px;
                    gap: 1rem;
                    height: 100%;
                    transition: grid-template-columns 0.3s ease;
                    overflow: hidden;
                ">
                    <!-- 前四列统一滚动区域 -->
                    <div class="four-columns-container" style="display: flex; flex-direction: column; background: white; border-radius: var(--border-radius); border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); overflow: hidden; position: relative; height: 100%;">
                        <!-- 收起状态下的展开按钮 - 与收起按钮同一高度 -->
                        <button class="expand-original-btn" id="expandOriginalBtn" onclick="toggleOriginalFile()" style="
                            position: absolute;
                            right: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="展开原件展示">
                            <i class="bi bi-chevron-right" style="font-size: 1rem;"></i>
                        </button>

                        <!-- 四列标题行 -->
                        <div class="four-columns-header" style="display: grid; grid-template-columns: 1.5fr 2fr 2fr 1fr; border-bottom: 1px solid var(--gray-200); flex-shrink: 0;">
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-list-ul me-2"></i>字段名称
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-robot me-2"></i>AI识别内容
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-target me-2"></i>预期正确结果
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem;">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-graph-up me-2"></i>对比结果
                                </h6>
                            </div>
                        </div>

                        <!-- 四列内容区域（统一滚动） -->
                        <div class="four-columns-content" style="flex: 1; overflow-y: auto; overflow-x: hidden; max-height: 100%;">
                            <div id="fourColumnsContentContainer" style="padding-bottom: 1rem;">
                                <!-- 动态生成的四列内容将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 原件展示列（独立滚动，支持展开/收起） -->
                    <div class="result-column" id="originalFileArea" style="background: white; border-radius: var(--border-radius); padding: 1rem; border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); display: flex; flex-direction: column; position: relative; transition: all 0.3s ease;">
                        <!-- 展开/收起按钮 - 放在左上角 -->
                        <button class="toggle-original-btn" id="toggleOriginalBtn" onclick="toggleOriginalFile()" style="
                            position: absolute;
                            left: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="收起原件展示">
                            <i class="bi bi-chevron-left" id="toggleIcon" style="font-size: 1rem;"></i>
                        </button>

                        <h6 style="color: var(--primary-color); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid var(--gray-200); flex-shrink: 0; display: flex; align-items: center; justify-content: space-between; padding-left: 3rem;">
                            <span>
                                <i class="bi bi-file-earmark-image me-2"></i>原件展示
                            </span>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="fullscreenBtn" onclick="toggleFullscreen()" title="全屏查看">
                                    <i class="bi bi-arrows-fullscreen" id="fullscreenIcon"></i>
                                </button>
                            </div>
                        </h6>
                        <div class="result-content" id="originalFileContent" style="flex: 1; overflow: auto; background: var(--gray-50); position: relative; padding: 0.5rem;">
                            <!-- 原件将在这里显示 -->
                            <div class="text-center text-muted" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <i class="bi bi-file-earmark-image" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-2">原件加载中...</p>
                                <p class="small text-primary mt-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    点击左上角按钮可收起此区域
                                </p>
                            </div>


                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部栏 - 固定比例 10% -->
            <div class="modal-footer-custom" style="
                background: var(--gray-50);
                border-top: 1px solid var(--gray-200);
                padding: 1rem 1.5rem;
                height: 10%;
                min-height: 70px;
                max-height: 100px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                position: relative;
                z-index: 1000;
            ">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- 左侧：审核说明和自动切换 -->
                    <div class="footer-left d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center gap-2">
                            <label class="form-label mb-0" style="font-size: 0.875rem; color: var(--gray-600); white-space: nowrap;">审核说明：</label>
                            <input type="text" class="form-control form-control-sm" id="auditComment" placeholder="输入审核说明..." style="width: 200px;">
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoNextSwitch" onchange="toggleAutoNext()">
                            <label class="form-check-label ms-2" for="autoNextSwitch" style="font-size: 0.875rem; color: var(--gray-600);">
                                自动切换下一个
                            </label>
                        </div>
                    </div>

                    <!-- 右侧：四个按钮（审核通过、审核不通过、上一个文件、下一个文件） -->
                    <div class="footer-right">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" onclick="submitAudit('pass')">
                                <i class="bi bi-check-circle me-1"></i>审核通过
                            </button>
                            <button type="button" class="btn btn-danger" onclick="submitAudit('fail')">
                                <i class="bi bi-x-circle me-1"></i>审核不通过
                            </button>
                            <button type="button" class="btn btn-primary" onclick="goToPreviousFile()">
                                <i class="bi bi-arrow-left me-1"></i>上一个文件
                            </button>
                            <button type="button" class="btn btn-primary" onclick="goToNextFile()">
                                <i class="bi bi-arrow-right me-1"></i>下一个文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 分析页面全屏查看模态框 -->
<div class="modal fade" id="analysisFullscreenModal" tabindex="-1" aria-labelledby="analysisFullscreenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title" id="analysisFullscreenModalLabel" style="color: white; font-weight: 500;">
                    <i class="bi bi-file-earmark-image me-2"></i>原件全屏查看
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0" style="position: relative; overflow: hidden;">
                <div id="analysisFullscreenContent" style="width: 100%; height: calc(100vh - 60px); display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                    <!-- 全屏内容将在这里显示 -->
                    <div class="text-center text-muted">
                        <i class="bi bi-file-earmark-image" style="font-size: 5rem; opacity: 0.3;"></i>
                        <p class="mt-3">准备全屏显示...</p>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩层 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="text-center">
        <div class="spinner-border loading-spinner" role="status">
            <span class="visually-hidden">处理中...</span>
        </div>
        <div class="mt-3 text-white">
            <span id="loadingText">正在处理...</span>
        </div>
    </div>
</div>

{% endblock %}

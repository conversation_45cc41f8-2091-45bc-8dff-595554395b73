# -*- coding: utf-8 -*-
"""
认证相关路由
"""
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
from models import User, db
from utils.auth_utils import log_user_activity, validate_password_strength, is_safe_url

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        # 如果已经登录，重定向到首页
        if current_user.is_authenticated:
            return redirect(url_for('main.index'))
        return render_template('auth/login_modern.html')
    
    # POST请求处理登录
    data = request.get_json() if request.is_json else request.form
    username = data.get('username', '').strip()
    password = data.get('password', '')
    remember = data.get('remember', False)
    
    if not username or not password:
        return jsonify({
            'success': False,
            'message': '用户名和密码不能为空'
        }), 400
    
    # 查找用户
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.check_password(password):
        return jsonify({
            'success': False,
            'message': '用户名或密码错误'
        }), 401
    
    # 检查用户状态
    if user.status != 'active':
        return jsonify({
            'success': False,
            'message': '用户账号已被禁用'
        }), 401
    
    # 登录用户
    login_user(user, remember=remember)
    
    # 更新登录信息
    from services.database_service import DatabaseService
    db_service = DatabaseService()
    db_service.update_user_login(user.id)
    
    # 记录用户活动
    log_user_activity(user, 'login', details={'ip': request.remote_addr})
    
    # 处理重定向
    next_page = request.args.get('next')
    if not next_page or not is_safe_url(next_page):
        next_page = url_for('main.index')
    
    return jsonify({
        'success': True,
        'message': '登录成功',
        'user': user.to_dict(),
        'redirect': next_page
    })

@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """用户登出"""
    # 记录用户活动
    log_user_activity(current_user, 'logout')
    
    # 登出用户
    logout_user()
    
    return jsonify({
        'success': True,
        'message': '已成功登出'
    })

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        return render_template('auth/register.html')
    
    # POST请求处理注册
    data = request.get_json() if request.is_json else request.form
    username = data.get('username', '').strip()
    password = data.get('password', '')
    email = data.get('email', '').strip()
    phone = data.get('phone', '').strip()
    
    # 验证必填字段
    if not username or not password:
        return jsonify({
            'success': False,
            'message': '用户名和密码不能为空'
        }), 400
    
    # 验证用户名长度
    if len(username) < 3 or len(username) > 20:
        return jsonify({
            'success': False,
            'message': '用户名长度应在3-20个字符之间'
        }), 400
    
    # 验证密码强度
    is_valid, message = validate_password_strength(password)
    if not is_valid:
        return jsonify({
            'success': False,
            'message': message
        }), 400
    
    # 检查用户名是否已存在
    existing_user = User.query.filter_by(username=username).first()
    if existing_user:
        return jsonify({
            'success': False,
            'message': '用户名已存在'
        }), 400
    
    # 检查邮箱是否已存在
    if email:
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({
                'success': False,
                'message': '邮箱已被注册'
            }), 400
    
    try:
        # 创建新用户
        user = User(
            username=username,
            email=email,
            phone=phone,
            role='user',  # 默认为普通用户
            status='active'
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        # 记录用户活动
        log_user_activity(user, 'register', details={'ip': request.remote_addr})
        
        return jsonify({
            'success': True,
            'message': '注册成功，请登录',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '注册失败，请稍后重试'
        }), 500

@auth_bp.route('/userinfo', methods=['GET'])
@login_required
def userinfo():
    """获取当前用户信息"""
    from utils.auth_utils import get_user_permissions
    
    user_data = current_user.to_dict()
    user_data['permissions'] = get_user_permissions(current_user)
    
    return jsonify({
        'success': True,
        'user': user_data
    })

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    data = request.get_json()
    old_password = data.get('old_password', '')
    new_password = data.get('new_password', '')
    
    if not old_password or not new_password:
        return jsonify({
            'success': False,
            'message': '旧密码和新密码不能为空'
        }), 400
    
    # 验证旧密码
    if not current_user.check_password(old_password):
        return jsonify({
            'success': False,
            'message': '旧密码错误'
        }), 400
    
    # 验证新密码强度
    is_valid, message = validate_password_strength(new_password)
    if not is_valid:
        return jsonify({
            'success': False,
            'message': message
        }), 400
    
    try:
        # 更新密码
        current_user.set_password(new_password)
        db.session.commit()
        
        # 记录用户活动
        log_user_activity(current_user, 'change_password')
        
        return jsonify({
            'success': True,
            'message': '密码修改成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '密码修改失败，请稍后重试'
        }), 500

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """用户资料"""
    if request.method == 'GET':
        return render_template('auth/profile.html', user=current_user)
    
    # POST请求更新资料
    data = request.get_json()
    email = data.get('email', '').strip()
    phone = data.get('phone', '').strip()
    
    # 检查邮箱是否已被其他用户使用
    if email and email != current_user.email:
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({
                'success': False,
                'message': '邮箱已被其他用户使用'
            }), 400
    
    try:
        # 更新用户信息
        current_user.email = email
        current_user.phone = phone
        db.session.commit()
        
        # 记录用户活动
        log_user_activity(current_user, 'update_profile')
        
        return jsonify({
            'success': True,
            'message': '资料更新成功',
            'user': current_user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '资料更新失败，请稍后重试'
        }), 500

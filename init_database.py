# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 数据库初始化脚本
版本: 3.1
更新日期: 2025-01-08
基于最新的模型结构和update_database.py的改进
"""
import os
import sys
from datetime import datetime, timedelta, date
import random
import json
from decimal import Decimal
from flask import Flask
from sqlalchemy.exc import IntegrityError
from sqlalchemy import inspect

# 导入所有模型
from models import (
    db, User, AnalysisRecord, PromptConfig, PromptVersion, MockData,
    CustomerSystemData, FileTag, ReviewRecord, StandardAnswer, ModelConfig,
    DashboardStats, UserActivity, GlobalSetting
)

# 导入配置
from config import Config

# 数据库版本信息
DATABASE_VERSION = "3.1"

def create_app():
    """创建应用实例"""
    app = Flask(__name__)

    # 使用配置类
    app.config.from_object(Config)

    # 初始化扩展
    db.init_app(app)

    # 初始化应用配置（创建目录等）
    Config.init_app(app)

    return app

def verify_database_structure():
    """验证数据库结构"""
    print("=" * 60)
    print("验证数据库结构...")

    inspector = inspect(db.engine)
    tables = inspector.get_table_names()

    # 期望的表列表（更新为最新的表名）
    expected_tables = [
        'users', 'analysis_records', 'prompt_configs', 'prompt_versions',
        'mock_data', 'customer_system_data', 'file_tags', 'review_records',
        'standard_answers', 'model_configs', 'dashboard_stats',
        'user_activities', 'global_settings'
    ]

    print(f"数据库中的表 ({len(tables)}个):")
    for table in sorted(tables):
        status = "✓" if table in expected_tables else "?"
        print(f"  {status} {table}")

        # 显示表的列信息
        columns = inspector.get_columns(table)
        print(f"    列数: {len(columns)}")

        # 显示前几个重要列
        for i, col in enumerate(columns[:3]):
            print(f"      {col['name']} ({col['type']})")
        if len(columns) > 3:
            print(f"      ... 还有 {len(columns) - 3} 列")
        print()

    # 检查缺失的表
    missing_tables = set(expected_tables) - set(tables)
    if missing_tables:
        print(f"⚠️  缺失的表: {', '.join(missing_tables)}")
    else:
        print("✅ 所有期望的表都已创建")

    # 特别验证 analysis_records 表的关键字段
    if 'analysis_records' in tables:
        verify_analysis_records_structure()

    print("=" * 60)

def verify_analysis_records_structure():
    """验证 analysis_records 表的结构"""
    print("\n验证 analysis_records 表结构...")

    inspector = inspect(db.engine)
    columns = inspector.get_columns('analysis_records')
    column_names = [col['name'] for col in columns]

    # 关键字段列表（基于最新的模型定义）
    required_fields = [
        'id', 'filename', 'analysis_type', 'ai_result', 'expected_result',
        'system_result', 'comparison_result', 'field_accuracy', 'is_first_analysis',
        'status', 'file_status', 'status_changed_by', 'status_changed_at', 'status_reason',
        'file_hash', 'file_info', 'review_status', 'review_priority', 'accuracy_score',
        'audit_status', 'audit_comment', 'audited_by', 'audited_at',
        'review_comment', 'reviewed_by', 'reviewed_at',
        'created_by', 'created_at', 'updated_at'
    ]

    missing_fields = [field for field in required_fields if field not in column_names]

    if missing_fields:
        print(f"  ⚠️  缺失的关键字段: {missing_fields}")
        print("  建议运行 update_database.py 来添加缺失字段")
    else:
        print(f"  ✅ 所有关键字段都存在 (共 {len(column_names)} 个字段)")

    print(f"  字段总数: {len(column_names)}")

def create_default_users():
    """创建默认用户"""
    print("创建默认用户...")

    users_data = [
        {
            'username': 'admin',
            'password': 'admin123',
            'role': 'admin',
            'email': '<EMAIL>',
            'phone': '13800138000'
        },
        {
            'username': 'analyst',
            'password': 'analyst123',
            'role': 'analyst',
            'email': '<EMAIL>',
            'phone': '13800138001'
        },
        {
            'username': 'user',
            'password': 'user123',
            'role': 'user',
            'email': '<EMAIL>',
            'phone': '13800138002'
        }
    ]

    created_count = 0
    for user_data in users_data:
        try:
            existing = User.query.filter_by(username=user_data['username']).first()
            if existing:
                print(f"  用户已存在: {user_data['username']}")
                continue

            user = User(
                username=user_data['username'],
                role=user_data['role'],
                email=user_data['email'],
                phone=user_data['phone'],
                status='active'
            )
            user.set_password(user_data['password'])

            db.session.add(user)
            db.session.commit()

            print(f"  ✓ 创建用户: {user_data['username']} ({user_data['role']})")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 用户创建失败: {user_data['username']} - {str(e)}")

    print(f"用户创建完成，新增 {created_count} 个用户")

def create_default_prompts():
    """创建默认提示词配置"""
    print("创建默认提示词配置...")

    # 从硬编码提示词文件导入
    try:
        from utils.hardcoded_prompts import get_default_prompts
        default_prompts_data = get_default_prompts()
        print(f"  从硬编码文件导入 {len(default_prompts_data)} 个提示词模板")
    except ImportError:
        print("  警告: 无法导入硬编码提示词，使用配置文件中的分析类型创建默认提示词")
        # 使用配置文件中的分析类型
        default_prompts_data = {}
        for analysis_type, type_name in Config.ANALYSIS_TYPES.items():
            default_prompts_data[analysis_type] = {
                'name': type_name,
                'prompt': f'你是一名{type_name}解析专家，请仔细分析上传的文档，提取关键信息并按照指定格式返回结果。\n\n请注意：\n1. 仔细识别文档中的所有关键字段\n2. 确保提取的信息准确无误\n3. 按照JSON格式返回结构化数据\n4. 如果某些字段无法识别，请标注为空值或"未识别"'
            }

    created_count = 0
    for analysis_type, prompt_data in default_prompts_data.items():
        try:
            # 检查是否已存在
            existing = PromptConfig.query.filter_by(
                analysis_type=analysis_type,
                prompt_key='base_prompt'
            ).first()

            if existing:
                print(f"  提示词已存在: {analysis_type}")
                continue

            # 处理不同的数据格式
            if isinstance(prompt_data, dict):
                prompt_content = prompt_data.get('prompt', '')
                description = prompt_data.get('name', analysis_type)
            elif isinstance(prompt_data, str):
                prompt_content = prompt_data
                description = analysis_type
            else:
                print(f"  ✗ 跳过无效的提示词数据: {analysis_type}")
                continue

            # 创建提示词配置
            prompt_config = PromptConfig(
                analysis_type=analysis_type,
                prompt_key='base_prompt',
                prompt_content=prompt_content,
                description=description,
                version='v1.0',
                is_active=True,
                is_default=True
            )

            db.session.add(prompt_config)
            db.session.commit()

            print(f"  ✓ 创建提示词: {analysis_type}")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 提示词创建失败: {analysis_type} - {str(e)}")
        except Exception as e:
            db.session.rollback()
            print(f"  ✗ 提示词创建异常: {analysis_type} - {str(e)}")

    print(f"提示词创建完成，新增 {created_count} 个配置")

def create_default_model_configs():
    """创建默认模型配置"""
    print("创建默认模型配置...")

    # 使用配置文件中的默认模型配置
    default_config = Config.DEFAULT_MODEL_CONFIG

    models_data = [
        {
            'model_id': default_config['model_id'],
            'model_name': default_config['model_name'],
            'api_url': default_config['api_url'],
            'api_key': default_config['api_key'],
            'vision_model': default_config.get('vision_model', ''),
            'timeout': default_config['timeout'],
            'max_tokens': default_config['max_tokens'],
            'temperature': Decimal(str(default_config['temperature'])),
            'is_active': True
        },
        {
            'model_id': 'gpt-4',
            'model_name': 'GPT-4',
            'api_url': 'https://api.openai.com/v1',
            'api_key': 'sk-your-openai-api-key-here',
            'vision_model': 'gpt-4-vision-preview',
            'timeout': 120,
            'max_tokens': 8192,
            'temperature': Decimal('0.70'),
            'is_active': False
        },
        {
            'model_id': 'claude-3-sonnet',
            'model_name': 'Claude 3 Sonnet',
            'api_url': 'https://api.anthropic.com/v1',
            'api_key': 'sk-ant-your-anthropic-api-key-here',
            'timeout': 90,
            'max_tokens': 4096,
            'temperature': Decimal('0.70'),
            'is_active': False
        },
        {
            'model_id': 'internvl3-38b',
            'model_name': 'InternVL3-38B',
            'api_url': 'http://192.168.10.236:23000/v1',
            'api_key': 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553',
            'vision_model': 'InternVL3-38B',
            'timeout': 60,
            'max_tokens': 8192,
            'temperature': Decimal('0.80'),
            'is_active': False
        }
    ]

    created_count = 0
    for model_data in models_data:
        try:
            existing = ModelConfig.query.filter_by(model_id=model_data['model_id']).first()
            if existing:
                print(f"  模型配置已存在: {model_data['model_id']}")
                continue

            model_config = ModelConfig(**model_data)
            db.session.add(model_config)
            db.session.commit()

            print(f"  ✓ 创建模型配置: {model_data['model_id']}")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 模型配置创建失败: {model_data['model_id']} - {str(e)}")

    print(f"模型配置创建完成，新增 {created_count} 个配置")

def create_global_settings(reset_existing=False):
    """创建全局设置"""
    print("创建全局设置...")

    # 从配置文件获取设置
    max_file_size_mb = getattr(Config, 'MAX_CONTENT_LENGTH', 200 * 1024 * 1024) // (1024 * 1024)
    allowed_extensions = ','.join(getattr(Config, 'ALLOWED_EXTENSIONS', ['pdf', 'jpg', 'jpeg', 'png']))

    settings_data = [
        {
            'key': 'system_name',
            'value': Config.SYSTEM_NAME,
            'description': '系统名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'system_version',
            'value': DATABASE_VERSION,  # 使用新的数据库版本
            'description': '系统版本',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'company_name',
            'value': Config.COMPANY_NAME,
            'description': '公司名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'auto_analysis',
            'value': 'true',
            'description': '上传后自动分析',
            'data_type': 'boolean',
            'is_public': True
        },
        {
            'key': 'max_file_size',
            'value': str(max_file_size_mb),
            'description': '最大文件大小(MB)',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'allowed_file_types',
            'value': allowed_extensions,
            'description': '允许的文件类型',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'records_per_page',
            'value': str(Config.RECORDS_PER_PAGE),
            'description': '每页记录数',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'mock_mode_enabled',
            'value': str(Config.MOCK_MODE_ENABLED).lower(),
            'description': '启用挡板模式',
            'data_type': 'boolean',
            'is_public': False
        },
        {
            'key': 'session_timeout',
            'value': '3600',
            'description': '会话超时时间(秒)',
            'data_type': 'integer',
            'is_public': False
        },
        {
            'key': 'enable_audit_log',
            'value': 'true',
            'description': '启用审计日志',
            'data_type': 'boolean',
            'is_public': False
        },
        {
            'key': 'analysis_types',
            'value': json.dumps(Config.ANALYSIS_TYPES, ensure_ascii=False),
            'description': '分析类型配置',
            'data_type': 'json',
            'is_public': True
        }
    ]

    created_count = 0
    updated_count = 0
    for setting_data in settings_data:
        try:
            existing = GlobalSetting.query.filter_by(key=setting_data['key']).first()
            if existing:
                if reset_existing:
                    # 更新现有设置
                    for key, value in setting_data.items():
                        if key != 'key':
                            setattr(existing, key, value)
                    existing.updated_at = datetime.utcnow()
                    db.session.commit()
                    print(f"  ✓ 更新设置: {setting_data['key']}")
                    updated_count += 1
                else:
                    print(f"  设置已存在: {setting_data['key']}")
                continue

            setting = GlobalSetting(**setting_data)
            db.session.add(setting)
            db.session.commit()

            print(f"  ✓ 创建设置: {setting_data['key']}")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 设置创建失败: {setting_data['key']} - {str(e)}")

    if reset_existing and updated_count > 0:
        print(f"全局设置完成，新增 {created_count} 个，更新 {updated_count} 个设置")
    else:
        print(f"全局设置创建完成，新增 {created_count} 个设置")

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")

    # 获取管理员用户ID
    admin_user = User.query.filter_by(role='admin').first()
    analyst_user = User.query.filter_by(role='analyst').first()

    if not admin_user:
        print("  警告: 未找到管理员用户，跳过示例数据创建")
        return

    # 使用配置文件中的分析类型
    analysis_types = list(Config.ANALYSIS_TYPES.keys())

    # 记录数量配置 - 根据分析类型动态分配
    base_counts = [50, 40, 30, 25, 20, 15]  # 递减的基础数量
    record_counts = {}
    for i, analysis_type in enumerate(analysis_types):
        record_counts[analysis_type] = base_counts[i] if i < len(base_counts) else 10

    total_created = 0
    for analysis_type in analysis_types:
        count = record_counts.get(analysis_type, 10)
        print(f"  创建 {analysis_type} 示例记录: {count} 条")

        for i in range(count):
            # 随机创建时间（过去30天内）
            created_at = datetime.now() - timedelta(days=random.randint(0, 30))

            # 随机状态分布
            status_choices = ['pending', 'processing', 'completed', 'failed']
            status_weights = [0.1, 0.15, 0.7, 0.05]  # 70%完成，5%失败
            status = random.choices(status_choices, weights=status_weights)[0]

            # 随机复核状态
            review_status_choices = ['pending', 'approved', 'rejected', 'needs_revision']
            review_status_weights = [0.4, 0.45, 0.1, 0.05]
            review_status = random.choices(review_status_choices, weights=review_status_weights)[0]

            # 创建分析记录
            record = AnalysisRecord(
                filename=f"{analysis_type}_sample_{i+1:03d}.pdf",
                analysis_type=analysis_type,
                status=status,
                file_status='active',  # 新增字段
                review_status=review_status,
                review_priority=random.choice(['low', 'normal', 'high']),
                file_hash=f"hash_{analysis_type}_{i+1}",
                is_first_analysis=False,  # 新增字段
                created_by=admin_user.id if i % 2 == 0 else (analyst_user.id if analyst_user else admin_user.id),
                created_at=created_at,
                updated_at=created_at
            )

            # 如果是已完成状态，添加结果数据
            if status == 'completed':
                # 模拟AI结果
                ai_result = {
                    "extracted_fields": {
                        "customer_name": f"客户{i+1:03d}",
                        "amount": round(random.uniform(1000, 1000000), 2),
                        "date": (created_at - timedelta(days=random.randint(1, 10))).strftime("%Y-%m-%d"),
                        "account_number": f"ACC{random.randint(100000, 999999)}"
                    },
                    "confidence": round(random.uniform(0.8, 1.0), 3)
                }
                record.set_ai_result(ai_result)

                # 设置期望结果（与AI结果相同，表示已验证）
                record.set_expected_result(ai_result)

                # 设置准确率
                record.accuracy_score = Decimal(str(round(random.uniform(0.7, 1.0), 4)))

            db.session.add(record)
            total_created += 1

            # 批量提交
            if total_created % 50 == 0:
                db.session.commit()

    # 最终提交
    db.session.commit()
    print(f"示例数据创建完成，共创建 {total_created} 条分析记录")

def check_database_upgrade():
    """检查数据库是否需要升级"""
    try:
        # 检查是否存在新字段
        inspector = inspect(db.engine)
        if 'analysis_records' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('analysis_records')]

            # 检查关键的新字段
            new_fields = ['expected_result', 'file_status', 'accuracy_score', 'review_status']
            missing_fields = [field for field in new_fields if field not in columns]

            if missing_fields:
                print(f"  检测到缺失字段: {missing_fields}")
                print("  建议运行 update_database.py 进行数据库升级")
                return False
            else:
                print("  数据库结构检查通过")
                return True
        else:
            print("  analysis_records 表不存在，将创建新表")
            return True

    except Exception as e:
        print(f"  警告: 数据库升级检查失败: {str(e)}")
        print("  将继续执行初始化流程")
        return True

def main(args=None):
    """主函数"""
    print("=" * 70)
    print("多场景智能化文档分析系统 - 数据库初始化")
    print(f"版本: {DATABASE_VERSION}")
    print("更新日期: 2025-01-08")
    print("=" * 70)

    # 创建应用上下文
    app = create_app()

    with app.app_context():
        try:
            # 0. 检查数据库升级需求
            print("0. 检查数据库结构...")
            check_database_upgrade()

            # 1. 创建所有表
            print("1. 创建数据库表...")
            db.create_all()
            print("✓ 数据库表创建完成")

            # 2. 验证数据库结构
            verify_database_structure()

            # 3. 创建默认用户
            print("3. 创建默认用户...")
            create_default_users()

            # 4. 创建默认提示词配置
            print("4. 创建默认提示词配置...")
            create_default_prompts()

            # 5. 创建默认模型配置
            print("5. 创建默认模型配置...")
            create_default_model_configs()

            # 6. 创建全局设置
            print("6. 创建全局设置...")
            reset_settings = args and args.reset_settings
            create_global_settings(reset_existing=reset_settings)

            # 7. 创建示例数据（可选）
            if not args or not args.skip_sample_data:
                print("7. 创建示例数据...")
                create_sample_data()

            else:
                print("7. 跳过示例数据创建")

            print("=" * 70)
            print("🎉 数据库初始化完成！")
            print()
            print("📋 初始化摘要:")
            print("  ✓ 数据库表结构已创建")
            print("  ✓ 默认用户已创建")
            print("  ✓ 提示词配置已创建")
            print("  ✓ 模型配置已创建")
            print("  ✓ 全局设置已创建")
            if not args or not args.skip_sample_data:
                print("  ✓ 示例数据已创建")
            print()
            print("👤 默认用户账号:")
            print("  管理员: admin / admin123")
            print("  分析师: analyst / analyst123")
            print("  普通用户: user / user123")
            print()
            print("📊 系统配置:")
            print(f"  系统名称: {Config.SYSTEM_NAME}")
            print(f"  系统版本: {DATABASE_VERSION}")
            print(f"  公司名称: {Config.COMPANY_NAME}")
            print(f"  支持的分析类型: {len(Config.ANALYSIS_TYPES)} 种")
            print()
            print("⚠️  重要提醒:")
            print("  1. 请及时修改默认用户密码")
            print("  2. 请配置正确的AI模型API密钥")
            print("  3. 请根据实际需求调整系统设置")
            print("  4. 如遇到字段缺失问题，请运行 update_database.py")
            print("=" * 70)

        except Exception as e:
            print(f"❌ 数据库初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='数据库初始化脚本')
    parser.add_argument('--skip-sample-data', action='store_true',
                       help='跳过示例数据创建')
    parser.add_argument('--only-structure', action='store_true',
                       help='仅创建数据库结构，不创建示例数据')
    parser.add_argument('--reset-settings', action='store_true',
                       help='重置全局设置（会覆盖现有设置）')

    args = parser.parse_args()

    # 根据参数调整执行流程
    if args.only_structure:
        args.skip_sample_data = True

    main(args)
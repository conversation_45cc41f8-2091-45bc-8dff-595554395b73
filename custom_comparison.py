#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义准确率对比脚本 - 优化版
比较 nb_bank_ai_poc\check\非标交易确认单\************** 与 nb_bank_ai_poc\answer\非标交易确认单
显示详细的字段差异值
"""

import os
import json

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {e}")
        return None

def compare_values(val1, val2):
    """比较两个值是否相等"""
    # 处理字符串比较
    if isinstance(val1, str) and isinstance(val2, str):
        return val1.strip() == val2.strip()

    # 处理数值比较
    if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
        return abs(val1 - val2) < 1e-10

    # 处理字符串数值比较
    if isinstance(val1, str) and isinstance(val2, str):
        try:
            num1 = float(val1.replace(',', ''))
            num2 = float(val2.replace(',', ''))
            return abs(num1 - num2) < 1e-10
        except:
            pass

    return val1 == val2

def format_value_for_display(value):
    """格式化值用于显示"""
    if value is None:
        return "null"
    elif value == "":
        return '""'
    elif isinstance(value, str):
        return f'"{value}"'
    else:
        return str(value)

def compare_transactions_detailed(trans1, trans2):
    """详细比较两个交易记录，返回差异信息"""
    if not isinstance(trans1, dict) or not isinstance(trans2, dict):
        return False, [], {}

    # 定义要比较的字段
    fields = ['投资者名称', '投资者账号', '业务日期', '业务类型',
              '投资标的名称', '投资标的代码', '投资标的金额',
              '投资标的数量', '交易费用']

    different_fields = []
    field_differences = {}

    for field in fields:
        val1 = trans1.get(field, '')
        val2 = trans2.get(field, '')

        if not compare_values(val1, val2):
            different_fields.append(field)
            field_differences[field] = {
                'answer': format_value_for_display(val1),
                'check': format_value_for_display(val2)
            }

    return len(different_fields) == 0, different_fields, field_differences

def compare_files_detailed(answer_file, check_file):
    """详细比较两个JSON文件"""
    answer_data = load_json_file(answer_file)
    check_data = load_json_file(check_file)

    if answer_data is None or check_data is None:
        return False, [], {}, "文件加载失败"

    # 确保都是列表格式
    if isinstance(answer_data, dict):
        answer_data = [answer_data]
    if isinstance(check_data, dict):
        check_data = [check_data]

    if len(answer_data) != len(check_data):
        return False, [], {}, f"交易记录数量不匹配: {len(answer_data)} vs {len(check_data)}"

    all_match = True
    different_fields = []
    all_field_differences = {}

    for i, (ans_trans, chk_trans) in enumerate(zip(answer_data, check_data)):
        is_match, diff_fields, field_diffs = compare_transactions_detailed(ans_trans, chk_trans)
        if not is_match:
            all_match = False
            different_fields.extend(diff_fields)
            if field_diffs:
                all_field_differences[f'交易{i+1}'] = field_diffs

    return all_match, list(set(different_fields)), all_field_differences, ""

def load_filename_mapping(folder_path):
    """加载文件名映射"""
    mapping_file = os.path.join(folder_path, "fn_md5_dict.json")
    if os.path.exists(mapping_file):
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载文件名映射失败: {e}")
    return {}

def get_original_filename(md5_hash, mapping):
    """获取原始文件名"""
    if md5_hash in mapping:
        full_path = mapping[md5_hash]
        # 提取文件名
        return os.path.basename(full_path)
    return "未知原始文件"

def main():
    """主函数"""
    print("🔍 开始自定义准确率对比分析（优化版）...")

    # 设置指定的路径
    answer_folder = os.path.join("answer", "非标交易确认单")
    check_folder = os.path.join("check", "非标交易确认单", "V1.5_20250805185035")

    print(f"📁 标准答案目录: {answer_folder}")
    print(f"📁 测试答案目录: {check_folder}")

    # 加载文件名映射
    print("📋 加载文件名映射...")
    answer_mapping = load_filename_mapping(answer_folder)
    check_mapping = load_filename_mapping(check_folder)

    # 检查文件夹是否存在
    if not os.path.exists(answer_folder):
        print(f"❌ 标准答案文件夹不存在: {answer_folder}")
        return

    if not os.path.exists(check_folder):
        print(f"❌ 测试文件夹不存在: {check_folder}")
        return

    print("-" * 80)

    # 获取所有JSON文件
    answer_files = {f for f in os.listdir(answer_folder) if f.endswith('.json') and f != 'fn_md5_dict.json'}
    check_files = {f for f in os.listdir(check_folder) if f.endswith('.json') and f != 'fn_md5_dict.json'}

    # 找到共同的文件
    common_files = answer_files & check_files

    print(f"📊 文件统计:")
    print(f"   标准答案文件数: {len(answer_files)}")
    print(f"   测试文件数: {len(check_files)}")
    print(f"   共同文件数: {len(common_files)}")
    print()

    if not common_files:
        print("❌ 没有找到共同的文件进行比较")
        return

    # 比较文件
    total_files = len(common_files)
    correct_files = 0
    field_errors = {}

    # 新增：字段匹配率统计
    field_match_stats = {}
    total_field_count = 0
    correct_field_count = 0

    # 定义要统计的字段
    all_fields = ['投资者名称', '投资者账号', '业务日期', '业务类型',
                  '投资标的名称', '投资标的代码', '投资标的金额',
                  '投资标的数量', '交易费用']

    # 初始化字段统计
    for field in all_fields:
        field_match_stats[field] = {'total': 0, 'correct': 0}

    print("📄 文件详细对比结果:")
    print("=" * 80)

    for filename in sorted(common_files):
        answer_file = os.path.join(answer_folder, filename)
        check_file = os.path.join(check_folder, filename)

        # 获取原始文件名
        md5_hash = os.path.splitext(filename)[0]  # 去掉.json扩展名
        original_filename = get_original_filename(md5_hash, answer_mapping)

        is_match, different_fields, field_differences, error_msg = compare_files_detailed(answer_file, check_file)

        if error_msg:
            print(f"❌ {filename}")
            print(f"   📁 原始文件: {original_filename}")
            print(f"   ❌ 错误: {error_msg}")
            print()
            continue

        # 统计字段匹配率
        answer_data = load_json_file(answer_file)
        check_data = load_json_file(check_file)

        if answer_data and check_data:
            # 确保都是列表格式
            if isinstance(answer_data, dict):
                answer_data = [answer_data]
            if isinstance(check_data, dict):
                check_data = [check_data]

            # 统计每个字段的匹配情况
            for ans_trans, chk_trans in zip(answer_data, check_data):
                for field in all_fields:
                    field_match_stats[field]['total'] += 1
                    ans_val = ans_trans.get(field, '')
                    chk_val = chk_trans.get(field, '')

                    if compare_values(ans_val, chk_val):
                        field_match_stats[field]['correct'] += 1

        if is_match:
            correct_files += 1
            print(f"✅ {filename}")
            print(f"   📁 原始文件: {original_filename}")
            print(f"   ✅ 状态: 完全匹配")
        else:
            print(f"❌ {filename}")
            print(f"   📁 原始文件: {original_filename}")
            print(f"   ❌ 状态: 不匹配")
            print(f"   🔸 不同字段: {', '.join(different_fields)}")

            # 显示详细的字段差异
            if field_differences:
                print(f"   📋 详细差异:")
                for transaction_key, field_diffs in field_differences.items():
                    if len(field_differences) > 1:
                        print(f"      {transaction_key}:")
                    for field, diff in field_diffs.items():
                        print(f"      🔸 {field}:")
                        print(f"         标准答案: {diff['answer']}")
                        print(f"         测试结果: {diff['check']}")

            # 统计字段错误
            for field in different_fields:
                field_errors[field] = field_errors.get(field, 0) + 1

        print("-" * 80)

    # 计算总体字段匹配率
    total_field_instances = sum(stats['total'] for stats in field_match_stats.values())
    correct_field_instances = sum(stats['correct'] for stats in field_match_stats.values())
    overall_field_accuracy = (correct_field_instances / total_field_instances * 100) if total_field_instances > 0 else 0

    # 总体统计
    print("📈 总体统计:")
    print(f"   总文件数: {total_files}")
    print(f"   完全匹配文件: {correct_files}")
    print(f"   文件准确率: {correct_files/total_files*100:.2f}%")
    print(f"   总字段实例数: {total_field_instances}")
    print(f"   正确字段实例数: {correct_field_instances}")
    print(f"   所有字段匹配率: {overall_field_accuracy:.2f}%")

    # 各字段匹配率统计
    print("\n📊 各字段匹配率统计:")
    for field in all_fields:
        stats = field_match_stats[field]
        if stats['total'] > 0:
            accuracy = stats['correct'] / stats['total'] * 100
            print(f"   {field}: {stats['correct']}/{stats['total']} ({accuracy:.2f}%)")
        else:
            print(f"   {field}: 0/0 (N/A)")

    if field_errors:
        print("\n📋 字段错误统计:")
        for field, count in sorted(field_errors.items(), key=lambda x: x[1], reverse=True):
            error_rate = count / total_files * 100
            print(f"   {field}: {count}次错误 ({error_rate:.1f}%)")

    # 保存结果到文件
    result_file = "comparison_result_**************.json"

    # 计算各字段准确率
    field_accuracy_rates = {}
    for field in all_fields:
        stats = field_match_stats[field]
        if stats['total'] > 0:
            field_accuracy_rates[field] = stats['correct'] / stats['total'] * 100
        else:
            field_accuracy_rates[field] = 0

    result_data = {
        'timestamp': '**************',
        'total_files': total_files,
        'correct_files': correct_files,
        'file_accuracy': correct_files/total_files*100,
        'total_field_instances': total_field_instances,
        'correct_field_instances': correct_field_instances,
        'overall_field_accuracy': overall_field_accuracy,
        'field_match_stats': field_match_stats,
        'field_accuracy_rates': field_accuracy_rates,
        'field_errors': field_errors
    }

    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    print(f"\n📄 详细结果已保存到: {result_file}")

    print("\n✅ 自定义准确率对比分析完成！")

if __name__ == '__main__':
    main()

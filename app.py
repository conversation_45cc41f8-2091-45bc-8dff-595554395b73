# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 主应用程序
"""
import os
import sys
import uuid
import json
import traceback
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import Flask, request, jsonify, render_template, send_from_directory, redirect, url_for, flash
from flask_cors import CORS
from flask_login import LoginManager, login_user, logout_user, login_required, current_user

# 导入配置和模型
from config import config
from models import db, User, AnalysisRecord
from services.database_service import DatabaseService

# 导入工具函数
from utils.file_utils import allowed_file, get_upload_folder, save_uploaded_file
from utils.auth_utils import require_permission

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_name is None:
        config_name = os.getenv('FLASK_CONFIG') or 'development'

    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    db.init_app(app)
    CORS(app)

    # 初始化Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # 初始化服务
    app.db_service = DatabaseService()

    # 注册蓝图
    register_blueprints(app)

    # 注册错误处理器
    register_error_handlers(app)

    # 注册模板上下文处理器
    register_template_context(app)

    return app

def register_blueprints(app):
    """注册蓝图"""
    from routes.auth import auth_bp
    from routes.api import api_bp
    from routes.main import main_bp
    from routes.direct_prompts import direct_prompts_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(main_bp)
    app.register_blueprint(direct_prompts_bp)

def register_error_handlers(app):
    """注册错误处理器"""

    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'success': False, 'message': '请求参数错误'}), 400

    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({'success': False, 'message': '未授权访问'}), 401

    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({'success': False, 'message': '权限不足'}), 403

    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'success': False, 'message': '资源不存在'}), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

def register_template_context(app):
    """注册模板上下文处理器"""

    @app.context_processor
    def inject_config():
        return {
            'SYSTEM_NAME': app.config.get('SYSTEM_NAME', '多场景智能化文档分析系统'),
            'SYSTEM_VERSION': app.config.get('SYSTEM_VERSION', '2.0.0'),
            'COMPANY_NAME': app.config.get('COMPANY_NAME', '宁波银行'),
            'ANALYSIS_TYPES': app.config.get('ANALYSIS_TYPES', {})
        }

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 检查数据库是否已初始化
    with app.app_context():
        try:
            # 尝试查询用户表，如果失败说明数据库未初始化
            User.query.first()
        except Exception as e:
            print("数据库未初始化，请先运行 python init_database.py")
            sys.exit(1)

    # 启动应用
    print("启动多场景智能化文档分析系统...")
    print(f"访问地址: http://localhost:5106")
    app.run(host='0.0.0.0', port=5106, debug=True)
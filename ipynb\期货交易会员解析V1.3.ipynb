{"cells": [{"cell_type": "code", "execution_count": 1, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from mineru_pdf import *"]}, {"cell_type": "code", "execution_count": 12, "id": "dbddd4b5", "metadata": {}, "outputs": [], "source": ["chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一名期货开户文件解析专家。请严格区分“会员号”（固定 4 位数字）和“交易编码”（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 产品名称：资管计划或产品的正式名称\n", "2. 资金账号：资金账户号码\n", "3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填“/”）\n", "4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为“投机”）\n", "5. 开始时间：写明的开始日期或者函件落款日，有时也被称为开户日期（YYYY-MM-DD；缺失填“/”）\n", "6. 结束时间：文件内表明的截止日期，取不到则为\"/\"（YYYY-MM-DD；缺失填“/”）\n", "\n", "=====================\n", "【交易所名称映射】\n", "- 上期所＝上海期货交易所／上海交易所\n", "- 大商所＝大连商品交易所／大连交易所\n", "- 郑商所＝郑州商品交易所／郑州交易所\n", "- 中金所＝中国金融期货交易所／金融交易所\n", "- 上能所＝上海能源交易所／能源中心\n", "- 广期所＝广州期货交易所／广州交易所\n", "\n", "=====================\n", "【账户用途映射】\n", "- 投机＝投机交易账户\n", "- 套利＝套利交易账户\n", "- 套保＝套期保值交易账户\n", "\n", "若文档未写明用途，默认“投机”。\n", "\n", "=====================\n", "【关键区别提醒】\n", "- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n", "- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n", "- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n", "- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n", "\n", "=====================\n", "【输出 JSON 格式示例】\n", "```json\n", "{\n", "  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "  \"资金账号\": \"2120061\",\n", "  \"会员号\": {\n", "    \"上期所\": \"0121\",\n", "    \"大商所\": \"/\",\n", "    \"郑商所\": \"0059\",\n", "    \"中金所\": \"0170\",\n", "    \"上能所\": \"8059\",\n", "    \"广期所\": \"0021\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"99871700\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"00185013\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"上能所\": {\"投机\": \"81010376\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"04471686\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"2025-01-01\",\n", "  \"结束时间\": \"2025-01-15\"\n", "}\n", "```\n", "    \"\"\"\n", "    )"]}, {"cell_type": "code", "execution_count": 9, "id": "cc532feb", "metadata": {}, "outputs": [], "source": ["# 本代码不采用pdf转扫描式pdf的前置步骤\n", "fn = \"../大模型样例/期货交易会员解析/泰盈期货.PNG_1745473507696.png\"\n", "markdown_data = trans_pdf_to_markdown(fn, parse_method='ocr', backend='vlm-sglang-engine')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 10, "id": "0c69cff4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<table><tr><td>收件箱</td><td>泰盈双季红...</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>回复</td><td>回复全部</td><td>转发</td><td>移动到</td><td>标记为</td><td>更多</td><td>删除</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>泰盈双季红期货账户</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>2024-10-17 10:19:45</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td colspan=\"13\">托管人好，创金合信泰盈双季红6个月定期开放债券型证券投资基金下述外部期货账户已销户</td></tr><tr><td>期货结算账户名</td><td>期货结算账户开户名</td><td>期货结算账号</td><td>期货结算账户开户日期</td><td>期货结算账户是</td><td>期货合约或期相关</td><td>是否有期货备付</td><td>期货备付金返息利率</td><td>期货账户状态</td><td></td><td></td><td></td><td></td></tr><tr><td>宁波银行股份有限公司创金合信泰盈双季红6个月定期开放债券型证券投资基金</td><td>交通银行上海分行</td><td>310066661018800297534</td><td>20190926</td><td>是</td><td>否</td><td>0.0000</td><td>已销户</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>期货结算账户名</td><td>期货结算账号</td><td>期货结算账号</td><td>期货结算账户是</td><td>期货合约或期相关</td><td>是否有期货备付</td><td>期货备付金返息利率</td><td>期货账户状态</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>宁波银行股份有限公司创金合信泰盈双季红6个月定期开放债券型证券投资基金</td><td>交通银行上海分行</td><td>310066661018800297534</td><td>20190926</td><td>是</td><td>否</td><td>0.0000</td><td>已销</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>期货结算账户名</td><td>期货结算账号</td><td>期货结算账号</td><td>期货结算账户是</td><td>期货合约或期相关</td><td>是否有期货备付</td><td>期货备付金返息利率</td><td>期货账户状态</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>宁波银行股份有限公司创金合信泰嘉双季红6个月定期开放债券型证券投资基金</td><td>交通银行上海分行</td><td>310066661018800297534</td><td>20190926</td><td>是</td><td>否</td><td>0.0000</td><td>已销</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>期货结算账户名</td><td>(已销) 0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td></td></tr><tr><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td>0.0000</td><td></td></tr></table>\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 13, "id": "77801cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\",\n", "  \"资金账号\": \"310066661018800297534\",\n", "  \"会员号\": {\n", "    \"上期所\": \"/\",\n", "    \"大商所\": \"/\",\n", "    \"郑商所\": \"/\",\n", "    \"中金所\": \"/\",\n", "    \"上能所\": \"/\",\n", "    \"广期所\": \"/\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"上能所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"2019-09-26\",\n", "  \"结束时间\": \"/\"\n", "}\n", "```\n"]}], "source": ["response = chatbot.chat(markdown_content)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "7bf3474d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c718b718", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23dcc490", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4dd30e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "be3f6955", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
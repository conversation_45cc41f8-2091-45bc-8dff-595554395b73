{% extends "base.html" %}

{% block title %}记录管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .record-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .record-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
    }
    
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-processing {
        background-color: #dbeafe;
        color: #1e40af;
    }
    
    .status-completed {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-failed {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .accuracy-score {
        font-size: 1.1rem;
        font-weight: bold;
    }
    
    .accuracy-excellent {
        color: #059669;
    }
    
    .accuracy-good {
        color: #0891b2;
    }
    
    .accuracy-fair {
        color: #d97706;
    }
    
    .accuracy-poor {
        color: #dc2626;
    }
    
    .filter-section {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .record-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .file-info {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .tag-list {
        display: flex;
        gap: 0.25rem;
        flex-wrap: wrap;
        margin-top: 0.5rem;
    }
    
    .tag-badge {
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
        background-color: #e5e7eb;
        color: #374151;
    }
    
    .search-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .search-input {
        border: none;
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .search-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }
    
    .quick-filters {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }
    
    .quick-filter {
        background-color: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 20px;
        padding: 0.375rem 1rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .quick-filter:hover,
    .quick-filter.active {
        background-color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-text me-2"></i>
        记录管理
    </h2>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="exportRecords()">
            <i class="bi bi-download me-2"></i>
            导出记录
        </button>
        <button class="btn btn-primary" onclick="window.location.href='/'">
            <i class="bi bi-plus-circle me-2"></i>
            新建分析
        </button>
    </div>
</div>

<!-- 搜索区域 -->
<div class="search-section">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="input-group">
                <input type="text" class="form-control search-input" id="searchInput" 
                       placeholder="搜索文件名、分析类型或创建人...">
                <button class="btn btn-light" type="button" onclick="searchRecords()">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-outline-light" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                <i class="bi bi-funnel me-2"></i>
                高级筛选
            </button>
        </div>
    </div>
    
    <!-- 快速筛选 -->
    <div class="quick-filters">
        <span class="quick-filter active" data-filter="all">全部</span>
        <span class="quick-filter" data-filter="today">今日</span>
        <span class="quick-filter" data-filter="week">本周</span>
        <span class="quick-filter" data-filter="month">本月</span>
        <span class="quick-filter" data-filter="completed">已完成</span>
        <span class="quick-filter" data-filter="pending">待处理</span>
        <span class="quick-filter" data-filter="high_accuracy">高准确率</span>
    </div>
</div>

<!-- 高级筛选 -->
<div class="collapse" id="advancedFilters">
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">分析类型</label>
                <select class="form-select" id="analysisTypeFilter">
                    <option value="">全部类型</option>
                    <option value="futures_account">期货账户/开户文件解析</option>
                    <option value="wealth_management">理财产品说明书</option>
                    <option value="broker_interest">券商账户计息变更</option>
                    <option value="account_opening">账户开户场景</option>
                    <option value="ningyin_fee">宁银理财费用变更</option>
                    <option value="non_standard_trade">非标交易确认单解析</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">处理状态</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="pending">待处理</option>
                    <option value="processing">处理中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">文件状态</label>
                <select class="form-select" id="fileStatusFilter">
                    <option value="">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="deprecated">已弃用</option>
                    <option value="archived">已归档</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">准确率范围</label>
                <select class="form-select" id="accuracyFilter">
                    <option value="">全部范围</option>
                    <option value="excellent">优秀 (≥95%)</option>
                    <option value="good">良好 (80-95%)</option>
                    <option value="fair">一般 (60-80%)</option>
                    <option value="poor">较差 (<60%)</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-4">
                <label class="form-label">创建时间范围</label>
                <div class="row">
                    <div class="col-6">
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-6">
                        <input type="date" class="form-control" id="endDate">
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <label class="form-label">创建人</label>
                <input type="text" class="form-control" id="createdByFilter" placeholder="输入创建人姓名">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button class="btn btn-primary me-2" onclick="applyFilters()">
                    <i class="bi bi-search me-2"></i>
                    应用筛选
                </button>
                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    重置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 记录列表 -->
<div class="row" id="recordsList">
    <div class="col-12 text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载记录...</p>
    </div>
</div>

<!-- 分页 -->
<nav id="paginationContainer" style="display: none;">
    <ul class="pagination justify-content-center" id="pagination">
    </ul>
</nav>

<!-- 记录详情模态框 -->
<div class="modal fade" id="recordModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    记录详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="recordDetails">
                    <!-- 记录详情内容将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-info" id="downloadReportBtn">
                    <i class="bi bi-download me-2"></i>
                    下载报告
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 标签管理模态框 -->
<div class="modal fade" id="tagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-tags me-2"></i>
                    管理标签
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="tagForm">
                    <input type="hidden" id="tagRecordId" name="record_id">
                    
                    <div class="mb-3">
                        <label class="form-label">标签名称 *</label>
                        <input type="text" class="form-control" id="tagName" name="tag_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">标签颜色</label>
                        <input type="color" class="form-control form-control-color" id="tagColor" name="tag_color" value="#2563eb">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">标签描述</label>
                        <textarea class="form-control" id="tagDescription" name="tag_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTagBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    保存标签
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const perPage = 12;
    let currentFilters = {};
    let currentRecordId = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadRecords();
        initEventListeners();
        initQuickFilters();
    });

    // 初始化事件监听器
    function initEventListeners() {
        // 搜索输入框
        document.getElementById('searchInput').addEventListener('input',
            Utils.debounce(searchRecords, 500));

        // 保存标签按钮
        document.getElementById('saveTagBtn').addEventListener('click', saveTag);

        // 筛选器变化
        const filters = ['analysisTypeFilter', 'statusFilter', 'fileStatusFilter', 'accuracyFilter'];
        filters.forEach(filterId => {
            document.getElementById(filterId).addEventListener('change', applyFilters);
        });

        // 日期筛选器
        document.getElementById('startDate').addEventListener('change', applyFilters);
        document.getElementById('endDate').addEventListener('change', applyFilters);
        document.getElementById('createdByFilter').addEventListener('input',
            Utils.debounce(applyFilters, 500));
    }

    // 初始化快速筛选
    function initQuickFilters() {
        const quickFilters = document.querySelectorAll('.quick-filter');
        quickFilters.forEach(filter => {
            filter.addEventListener('click', function() {
                // 移除其他激活状态
                quickFilters.forEach(f => f.classList.remove('active'));
                // 添加当前激活状态
                this.classList.add('active');

                // 应用快速筛选
                applyQuickFilter(this.dataset.filter);
            });
        });
    }

    // 应用快速筛选
    function applyQuickFilter(filterType) {
        // 重置所有筛选器
        resetFilters(false);

        const today = new Date();
        const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        switch (filterType) {
            case 'today':
                document.getElementById('startDate').value = new Date().toISOString().split('T')[0];
                document.getElementById('endDate').value = new Date().toISOString().split('T')[0];
                break;
            case 'week':
                document.getElementById('startDate').value = startOfWeek.toISOString().split('T')[0];
                document.getElementById('endDate').value = new Date().toISOString().split('T')[0];
                break;
            case 'month':
                document.getElementById('startDate').value = startOfMonth.toISOString().split('T')[0];
                document.getElementById('endDate').value = new Date().toISOString().split('T')[0];
                break;
            case 'completed':
                document.getElementById('statusFilter').value = 'completed';
                break;
            case 'pending':
                document.getElementById('statusFilter').value = 'pending';
                break;
            case 'high_accuracy':
                document.getElementById('accuracyFilter').value = 'excellent';
                break;
        }

        applyFilters();
    }

    // 搜索记录
    function searchRecords() {
        const searchTerm = document.getElementById('searchInput').value.trim();
        currentFilters.search = searchTerm;
        loadRecords(1);
    }

    // 应用筛选器
    function applyFilters() {
        currentFilters = {
            analysis_type: document.getElementById('analysisTypeFilter').value,
            status: document.getElementById('statusFilter').value,
            file_status: document.getElementById('fileStatusFilter').value,
            accuracy: document.getElementById('accuracyFilter').value,
            start_date: document.getElementById('startDate').value,
            end_date: document.getElementById('endDate').value,
            created_by: document.getElementById('createdByFilter').value.trim(),
            search: document.getElementById('searchInput').value.trim()
        };

        // 移除空值
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });

        loadRecords(1);
    }

    // 重置筛选器
    function resetFilters(reload = true) {
        document.getElementById('analysisTypeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('fileStatusFilter').value = '';
        document.getElementById('accuracyFilter').value = '';
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        document.getElementById('createdByFilter').value = '';
        document.getElementById('searchInput').value = '';

        currentFilters = {};

        if (reload) {
            // 重置快速筛选为"全部"
            document.querySelectorAll('.quick-filter').forEach(f => f.classList.remove('active'));
            document.querySelector('.quick-filter[data-filter="all"]').classList.add('active');

            loadRecords(1);
        }
    }

    // 加载记录
    function loadRecords(page = 1) {
        currentPage = page;

        const params = {
            page: page,
            per_page: perPage,
            ...currentFilters
        };

        API.get('/api/records', params)
            .then(response => {
                if (response.success) {
                    renderRecords(response.data.records);
                    renderPagination(response.data.pagination);
                } else {
                    Utils.showMessage('加载记录失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载记录失败:', error);
                Utils.showMessage('加载记录失败', 'error');
            });
    }

    // 渲染记录列表
    function renderRecords(records) {
        const container = document.getElementById('recordsList');

        if (records.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-file-earmark-text display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无记录</h5>
                    <p class="text-muted">尝试调整筛选条件或创建新的分析记录</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        records.forEach(record => {
            const recordCard = createRecordCard(record);
            container.appendChild(recordCard);
        });
    }

    // 创建记录卡片
    function createRecordCard(record) {
        const col = document.createElement('div');
        col.className = 'col-lg-6 col-xl-4 mb-4';

        const statusClass = getStatusClass(record.status);
        const statusText = getStatusText(record.status);
        const accuracyClass = getAccuracyClass(record.accuracy_score);
        const accuracyPercent = ((record.accuracy_score || 0) * 100).toFixed(1);
        const analysisTypeName = getAnalysisTypeName(record.analysis_type);

        col.innerHTML = `
            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        <span title="${record.filename}">${record.filename.length > 30 ? record.filename.substring(0, 30) + '...' : record.filename}</span>
                    </div>
                    <div class="record-status ${statusClass}">${statusText}</div>
                </div>

                <div class="record-body">
                    <div class="record-info">
                        <div class="info-item">
                            <span class="info-label">分析类型:</span>
                            <span class="info-value">${analysisTypeName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建时间:</span>
                            <span class="info-value">${new Date(record.created_at).toLocaleString()}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">识别率:</span>
                            <span class="info-value">
                                <span class="accuracy-badge ${accuracyClass}">${accuracyPercent}%</span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="record-actions">
                    <button class="btn btn-sm btn-outline-success me-1" onclick="viewResultComparison(${record.id})" title="查看结果对比">
                        <i class="bi bi-clipboard-data"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="reanalyzeFile(${record.id})" title="重新分析">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info me-1" onclick="downloadResult(${record.id})" title="下载结果">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${record.id})" title="删除记录">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;

        return col;
    }

    // 获取状态样式类
    function getStatusClass(status) {
        switch (status) {
            case 'pending': return 'status-pending';
            case 'processing': return 'status-processing';
            case 'completed': return 'status-completed';
            case 'failed': return 'status-failed';
            default: return 'status-pending';
        }
    }

    // 获取状态文本
    function getStatusText(status) {
        switch (status) {
            case 'pending': return '待处理';
            case 'processing': return '处理中';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            default: return status;
        }
    }

    // 获取准确率样式类
    function getAccuracyClass(accuracy) {
        const score = accuracy || 0;
        if (score >= 0.95) return 'accuracy-excellent';
        if (score >= 0.8) return 'accuracy-good';
        if (score >= 0.6) return 'accuracy-fair';
        return 'accuracy-poor';
    }

    // 获取分析类型名称 - 文件列表中显示简洁的四字名称
    function getAnalysisTypeName(type) {
        const names = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return names[type] || type;
    }

    // 查看结果对比
    function viewResultComparison(recordId) {
        if (typeof resultComparison !== 'undefined') {
            resultComparison.showModal(recordId);
        } else {
            Utils.showMessage('结果对比功能未加载', 'error');
        }
    }

    // 重新分析文件
    function reanalyzeFile(recordId) {
        if (confirm('确定要重新分析这个文件吗？')) {
            API.post(`/api/reanalyze/${recordId}`)
                .then(response => {
                    if (response.success) {
                        Utils.showMessage('重新分析完成', 'success');
                        loadRecords(currentPage);
                    } else {
                        Utils.showMessage('重新分析失败: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('重新分析失败:', error);
                    Utils.showMessage('重新分析失败', 'error');
                });
        }
    }

    // 下载结果
    function downloadResult(recordId) {
        window.open(`/api/records/${recordId}/download`, '_blank');
    }

    // 删除记录
    function deleteRecord(recordId) {
        if (confirm('确定要删除这条记录吗？此操作不可恢复。')) {
            API.delete(`/api/records/${recordId}`)
                .then(response => {
                    if (response.success) {
                        Utils.showMessage('记录删除成功', 'success');
                        loadRecords(currentPage);
                    } else {
                        Utils.showMessage('删除失败: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除记录失败:', error);
                    Utils.showMessage('删除失败', 'error');
                });
        }
    }

    // 导出记录
    function exportRecords() {
        const params = new URLSearchParams(currentFilters);
        window.open(`/api/records/export?${params.toString()}`, '_blank');
    }
</script>

<!-- 引入结果对比模板 -->
{% include 'result_comparison_modal.html' %}

<script src="{{ url_for('static', filename='js/result_comparison.js') }}"></script>
{% endblock %}

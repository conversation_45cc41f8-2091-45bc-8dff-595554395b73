<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>用户登录 - {{ SYSTEM_NAME }}</title>

    <!-- CSS样式 - 使用本地样式替代CDN -->
    <style>
      /* Bootstrap基础样式 */
      * {
        box-sizing: border-box;
      }

      .container,
      .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
      }

      .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
      }

      .col,
      .col-12 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
      }

      .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        background-color: transparent;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        text-decoration: none;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }

      .btn-primary {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
      }

      .btn-primary:hover {
        color: #fff;
        background-color: #0b5ed7;
        border-color: #0a58ca;
      }

      .btn:disabled {
        opacity: 0.65;
        cursor: not-allowed;
      }

      .w-100 {
        width: 100% !important;
      }

      .form-control {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }

      .form-control:focus {
        color: #212529;
        background-color: #fff;
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }

      .input-group {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        width: 100%;
      }

      .input-group-text {
        display: flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: center;
        white-space: nowrap;
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
      }

      .input-group .form-control {
        position: relative;
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
      }

      .input-group .input-group-text:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .input-group .form-control:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      .form-check {
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5em;
        margin-bottom: 0.125rem;
      }

      .form-check-input {
        width: 1em;
        height: 1em;
        margin-top: 0.25em;
        vertical-align: top;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        border: 1px solid rgba(0, 0, 0, 0.25);
        appearance: none;
        color-adjust: exact;
      }

      .form-check-input[type="checkbox"] {
        border-radius: 0.25em;
      }

      .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
      }

      .form-check-input:checked[type="checkbox"] {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
      }

      .form-check-label {
        color: #212529;
        cursor: pointer;
      }

      .alert {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.25rem;
      }

      .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
      }

      .alert-success {
        color: #0f5132;
        background-color: #d1e7dd;
        border-color: #badbcc;
      }

      .alert-danger {
        color: #842029;
        background-color: #f8d7da;
        border-color: #f5c2c7;
      }

      .alert-warning {
        color: #664d03;
        background-color: #fff3cd;
        border-color: #ffecb5;
      }

      .alert-dismissible {
        padding-right: 3rem;
      }

      .btn-close {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 2;
        padding: 0.75rem 1.25rem;
        color: inherit;
        background: transparent
          url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='m.235.867 8 8a.5.5 0 0 1 0 .707l-8 8a.5.5 0 1 1-.707-.707L7.293 8.5.528 1.735a.5.5 0 0 1 .707-.707z'/%3e%3c/svg%3e")
          center/1em auto no-repeat;
        border: 0;
        border-radius: 0.25rem;
        opacity: 0.5;
      }

      .btn-close:hover {
        color: inherit;
        text-decoration: none;
        opacity: 0.75;
      }

      .fade {
        transition: opacity 0.15s linear;
      }

      .show {
        opacity: 1;
      }

      .mb-0 {
        margin-bottom: 0 !important;
      }
      .mb-1 {
        margin-bottom: 0.25rem !important;
      }
      .mb-3 {
        margin-bottom: 1rem !important;
      }
      .mt-2 {
        margin-top: 0.5rem !important;
      }
      .mt-3 {
        margin-top: 1rem !important;
      }
      .mt-4 {
        margin-top: 1.5rem !important;
      }
      .me-2 {
        margin-right: 0.5rem !important;
      }
      .text-center {
        text-align: center !important;
      }
      .text-muted {
        color: #6c757d !important;
      }
      .text-decoration-none {
        text-decoration: none !important;
      }
      .opacity-75 {
        opacity: 0.75 !important;
      }

      /* 图标字体替代 */
      .bi::before {
        display: inline-block;
        font-family: monospace;
        font-weight: bold;
      }

      .bi-bank::before {
        content: "🏦";
      }
      .bi-person::before {
        content: "👤";
      }
      .bi-lock::before {
        content: "🔒";
      }
      .bi-box-arrow-in-right::before {
        content: "➡️";
      }
      .bi-info-circle::before {
        content: "ℹ️";
      }
      .bi-hourglass-split::before {
        content: "⏳";
      }
    </style>

    <style>
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 400px;
        width: 100%;
      }

      .login-header {
        background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .login-body {
        padding: 2rem;
      }

      .form-control {
        border-radius: 10px;
        border: 2px solid #e5e7eb;
        padding: 12px 16px;
        transition: all 0.3s ease;
      }

      .form-control:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
      }

      .btn-login {
        background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
      }

      .input-group-text {
        background: #f8fafc;
        border: 2px solid #e5e7eb;
        border-right: none;
      }

      .input-group .form-control {
        border-left: none;
      }

      .alert {
        border-radius: 10px;
        border: none;
      }
    </style>
  </head>
  <body>
    <div class="login-card">
      <div class="login-header">
        <h3 class="mb-0">
          <i class="bi bi-bank me-2"></i>
          {{ SYSTEM_NAME }}
        </h3>
        <p class="mb-0 mt-2 opacity-75">用户登录</p>
      </div>

      <div class="login-body">
        <!-- 消息提示 -->
        <div id="messageContainer"></div>

        <form id="loginForm">
          <div class="mb-3">
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-person"></i>
              </span>
              <input
                type="text"
                class="form-control"
                id="username"
                name="username"
                placeholder="请输入用户名"
                required
              />
            </div>
          </div>

          <div class="mb-3">
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-lock"></i>
              </span>
              <input
                type="password"
                class="form-control"
                id="password"
                name="password"
                placeholder="请输入密码"
                required
              />
            </div>
          </div>

          <div class="mb-3 form-check">
            <input
              type="checkbox"
              class="form-check-input"
              id="remember"
              name="remember"
            />
            <label class="form-check-label" for="remember"> 记住我 </label>
          </div>

          <button
            type="submit"
            class="btn btn-primary btn-login w-100"
            id="loginBtn"
          >
            <i class="bi bi-box-arrow-in-right me-2"></i>
            登录
          </button>
        </form>

        <div class="text-center mt-3">
          <p class="text-muted">
            还没有账号？
            <a
              href="{{ url_for('auth.register') }}"
              class="text-decoration-none"
              >立即注册</a
            >
          </p>
        </div>

        <!-- 默认账号提示 -->
        <div class="mt-4">
          <div class="alert alert-info">
            <h6 class="alert-heading">
              <i class="bi bi-info-circle me-2"></i>
              默认测试账号
            </h6>
            <hr />
            <p class="mb-1"><strong>管理员:</strong> admin / admin123</p>
            <p class="mb-1"><strong>分析师:</strong> analyst / analyst123</p>
            <p class="mb-0"><strong>普通用户:</strong> user / user123</p>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript - 使用原生JavaScript替代CDN依赖 -->

    <script>
      function showMessage(message, type = "info") {
        const container = document.getElementById("messageContainer");
        const alertClass =
          type === "error"
            ? "alert-danger"
            : type === "success"
            ? "alert-success"
            : type === "warning"
            ? "alert-warning"
            : "alert-info";

        const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

        container.innerHTML = alertHtml;
      }

      document
        .getElementById("loginForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const formData = new FormData(this);
          const loginBtn = document.getElementById("loginBtn");
          const originalText = loginBtn.innerHTML;

          // 显示加载状态
          loginBtn.innerHTML =
            '<i class="bi bi-hourglass-split me-2"></i>登录中...';
          loginBtn.disabled = true;

          // 发送登录请求
          axios
            .post("/auth/login", {
              username: formData.get("username"),
              password: formData.get("password"),
              remember: formData.get("remember") === "on",
            })
            .then((response) => {
              if (response.data.success) {
                showMessage("登录成功，正在跳转...", "success");
                setTimeout(() => {
                  window.location.href = response.data.redirect || "/";
                }, 1000);
              } else {
                showMessage(response.data.message, "error");
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
              }
            })
            .catch((error) => {
              console.error("登录失败:", error);
              let message = "登录失败，请稍后重试";
              if (
                error.response &&
                error.response.data &&
                error.response.data.message
              ) {
                message = error.response.data.message;
              }
              showMessage(message, "error");
              loginBtn.innerHTML = originalText;
              loginBtn.disabled = false;
            });
        });

      // 回车键登录
      document.addEventListener("keypress", function (e) {
        if (e.key === "Enter") {
          document
            .getElementById("loginForm")
            .dispatchEvent(new Event("submit"));
        }
      });
    </script>
  </body>
</html>

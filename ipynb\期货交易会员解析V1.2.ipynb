{"cells": [{"cell_type": "code", "execution_count": 4, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import *\n", "from mineru_pdf import *\n", "from PIL import Image\n", "import json\n", "from util_img_process import process_pdf\n", "import hashlib\n", "\n", "def pdf2img(fn):\n", "    \"\"\"\n", "    强制使用ocr，转换pdf为图片形式的pdf\n", "    \"\"\"\n", "    # 如果是PDF文件，先转换为图片\n", "    if fn.lower().endswith('.pdf'):\n", "        md5_value = cal_file_md5(fn)\n", "        processed_filename = f\"processed_{md5_value}.jpg\"\n", "        os.makedirs('./temp', exist_ok=True)\n", "        processed_filepath = f\"./temp/{processed_filename}\"\n", "\n", "        # 使用PDF处理函数\n", "        process_pdf(fn, processed_filepath)\n", "        analysis_filepath = processed_filepath\n", "    else:\n", "        analysis_filepath = fn\n", "    return analysis_filepath\n", "\n", "def cal_file_md5(fn):\n", "    \"\"\"\n", "    计算文件的md5值\n", "    \"\"\"\n", "    with open(fn, 'rb') as f:\n", "        return hashlib.md5(f.read()).hexdigest()"]}, {"cell_type": "code", "execution_count": 2, "id": "dbddd4b5", "metadata": {}, "outputs": [], "source": ["chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一个期货开户文件解析专家，请对用户提供的期货开户文件进行解析，并输出解析结果。\n", "    1. 产品名称（一般指代资管计划的名称）\n", "    2. 资金账号\n", "    3. 交易所会员号（4位数字）也被称为会员编号\n", "    4. 交易编码（8位数字）\n", "    5. 开始时间（格式：YYYY-MM-DD）, 写明的开始日期或者函件落款日期，如果未提及则输出\"/\"\n", "    6. 结束时间（格式：YYYY-MM-DD）, 写明的结束日期或者函件落款日期，如果未提及则输出\"/\"\n", "    7. 请勿遗漏任何一个交易所，如该交易所不存在或未提及则输出\"/\"\n", "    8. 交易所名称：\n", "      - 上期所：也被称作上海期货交易所、上海交易所\n", "      - 大商所：也被称作大连商品交易所、大连交易所\n", "      - 郑商所：也被称作郑州商品交易所、郑州交易所\n", "      - 中金所：也被称作中国金融期货交易所、金融交易所\n", "      - 上能所：也被称作上海能源交易所、能源中心\n", "      - 广期所：也被称作广州期货交易所、广州交易所\n", "    9. 交易编码中的账户类型：\n", "      - 投机：投机交易账户，有时也被称作\"交易账户\"\n", "      - 套利：套利交易账户\n", "      - 套保：套期保值交易账户\n", "    10. 如未提及账户的交易用途，默认为投机\n", "    11. 对于未提及的信息，请输出\"/\"\n", "    12. 会员号仅输出交易所名称，不输出账户类型，如\"中金所（投机）\"，则输出\"中金所\"\n", "\n", "    输出示例：\n", "    ```json\n", "    {\n", "      \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "      \"资金账号\": \"2120061\",\n", "      \"会员号\": {\n", "        \"上期所\": \"0121\",\n", "        \"大商所\": \"/\",\n", "        \"郑商所\": \"0059\",\n", "        \"中金所\": \"0170\",\n", "        \"上能所\": \"8059\",\n", "        \"广期所\": \"0021\"\n", "      },\n", "      \"交易编码\": {\n", "          \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n", "          \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "          \"郑商所\": {\"投机\": \"99871700\", \"套利\": \"/\", \"套保\": \"/\"},\n", "          \"中金所\": {\"投机\": \"00185013\", \"套利\": \"/\", \"套保\": \"/\"},\n", "          \"上能所\": {\"投机\": \"81010376\", \"套利\": \"/\", \"套保\": \"/\"},\n", "          \"广期所\": {\"投机\": \"04471686\", \"套利\": \"/\", \"套保\": \"/\"}\n", "      },\n", "      \"开始时间\": \"2025-01-01\",\n", "      \"结束时间\": \"2025-01-15\"\n", "    }\n", "    ```\n", "    \"\"\"\n", "    )"]}, {"cell_type": "code", "execution_count": 20, "id": "cc532feb", "metadata": {}, "outputs": [], "source": ["fn = \"../大模型样例/期货交易会员解析/均衡成长-期货户-中信期货.pdf_1745473949235.pdf\"\n", "\n", "# # 将pdf转为图片形式的pdf,以启用ocr\n", "# # 强制OCR\n", "# fn = pdf2img(fn)\n", "# if fn.lower().endswith('.pdf'):\n", "#     out_pdf_fn = fn\n", "# else:\n", "#     # 将图片转为pdf\n", "#     fn_end = fn.split('.')[-1]  \n", "#     fix_fn = \".\".join(fn.split('.')[:-1]) + \".pdf\"\n", "#     out_pdf_fn = convert_images_to_pdf(fn, fix_fn)\n", "\n", "markdown_data = trans_pdf_to_markdown(fn, parse_method='ocr')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 21, "id": "0c69cff4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 期货账户开户信息\n", "\n", "中欧基金管理有限公司、宁波银行股份有限公司：\n", "\n", "中欧均衡成长混合型证券投资基金已完成期货开户申请手续，具体信息如下：\n", "\n", "# 1、 开户信息\n", "\n", "<html><body><table><tr><td>资金账号</td><td colspan=\"2\">120520336</td></tr><tr><td>交易编码对应名称</td><td colspan=\"2\">中欧基金管理有限公司-宁波银行股份有限公司-中欧 均衡成长混合型证券投资基金</td></tr><tr><td>交易所</td><td>会员号</td><td>交易编码</td></tr><tr><td>中金所</td><td>0018</td><td>交易：／套保：00094820</td></tr><tr><td>上期所</td><td>0148</td><td>/</td></tr><tr><td>大商所</td><td>0110</td><td>/</td></tr><tr><td>郑商所</td><td>0055</td><td>/</td></tr><tr><td>能源中心</td><td>8148</td><td>/</td></tr><tr><td>广期所</td><td>0008</td><td>/</td></tr></table></body></html>\n", "\n", "# 2、 保证金监控中心信息\n", "\n", "<html><body><table><tr><td>统一开户编码</td><td>/</td></tr><tr><td>期货市场监控中心账号</td><td>/</td></tr><tr><td>期货市场监控中心账号密码</td><td>/</td></tr></table></body></html>\n", "\n", "3、 期货公司保证金账户信息\n", "\n", "<html><body><table><tr><td>公司名称</td><td>中信期货有限公司</td></tr><tr><td>公司保证金账号</td><td>443066041018010049863</td></tr><tr><td>开户行网点</td><td>交通银行深圳红荔支行</td></tr></table></body></html>\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 22, "id": "77801cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"中欧均衡成长混合型证券投资基金\",\n", "  \"资金账号\": \"120520336\",\n", "  \"会员号\": {\n", "    \"上期所\": \"0148\",\n", "    \"大商所\": \"0110\",\n", "    \"郑商所\": \"0055\",\n", "    \"中金所\": \"0018\",\n", "    \"上能所\": \"8148\",\n", "    \"广期所\": \"0008\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"00094820\"},\n", "    \"上能所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"/\",\n", "  \"结束时间\": \"/\"\n", "}\n", "```\n"]}], "source": ["response = chatbot.chat(markdown_content + \"\\n/no_think\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "7bf3474d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c718b718", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23dcc490", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4dd30e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "be3f6955", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "ocr"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
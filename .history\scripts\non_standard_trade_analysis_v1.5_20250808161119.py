# 非标交易确认单解析 - V1.5优化版（PNG识别增强）
"""
V1.5版本主要优化内容：
1. PNG图片识别准确率大幅提升：专注InternVL3-38B单一优质模型、图片预处理、智能策略选择
2. 继承V1.3所有功能：数值格式优化、千分位分隔符处理、引号处理等
3. 新增图片预处理：去噪、对比度增强、分辨率优化
4. 专注单一优质模型：InternVL3-38B 专用优化，移除备用模型依赖
5. 智能质量评估：基于内容完整性、数值准确性、结构化程度的综合评分
6. PNG专用优化：针对PNG格式的特殊处理流程
7. 数值识别增强：优化千分位分隔符处理，防止数值截断
"""
import sys
import os
# 获取当前脚本所在目录的上级目录（项目根目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)  # 使用 insert(0, ...) 确保优先级

# 解决模块命名冲突问题 - 使用 importlib 直接加载 utils.py 文件
import importlib.util

# 直接指定 utils.py 文件路径
utils_file_path = os.path.join(project_root, 'utils.py')
spec = importlib.util.spec_from_file_location("utils_direct", utils_file_path)
utils_module = importlib.util.module_from_spec(spec)

# 临时修改 sys.modules 避免循环导入
original_utils = sys.modules.get('utils')
sys.modules['utils'] = utils_module

try:
    spec.loader.exec_module(utils_module)
    print("[SUCCESS] utils.py 模块加载成功")
except Exception as e:
    print(f"[ERROR] utils.py 模块加载失败: {e}")
    # 恢复原始模块
    if original_utils:
        sys.modules['utils'] = original_utils
    else:
        sys.modules.pop('utils', None)
    sys.exit(1)

# 导入 ChatBot
from util_ai import ChatBot

# 从 utils.py 获取需要的函数
fn_to_markdown_v2 = utils_module.fn_to_markdown_v2
process_file_to_base64 = utils_module.process_file_to_base64
markdown_json_to_dict = utils_module.markdown_json_to_dict
cal_fn_md5 = utils_module.cal_fn_md5

print("[SUCCESS] 所有函数导入成功")
import json
import tqdm
import datetime
import inspect
import time
import pandas as pd
import io
import re

# V1.5版本新增：图片处理库
try:
    from PIL import Image, ImageEnhance, ImageFilter
    import cv2
    import numpy as np
    PIL_AVAILABLE = True
    CV2_AVAILABLE = True
    print("✓ 图片处理库已加载：PIL, OpenCV")
except ImportError as e:
    PIL_AVAILABLE = False
    CV2_AVAILABLE = False
    print(f"⚠ 图片处理库未安装: {e}")
    print("建议安装: pip install Pillow opencv-python")

DIR_NAME = "非标交易确认单"

# 继承V1.3版本的所有数值处理函数
def format_numeric_value(value):
    """格式化数值，保留两位小数的字符串格式，去除金融标识符，正确处理千分位分隔符"""
    if value is None or value == "" or str(value).strip() == "":
        return "/"

    # 转换为字符串并去除首尾空格
    value_str = str(value).strip()

    # 如果已经是"/"，直接返回
    if value_str == "/":
        return "/"

    # 先去除金融标识符（￥、$、€、£、RMB等）
    # 更全面的金融标识符清理
    financial_symbols = ['￥', '$', '€', '£', '¥', 'RMB', 'USD', 'EUR', 'GBP', 'CNY']
    clean_str = value_str
    for symbol in financial_symbols:
        clean_str = clean_str.replace(symbol, '')
    
    # 去除其他可能的非数字字符（但保留数字、小数点、逗号、负号）
    clean_str = re.sub(r'[^\d.,\-]', '', clean_str).strip()
    
    # 如果清理后为空，返回"/"
    if not clean_str:
        return "/"

    # V1.5版本：超级优化的数值匹配正则表达式，确保完整匹配千分位分隔符
    number_patterns = [
        r'^[-]?\d{1,3}(?:,\d{3})+(?:\.\d{1,6})?$',  # 标准千分位格式：47,047,164.46
        r'^[-]?\d{4,}(?:\.\d{1,6})?$',              # 长数字格式：47047164.46
        r'^[-]?\d{1,3}(?:\.\d{1,6})?$'              # 短数字格式：470.00
    ]
    
    matched_number = None
    for pattern in number_patterns:
        if re.match(pattern, clean_str):
            matched_number = clean_str
            break
    
    if matched_number:
        try:
            # 去除千分位分隔符，然后转换为浮点数
            clean_number_str = matched_number.replace(',', '')
            number = float(clean_number_str)
            # 格式化为两位小数的字符串
            return f"{number:.2f}"
        except ValueError:
            return "/"
    else:
        # 如果没有匹配到标准格式，尝试提取所有数字和小数点
        # 这是一个备用方案，处理一些特殊格式
        digits_and_dots = re.findall(r'[\d.]', clean_str)
        if digits_and_dots:
            try:
                # 重新组合数字，去除多余的小数点
                number_str = ''.join(digits_and_dots)
                # 处理多个小数点的情况，只保留第一个
                if number_str.count('.') > 1:
                    parts = number_str.split('.')
                    number_str = parts[0] + '.' + ''.join(parts[1:])
                
                # 处理负号
                if '-' in clean_str and not number_str.startswith('-'):
                    number_str = '-' + number_str
                
                number = float(number_str)
                return f"{number:.2f}"
            except ValueError:
                return "/"
        
        return "/"

def validate_comma_separated_number(number_str):
    """验证千分位分隔符格式的数值是否完整和正确"""
    if not number_str or ',' not in number_str:
        return False

    # 检查千分位格式是否正确
    # 正确格式：1,234.56 或 12,345,678.90
    pattern = r'^-?\d{1,3}(?:,\d{3})+(?:\.\d+)?$'
    if not re.match(pattern, number_str):
        return False

    # 额外检查：确保不是被截断的数值
    # 如果数值太短（如只有470），可能是截断的
    clean_number = number_str.replace(',', '').replace('.', '').replace('-', '')
    if len(clean_number) < 3:  # 太短的数值可能是截断的
        return False

    return True

def check_number_completeness(number_str, context_line=""):
    """检查数值是否完整，防止截断"""
    if not number_str:
        return False

    # 去除金融符号和空格
    clean_number = re.sub(r'[^\d.,\-]', '', number_str)

    # 如果包含千分位分隔符，验证格式
    if ',' in clean_number:
        return validate_comma_separated_number(clean_number)

    # 对于普通数值，检查是否可能被截断
    # 如果上下文中有更长的数值，当前数值可能是截断的
    if context_line:
        # 在上下文中查找更长的数值
        longer_numbers = re.findall(r'\d{' + str(len(clean_number.replace('.', '').replace('-', '')) + 1) + ',}', context_line)
        if longer_numbers:
            return False  # 可能被截断了

    return True

def clean_quotes(text):
    """处理引号，保持中文引号和英文引号的原样"""
    if not isinstance(text, str):
        return text

    # 不进行引号转换，保持原样
    return text

# V1.5版本新增：PNG图片预处理函数
def preprocess_png_image(image_path, output_path=None):
    """
    PNG图片预处理，提高OCR识别准确率
    """
    if not PIL_AVAILABLE:
        print("PIL库未安装，跳过图片预处理")
        return image_path
    
    try:
        print(f"开始预处理PNG图片: {image_path}")
        
        # 读取图片
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                else:
                    background.paste(img, mask=img.split()[-1])
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.2)  # 增强20%对比度
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.1)  # 增强10%锐度
            
            # 如果图片太小，进行放大
            width, height = img.size
            if width < 1000 or height < 1000:
                scale_factor = max(1000/width, 1000/height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                print(f"图片已放大: {width}x{height} → {new_width}x{new_height}")
            
            # 保存预处理后的图片
            if output_path is None:
                base_name = os.path.splitext(image_path)[0]
                output_path = f"{base_name}_preprocessed.png"
            
            img.save(output_path, 'PNG', quality=95)
            print(f"预处理完成，保存到: {output_path}")
            return output_path
            
    except Exception as e:
        print(f"图片预处理失败: {str(e)}")
        return image_path

def preprocess_png_image_cv2(image_path, output_path=None):
    """
    使用OpenCV进行PNG图片预处理（备用方案）
    """
    if not CV2_AVAILABLE:
        return image_path
    
    try:
        print(f"使用OpenCV预处理PNG图片: {image_path}")
        
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print("OpenCV无法读取图片，使用原图")
            return image_path
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 去噪
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # 自适应阈值处理
        adaptive_thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作，去除噪点
        kernel = np.ones((2,2), np.uint8)
        cleaned = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
        
        # 保存预处理后的图片
        if output_path is None:
            base_name = os.path.splitext(image_path)[0]
            output_path = f"{base_name}_cv2_preprocessed.png"
        
        cv2.imwrite(output_path, cleaned)
        print(f"OpenCV预处理完成，保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"OpenCV预处理失败: {str(e)}")
        return image_path

# V1.5版本新增：智能质量评估函数
def evaluate_ocr_quality(markdown_content):
    """
    智能评估OCR结果质量
    """
    if not markdown_content or not markdown_content.strip():
        return 0
    
    content = markdown_content.strip()
    score = 0
    
    # 基础分数：内容长度
    length_score = min(len(content) / 10, 100)  # 最高100分
    score += length_score
    
    # 关键字段检测
    key_fields = {
        '投资者': 50,
        '账号': 40,
        '金额': 60,
        '数量': 40,
        '费用': 30,
        '日期': 40,
        '业务类型': 50,
        '标的': 40,
        '代码': 30
    }
    
    for field, points in key_fields.items():
        if field in content:
            score += points
    
    # 数值检测（包含小数点的数字）
    number_pattern = r'\d+\.\d+'
    numbers = re.findall(number_pattern, content)
    score += min(len(numbers) * 20, 100)  # 每个数值20分，最高100分
    
    # 表格结构检测
    if '|' in content and '---' in content:
        score += 50  # 表格结构加分
    
    # 千分位分隔符检测
    comma_numbers = re.findall(r'\d{1,3}(?:,\d{3})+(?:\.\d+)?', content)
    score += len(comma_numbers) * 15  # 每个千分位数值15分
    
    # 完整性检测：是否包含多个关键信息
    completeness_indicators = ['名称', '账号', '金额', '日期', '类型']
    completeness_score = sum(20 for indicator in completeness_indicators if indicator in content)
    score += completeness_score
    
    print(f"OCR质量评分: {score:.1f} (长度:{length_score:.1f}, 数值:{len(numbers)}, 千分位:{len(comma_numbers)}, 完整性:{completeness_score})")
    return score

# V1.5版本核心：PNG文件超级优化处理函数
def process_png_file_super_optimized(fn, usage_model):
    """
    V1.5版本PNG文件超级优化处理，大幅提升识别准确率
    """
    print(f"=== V1.5版本PNG超级优化处理开始 ===")
    print(f"处理文件: {fn}")

    # 准备多个预处理版本
    processed_images = [fn]  # 原图

    # 1. PIL预处理版本
    if PIL_AVAILABLE:
        try:
            pil_processed = preprocess_png_image(fn)
            if pil_processed != fn:
                processed_images.append(pil_processed)
        except Exception as e:
            print(f"PIL预处理失败: {e}")

    # 2. OpenCV预处理版本
    if CV2_AVAILABLE:
        try:
            cv2_processed = preprocess_png_image_cv2(fn)
            if cv2_processed != fn:
                processed_images.append(cv2_processed)
        except Exception as e:
            print(f"OpenCV预处理失败: {e}")

    print(f"准备了 {len(processed_images)} 个图片版本进行OCR")

    # 多策略OCR处理
    strategies = [
        {"convert_to_scanned": False, "ai_seal": True, "description": "直接处理模式"},
        {"convert_to_scanned": True, "ai_seal": True, "description": "扫描模式"},
        {"convert_to_scanned": False, "ai_seal": False, "description": "无印章模式"},
        {"convert_to_scanned": True, "ai_seal": False, "description": "扫描无印章模式"},
    ]

    best_result = None
    best_score = 0
    best_info = ""

    # 对每个预处理版本尝试所有策略
    for img_idx, img_path in enumerate(processed_images):
        img_name = "原图" if img_idx == 0 else f"预处理版本{img_idx}"
        print(f"\n--- 处理 {img_name}: {os.path.basename(img_path)} ---")

        for strategy_idx, strategy in enumerate(strategies):
            try:
                print(f"尝试策略 {strategy_idx+1}: {strategy['description']}")

                # 执行OCR
                markdown_content, seal_img_list = fn_to_markdown_v2(
                    img_path,
                    convert_to_scanned=strategy["convert_to_scanned"],
                    ai_seal=strategy["ai_seal"],
                    ai_model=usage_model
                )

                # 评估质量
                quality_score = evaluate_ocr_quality(markdown_content)

                print(f"策略 {strategy_idx+1} 质量评分: {quality_score:.1f}")

                if quality_score > best_score:
                    best_result = (markdown_content, seal_img_list)
                    best_score = quality_score
                    best_info = f"{img_name} + {strategy['description']}"
                    print(f"✓ 发现更好结果！当前最佳: {best_info} (评分: {quality_score:.1f})")

            except Exception as e:
                print(f"✗ 策略 {strategy_idx+1} 失败: {str(e)}")
                continue

    # 清理临时文件
    for img_path in processed_images[1:]:  # 保留原图，删除预处理版本
        try:
            if os.path.exists(img_path):
                os.remove(img_path)
                print(f"已清理临时文件: {img_path}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    if best_result:
        print(f"\n=== PNG超级优化处理完成 ===")
        print(f"最佳结果: {best_info}")
        print(f"最佳评分: {best_score:.1f}")
        return best_result
    else:
        print("\n=== 所有策略都失败，使用默认处理 ===")
        return fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)

# V1.5版本：专注单一优质模型的增强OCR处理
def enhanced_single_model_ocr(fn, markdown_content):
    """
    专注InternVL3-38B单一优质模型的增强OCR处理
    """
    print("开始增强OCR处理...")

    # 使用InternVL3-38B进行补充OCR，专注于数值和关键信息
    try:
        print("使用InternVL3-38B进行补充OCR...")

        # V1.5版本：专门针对红利转投文档的OCR提示词
        ocr_prompt = """你是一名专业的OCR专家，请仔细识别图片中的所有文字内容，特别注意：

【超级重要】表格数据识别：
1. 准确识别表格中每一行、每一列的数据
2. 区分表头和数据行
3. 正确识别多行数据的分隔

【关键字段识别】：
1. 投资者名称：通常是"长城证券"、"XX资管计划"等，不是"永诚保险"等发行方
2. 业务类型：查找"红利转投"、"分红转投"、"再投资"等关键词
3. 投资标的：产品名称和代码
4. 数值信息：完整识别所有数值，特别是带千分位分隔符的大数值

【特别注意】：
- 区分投资方和发行方，投资者名称应该是投资方
- 红利转投文档中通常有多笔交易记录
- 数值不要截断，保持完整性

请按表格结构输出，确保每行数据的完整性！"""

        ocr_bot = ChatBot(model='InternVL3-38B')
        img_list = process_file_to_base64(fn)
        enhanced_result = ocr_bot.chat_with_img(ocr_prompt, img_url=img_list, max_tokens=800)

        if enhanced_result and len(enhanced_result.strip()) > 50:
            print(f"✓ 增强OCR成功，内容长度: {len(enhanced_result)}")
            # 将增强结果附加到原始结果
            combined_result = markdown_content + "\n\n# 增强OCR补充信息：\n" + enhanced_result
            return combined_result
        else:
            print("✗ 增强OCR结果不理想，使用原始结果")
            return markdown_content

    except Exception as e:
        print(f"✗ 增强OCR处理失败: {str(e)}")
        return markdown_content

# 继承V1.3版本的后处理函数
def post_process_result(parsed_result):
    """V1.5版本：后处理解析结果，确保数值格式正确"""
    if isinstance(parsed_result, dict):
        return post_process_transaction(parsed_result)
    elif isinstance(parsed_result, list):
        return [post_process_transaction(transaction) for transaction in parsed_result]
    else:
        return parsed_result

def post_process_transaction(transaction):
    """V1.5版本：后处理单个交易记录"""
    if not isinstance(transaction, dict):
        return transaction

    # 需要格式化的数值字段
    numeric_fields = ['投资标的金额', '投资标的数量', '交易费用']

    processed_transaction = transaction.copy()

    for field in numeric_fields:
        if field in processed_transaction:
            # 特殊处理交易费用：分红等场景不提取费用
            if field == '交易费用':
                business_type = processed_transaction.get('业务类型', '')
                if business_type in ['分红', '红利转投']:
                    processed_transaction[field] = '/'
                    continue

            # 格式化数值
            processed_transaction[field] = format_numeric_value(processed_transaction[field])

    return processed_transaction

def post_process_json_data(json_data, original_markdown):
    """
    后处理JSON数据，修复数值字段提取问题 - V1.5版本优化
    """
    if not isinstance(json_data, list):
        # 如果是单个字典，先转换为列表处理
        if isinstance(json_data, dict):
            json_data = [json_data]
        else:
            return json_data

    # 业务类型标准化映射
    business_type_mapping = {
        # 分红相关
        '分红': '分红', '股息': '分红', '利息分配': '分红', '收益分配': '分红',
        '现金分红': '分红', '分红派息': '分红', '预计分红': '分红',

        # 红利转投相关 - V1.5版本增强
        '红利转投': '红利转投', '分红转投': '红利转投', '红利再投资': '红利转投',
        '分红再投资': '红利转投', '红利转份额': '红利转投', '分红转份额': '红利转投',
        '再投资': '红利转投', '转投': '红利转投', '份额结转': '红利转投',
        '红利结转': '红利转投', '分红结转': '红利转投', '收益再投': '红利转投',

        # 买入相关
        '买入': '买入', '购买': '买入', '投资': '买入', '成交买入': '买入',

        # 卖出相关
        '卖出': '卖出', '出售': '卖出', '变现': '卖出', '成交卖出': '卖出',
        '兑付': '卖出', '到期兑付': '卖出',

        # 认购相关
        '认购': '认购', '首次认购': '认购', '新发认购': '认购', '认购确认': '认购',

        # 申购相关
        '申购': '申购', '追加申购': '申购', '申购确认': '申购', '增购': '申购',

        # 赎回相关
        '赎回': '赎回', '赎回确认': '赎回', '提取': '赎回', '退出': '赎回',
        '部分赎回': '赎回', '全部赎回': '赎回'
    }

    processed_data = []

    for item in json_data:
        if not isinstance(item, dict):
            processed_data.append(item)
            continue

        processed_item = item.copy()

        # 1. 标准化业务类型
        if '业务类型' in processed_item:
            original_type = processed_item['业务类型']
            if original_type in business_type_mapping:
                processed_item['业务类型'] = business_type_mapping[original_type]
            elif original_type and original_type != '/':
                # 尝试模糊匹配
                for key, value in business_type_mapping.items():
                    if key in original_type or original_type in key:
                        processed_item['业务类型'] = value
                        break

        # 2. V1.5版本特殊处理：分红场景的字段识别
        business_type = processed_item.get('业务类型', '')
        if business_type == '分红':
            # 分红场景：优先提取"红利"作为标的金额（数值字段）
            if '投资标的金额' in processed_item and processed_item['投资标的金额'] == '/':
                red_profit = extract_field_from_markdown(original_markdown, ['红利', '分红金额', '现金分红'], is_numeric=True)
                if red_profit:
                    processed_item['投资标的金额'] = red_profit

            # 分红场景：优先提取"红股"作为标的数量（数值字段）
            if '投资标的数量' in processed_item and processed_item['投资标的数量'] == '/':
                red_stock = extract_field_from_markdown(original_markdown, ['红股', '分红股数'], is_numeric=True)
                if red_stock:
                    processed_item['投资标的数量'] = red_stock

        # 3. V1.5版本：投资者账号字段优化（非数值字段）
        if '投资者账号' in processed_item and processed_item['投资者账号'] == '/':
            account_value = extract_field_from_markdown(original_markdown, ['交易账号', '客户账号', '证件号码', '基金账号', '资金账户'], is_numeric=False)
            if account_value:
                processed_item['投资者账号'] = account_value

        # 4. 修复数值字段
        numeric_fields = ['投资标的金额', '投资标的数量', '交易费用']

        for field in numeric_fields:
            if field in processed_item and processed_item[field] == '/':
                # 尝试从markdown中重新提取数值
                extracted_value = extract_numeric_value_from_markdown(
                    original_markdown, field, processed_item
                )
                if extracted_value:
                    processed_item[field] = extracted_value

        # 5. 确保所有字段都存在
        required_fields = [
            '投资者名称', '投资者账号', '业务日期', '业务类型',
            '投资标的名称', '投资标的代码', '投资标的金额',
            '投资标的数量', '交易费用'
        ]

        for field in required_fields:
            if field not in processed_item:
                processed_item[field] = '/'

        processed_data.append(processed_item)

    # V1.5版本：应用后处理格式化
    processed_data = post_process_result(processed_data)

    return processed_data

# 继承V1.3版本的字段提取函数
def extract_field_from_markdown(markdown_content, keywords, is_numeric=True):
    """从markdown内容中根据关键词提取字段值，支持数值和非数值字段"""
    try:
        lines = markdown_content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否包含关键词
            for keyword in keywords:
                if keyword in line:
                    # 尝试提取冒号后的值
                    if ':' in line or '：' in line:
                        parts = re.split('[：:]', line)
                        if len(parts) >= 2:
                            value = parts[1].strip()

                            # 根据字段类型进行不同处理
                            if is_numeric:
                                # 数值字段：去除金融标识符并格式化
                                formatted_value = format_numeric_value(value)
                                if formatted_value != "/":
                                    return formatted_value
                            else:
                                # 非数值字段：直接返回清理后的值
                                # 去除多余的空格和特殊字符，但保留字母数字
                                clean_value = re.sub(r'\s+', '', value)  # 去除空格
                                clean_value = re.sub(r'[^\w\-]', '', clean_value)  # 只保留字母数字和连字符
                                if clean_value and len(clean_value) > 2:  # 确保不是太短的无意义字符
                                    return clean_value

                    # 对于数值字段，继续使用原有的数值提取逻辑
                    if is_numeric:
                        # 先去除金融标识符
                        clean_line = line
                        financial_symbols = ['￥', '$', '€', '£', '¥', 'RMB', 'USD', 'EUR', 'GBP', 'CNY']
                        for symbol in financial_symbols:
                            clean_line = clean_line.replace(symbol, ' ')

                        # V1.5版本：超级优化的数值提取模式，确保完整匹配千分位分隔符
                        patterns = [
                            # 千分位格式（优先级最高，最精确匹配）
                            r'-?\d{1,3}(?:,\d{3})+\.\d{1,6}',    # 千分位+小数：47,047,164.46
                            r'-?\d{1,3}(?:,\d{3})+',             # 千分位整数：47,047,164
                            # 普通格式（作为备选）
                            r'-?\d{4,}\.\d{1,6}',                # 长数字+小数：47047164.46
                            r'-?\d{4,}',                         # 长整数：47047164
                            r'-?\d{1,3}\.\d{1,6}',               # 短数字+小数：470.00
                            r'-?\d{1,3}',                        # 短整数：470
                        ]

                        # V1.5版本：优化匹配逻辑，优先选择最长最完整的数值
                        all_matches = []
                        for pattern in patterns:
                            matches = re.findall(pattern, clean_line)
                            if matches:
                                for match in matches:
                                    try:
                                        test_value = match.replace(',', '')
                                        float_value = float(test_value)  # 验证是否为有效数字
                                        # 记录匹配结果和其长度（用于优先级排序）
                                        all_matches.append((match, len(test_value), float_value))
                                    except ValueError:
                                        continue

                        # 如果找到匹配，选择最长的数值（最可能是完整的）
                        if all_matches:
                            # 按数值长度降序排序，优先选择最长的
                            all_matches.sort(key=lambda x: x[1], reverse=True)
                            best_match = all_matches[0][0]  # 取最长的匹配

                            # 额外验证：如果是千分位格式，确保格式正确
                            if ',' in best_match:
                                # 验证千分位格式的完整性
                                if validate_comma_separated_number(best_match):
                                    formatted_value = format_numeric_value(best_match)
                                    if formatted_value != "/":
                                        return formatted_value
                            else:
                                formatted_value = format_numeric_value(best_match)
                                if formatted_value != "/":
                                    return formatted_value
                    else:
                        # 对于非数值字段，尝试提取账号等信息
                        # 查找可能的账号模式（字母数字组合）
                        account_patterns = [
                            r'[A-Za-z0-9]{6,20}',  # 6-20位字母数字组合
                            r'\d{8,15}',           # 8-15位纯数字
                        ]

                        for pattern in account_patterns:
                            matches = re.findall(pattern, line)
                            if matches:
                                for match in matches:
                                    # 过滤掉明显不是账号的内容
                                    if not re.match(r'^[0-9]+$', match) or len(match) >= 8:  # 如果是纯数字，至少8位
                                        return match

        return None

    except Exception as e:
        print(f"提取字段时出错: {str(e)}")
        return None

def extract_numeric_value_from_markdown(markdown_content, field_name, item_context):
    """
    从markdown内容中提取数值，优化千分位分隔符处理
    """
    try:
        # 根据字段类型定义搜索关键词
        field_keywords = {
            '投资标的金额': ['金额', '确认金额', '交易金额', '成交金额', '申购金额', '赎回金额', '分红金额', '红利'],
            '投资标的数量': ['数量', '份额', '确认份额', '交易份额', '申购份额', '赎回份额', '红股'],
            '交易费用': ['费用', '手续费', '管理费', '申购费', '赎回费', '交易费用']
        }

        keywords = field_keywords.get(field_name, [])

        # 在markdown中搜索相关数值
        lines = markdown_content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否包含相关关键词
            has_keyword = any(keyword in line for keyword in keywords)

            if has_keyword:
                # 先去除金融标识符
                clean_line = line
                financial_symbols = ['￥', '$', '€', '£', '¥', 'RMB', 'USD', 'EUR', 'GBP', 'CNY']
                for symbol in financial_symbols:
                    clean_line = clean_line.replace(symbol, ' ')

                # 优化的数值提取模式，正确处理千分位分隔符
                patterns = [
                    r'-?\d{1,3}(?:,\d{3})+\.\d+',    # 千分位格式带小数：1,234.56 或 1,234,567.89
                    r'-?\d{1,3}(?:,\d{3})+',         # 千分位格式整数：1,234 或 1,234,567
                    r'-?\d+\.\d+',                   # 普通小数：1234.56
                    r'-?\d+',                        # 普通整数：1234
                ]

                for pattern in patterns:
                    matches = re.findall(pattern, clean_line)
                    if matches:
                        # 找到匹配的数值，使用format_numeric_value进行标准化处理
                        for match in matches:
                            # 验证这是一个有效的数值
                            try:
                                test_value = match.replace(',', '')
                                float(test_value)  # 验证是否为有效数字
                                # 使用format_numeric_value进行标准化
                                formatted_value = format_numeric_value(match)
                                if formatted_value != "/":
                                    return formatted_value
                            except ValueError:
                                continue

        return None

    except Exception as e:
        print(f"提取数值时出错: {str(e)}")
        return None

# V1.5版本新增：自定义markdown表格生成函数
def create_custom_markdown_table(data, headers):
    """创建自定义Markdown表格，确保数值精度不丢失，V1.5版本优化"""
    if not data or not headers:
        return ""

    # 创建表头
    header_row = "| " + " | ".join(str(h) for h in headers) + " |"
    separator_row = "| " + " | ".join(["---"] * len(headers)) + " |"

    # 创建数据行
    data_rows = []
    for row in data:
        row_values = []
        for value in row:
            if value is None:
                row_values.append("")
            else:
                # 确保完全按照原始字符串输出，保持引号原样
                original_str = clean_quotes(str(value).strip())
                row_values.append(original_str)
        data_rows.append("| " + " | ".join(row_values) + " |")

    # 组合表格
    table_lines = [header_row, separator_row] + data_rows
    return "\n".join(table_lines) + "\n\n"

def run(fn):
    """V1.5版本主运行函数，PNG识别大幅优化"""
    usage_model = 'InternVL3-38B'

    print(f"=== V1.5版本处理开始 ===")
    print(f"文件: {fn}")

    # 判断文件类型
    file_ext = os.path.splitext(fn)[1].lower()
    print(f"文件类型: {file_ext}")

    # 对Excel文件特殊处理 - 继承V1.3版本优化
    if file_ext in ['.xlsx', '.xls']:
        try:
            # 根据文件扩展名选择合适的引擎
            if file_ext == '.xls':
                # .xls文件使用xlrd引擎
                df = pd.read_excel(fn, dtype=str, engine='xlrd')
            else:
                # .xlsx文件使用openpyxl引擎
                df = pd.read_excel(fn, dtype=str, engine='openpyxl')

            # 处理NaN值，但保持其他值的原始格式
            df = df.fillna('')  # 将NaN替换为空字符串

            # V1.5版本：使用优化的markdown生成
            markdown_content = "# Excel文件内容\n\n**V1.5版本重要提示：以下表格中的所有数值必须严格按照显示的格式进行提取，包括所有小数位数和负号，不得进行任何精度优化！**\n\n"

            # 使用自定义表格生成函数，避免pandas的数值格式化
            headers = df.columns.tolist()
            data = df.values.tolist()

            markdown_content += create_custom_markdown_table(data, headers)

            # V1.5版本：添加特殊提示
            markdown_content += "\n\n**【V1.5数据精度检查】**：请严格按照上表中显示的数值进行提取，特别注意小数位数！去除金融标识符（如￥、$等），保持中英文引号原样！千分位分隔符（逗号）不是小数点！\n"

        except Exception as e:
            print(f"Excel文件读取错误: {str(e)}")
            raise Exception(f"Excel文件读取错误: {str(e)}")

    # 对Word文件特殊处理 - 继承V1.3版本优化
    elif file_ext in ['.docx', '.doc']:
        try:
            import docx2txt
            # 提取文本内容
            text = docx2txt.process(fn)
            # V1.5版本：保持引号原样
            text = clean_quotes(text)
            markdown_content = f"# Word文档内容\n\n{text}"
        except ImportError:
            print("缺少docx2txt库，请安装: pip install docx2txt")
            raise Exception("缺少docx2txt库，请安装: pip install docx2txt")
        except Exception as e:
            print(f"Word文件读取错误: {str(e)}")
            raise Exception(f"Word文件读取错误: {str(e)}")

    # PDF和图片文件处理 - V1.5版本PNG超级优化
    else:
        if fn.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model=usage_model)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            # V1.5版本：PNG文件超级优化处理
            print(f"处理图片文件: {file_ext}")

            # 对于PNG文件，使用超级优化的处理函数
            if file_ext == '.png':
                print("启用PNG超级优化模式...")
                markdown_content, seal_img_list = process_png_file_super_optimized(fn, usage_model)

                # V1.5版本：增强OCR处理
                print("启用增强OCR处理...")
                markdown_content = enhanced_single_model_ocr(fn, markdown_content)
            else:
                # 其他图片格式使用原有参数
                markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
        else:
            # 其他格式的图片文件
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)

    # 如果是Excel或Word文件，跳过VLM OCR处理
    if file_ext not in ['.xlsx', '.xls', '.docx', '.doc']:
        # V1.5版本：PNG文件OCR优化
        print("开始补充OCR处理...")

        # V1.5版本：针对红利转投文档的超级优化OCR提示词
        if file_ext == '.png':
            ocr_prompt = """你是一名专业的OCR专家，特别擅长处理金融文档。请仔细分析这个PNG图片，这很可能是一个红利转投确认单。

【超级重要】请特别注意以下内容：

1. **投资者信息识别**：
   - 查找"长城证券"开头的投资者名称（如"长城证券乐活1号集合资产管理计划"）
   - 区分投资方和发行方，投资者是"长城证券XX计划"，不是"永诚保险"
   - 提取对应的账号信息

2. **业务类型识别**：
   - 查找"红利转投"、"分红转投"、"再投资"、"转投"、"份额结转"等关键词
   - 这是红利转投业务，不是普通分红

3. **投资标的信息**：
   - 产品名称通常是"永诚资产XX产品"
   - 产品代码通常是6位数字（如110001）

4. **数值信息**：
   - 完整识别所有金额和份额数据
   - 特别注意千分位分隔符（如1,213.03）
   - 红利转投中，金额和份额通常相等

5. **表格结构**：
   - 通常有多行交易记录
   - 每行包含：投资者名称、账号、金额、份额等信息
   - 按行逐一识别，不要遗漏

请按表格结构详细输出所有识别到的内容，特别是多行交易数据！"""
            max_tokens = 800  # 增加token数量以获得更详细的识别结果
        else:
            ocr_prompt = "你是一名OCR专家。请从用户提供的图片中提取有关印章信息、落款日期的全部有效信息"
            max_tokens = 100

        # VL2OCR
        ocr_bot = ChatBot(model='InternVL3-38B')
        img_list = process_file_to_base64(fn)
        vlm_info = ocr_bot.chat_with_img(ocr_prompt, img_url=img_list, max_tokens=max_tokens)
        markdown_content += f"\n# 补充信息：\n{vlm_info}"
        print("补充OCR处理完成")

    # 继承V1.3版本的系统提示词（保持完全一致）
    sys_prompt = """你是一位资深的银行托管部经理，负责从客户提供的非标交易确认单中**抽取交易要素**，并按以下要求结构化输出 JSON 数组。

**【V1.5版本关键提醒】**：在处理数值时，你必须像复制粘贴一样，完全按照原文档中显示的字符进行提取，不得进行任何数值处理！
**【千分位分隔符特别注意】**：当遇到包含逗号的数值时，必须正确识别千分位分隔符！例如：
- 2,123.57 → 应识别为 2123.57（不是 2.12）
- 1,234,567.89 → 应识别为 1234567.89（不是 1.23）
- 逗号在数值中作为千分位分隔符，不是小数点！

=====================【V1.5版本字段定义】=====================
1. 【投资者名称】：通常指代客户姓名，一般是资管计划的名称，在文档中可对应'投资人名称'、'客户名称'、'计划名称'等字段。**超级重要**：投资者是投资方（如"长城证券乐活1号集合资产管理计划"），绝对不是发行方（如"永诚保险资产管理有限公司"）！在红利转投文档中，投资者名称通常以"长城证券"开头。
2. 【投资者账号】通常指客户的资金账户，在文档中可对应'交易账号'、'客户账号'、'证件号码'、'基金账号'、'理财账户号'等字段，优先级顺序为：交易账号 > 资金账户 > 客户账号 > 证件号码 > 基金账号 > 理财账户号。投资者账号对应的值（value）应根据该优先级选取。不要取'合同编号'为本字段值。
3. 【业务日期】：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 【业务类型】：需要提取文件中代表当前类型的文字，并映射到下面的选项中：分红、红利转投、买入、卖出、认购、申购、赎回。**红利转投文档特别注意**：如果文档标题包含"分红确认单"但内容涉及"再投资"、"转投"、"份额结转"等，业务类型应该是"红利转投"而不是"分红"！
5. 【投资标的名称】：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 【投资标的代码】：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 【投资标的金额】：实际交易的确认金额（缺失填"/"）
8. 【投资标的数量】：文档中可能用份额来描述（缺失填"/"）
9. 【交易费用】：一般申购、赎回、买入、卖出交易中，会标明交易费用（缺失填"/"）

=====================【V1.5版本特殊处理规则】=====================

**【V1.5版本超级重要】金额识别规则：**
- 识别金额时，自动去除金融标识符（如￥、$、€、£等），只保留数字部分
- 例如：￥5010317.83 → 5010317.83
- **【关键】千分位分隔符处理**：必须正确识别逗号作为千分位分隔符，不能截断数值！
- 例如：￥2,123.57 → 2123.57（不是2.12）
- 例如：1,234,567.89 → 1234567.89（不是1.23）
- **【超级重要】完整数值识别**：47,047,164.46 → 47047164.46（绝对不能识别为470.00！）
- **【防止截断】**：如果看到千分位格式的数值，必须提取完整的数值，不能只提取前几位！

**数值格式要求：**
- 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
- 例如：1000000 → "1000000.00"，1000000.5 → "1000000.50"
- 如果字段不存在或为空，填写"/"

**引号处理：**
- 中文引号""保持原样，不要转换为英文引号
- 英文引号""保持原样，不要转换为中文引号

**特殊场景处理（分红）：**
- 【投资标的金额】：若文档中"红利"字段存在且非空，优先提取"红利"作为标的金额
- 【投资标的数量】：若文档中"红股"字段存在且非空，提取"红股"作为标的数量
- 【交易费用】：仅在申购、赎回、买入、卖出等交易类型中提取费用字段，分红等场景填写"/"

=====================【V1.5版本业务类型识别规则】=====================
**重要：必须严格按照以下规则识别业务类型，并映射到标准术语**

**标准业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回

**识别关键词映射表**：
• **分红** ← 关键词：分红、股息、利息分配、收益分配、现金分红、分红派息
• **红利转投** ← 关键词：红利转投、分红转投、红利再投资、分红再投资、红利转份额、分红转份额、再投资、转投、份额结转、红利结转、分红结转、收益再投
• **买入** ← 关键词：买入、购买、投资、成交买入
• **卖出** ← 关键词：卖出、出售、变现、成交卖出、兑付、到期兑付
• **认购** ← 关键词：认购、首次认购、新发认购、认购确认
• **申购** ← 关键词：申购、追加申购、申购确认、增购
• **赎回** ← 关键词：赎回、赎回确认、提取、退出、部分赎回、全部赎回

**V1.5版本识别步骤**：
1. 首先在文档中寻找明确的业务类型标识（如"业务类型"、"交易类型"、"操作类型"等字段）
2. 如果没有明确字段，则根据文档标题、内容描述中的关键词进行识别
3. 将识别到的原始词汇映射到上述7个标准业务类型之一
4. 如果无法确定，优先根据文档的主要内容和金额流向判断
5. **特别注意**：分红场景要优先查找"红利"和"红股"字段

=====================【数值提取增强规则】=====================
**针对投资标的金额、投资标的数量、交易费用字段的特殊处理**：

1. **多重查找策略**：
   - 优先查找明确标注的字段（如"确认金额"、"交易金额"、"份额"、"数量"等）
   - 查找表格中的数值列
   - 查找文本中的货币符号后的数值（¥、￥、RMB等）
   - 查找带有千分位分隔符的数值（如1,234,567.89）

2. **数值识别模式**：
   - 正数：123456.78、1,234,567.89、￥123,456.78
   - 负数：-123456.78、-1,234,567.89、-￥123,456.78
   - 零值：0、0.00、0.000等
   - **千分位分隔符处理**：2,123.57 应识别为 2123.57，不是 2.12

3. **容错处理**：
   - 如果某个数值字段在文档中确实存在但难以精确定位，不要填写"/"
   - 仔细查看表格的每一行每一列
   - 注意数值可能出现在不同的位置（表头、表尾、备注等）

4. **精度保持**：
   - 完全按照原文档显示的格式输出，包括所有小数位
   - 保留千分位分隔符（如果原文档有的话）
   - 保留负号和货币符号前缀

=====================【V1.5版本格式要求】=====================
• 日期全部转为 `YYYY-MM-DD` 格式
• **V1.5版本数值格式要求**：投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
• 去除金融标识符（￥、$等），只保留数字部分
• 保持中英文引号原样，不进行转换
• 业务类型必须是以下7个之一：分红、红利转投、买入、卖出、认购、申购、赎回
• 输出 **JSON 数组**，字段顺序固定，V1.5版本示例如下：

```json
[
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-15",
    "业务类型": "申购",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "100000.00",
    "投资标的数量": "10000.00",
    "交易费用": "150.00"
  },
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-16",
    "业务类型": "分红",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "5000.00",
    "投资标的数量": "/",
    "交易费用": "/"
  }
]
```

**【V1.5版本严格精度要求 - 必须遵守】**
1. 如果文档中确实没有相关信息，对应字段填写"/"
2. **V1.5版本数值格式要求（绝对不可违反）**：
   - 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
   - 例如：原文档显示"329961736.14"，输出"329961736.14"
   - 例如：原文档显示"100"，输出"100.00"
   - 例如：原文档显示"￥5010317.83"，输出"5010317.83"
   - **千分位分隔符处理**：原文档显示"2,123.57"，输出"2123.57"（不是"2.12"）
   - **千分位分隔符处理**：原文档显示"1,234,567.89"，输出"1234567.89"（不是"1.23"）
   - 去除金融标识符和千分位分隔符，但保留数值精度
3. **V1.5版本引号处理（绝对不可违反）**：
   - 中文引号""保持原样，不要转换为英文引号
   - 英文引号""保持原样，不要转换为中文引号
4. **V1.5版本特殊场景处理**：
   - 分红场景：优先查找"红利"字段作为投资标的金额
   - 分红场景：优先查找"红股"字段作为投资标的数量
   -分红场景：投资标的代码不要取"理财账户号"
   - 分红场景：交易费用字段填写"/"
   - 红利转投场景：优先查找"再投资红利金额"、"转投金额"、"结转金额"字段作为投资标的金额
   - 红利转投场景：优先查找"再投资份额"、"结转份额"、"转投份额"字段作为投资标的数量
   - 红利转投场景：投资者名称应该是投资方（如"长城证券XX计划"），绝对不是发行方（如"永诚保险"）
   - 红利转投场景：业务类型必须识别为"红利转投"，关键词包括：再投资、转投、份额结转等
   - 红利转投场景：通常有多笔交易，每笔都要单独提取
   - 红利转投场景：投资标的代码通常是6位数字（如110001），不要与账号混淆
   - 投资者账号：优先查找"交易账号"或"客户账号"字段


**【V1.5版本特别注意】**：
- 仔细检查文档中的每个表格、每行数据，特别是多行交易记录
- 数值字段绝对不能轻易填写"/"，除非确实在文档中找不到任何相关数值
- 业务类型必须严格按照映射规则转换为标准术语
- 所有数值必须格式化为两位小数的字符串格式
- **红利转投文档特别注意**：
  * 通常包含多笔交易记录，每笔都要单独提取
  * 投资者名称是投资方（如"长城证券XX计划"），不是发行方（如"永诚保险"）
  * 业务类型必须识别为"红利转投"
  * 投资标的金额和数量在红利转投中通常相等
  * 仔细区分投资标的代码和投资者账号，不要混淆
- **PNG图片特别关注**：由于PNG图片识别已经过多重优化，请特别仔细提取其中的数值信息
- **红利转投文档识别要点**：
  * 如果文档标题是"分红确认单"但内容涉及再投资，这是红利转投业务
  * 投资者名称格式："长城证券XX集合资产管理计划"
  * 投资标的名称格式："永诚资产XX产品"
  * 投资标的代码：通常是6位数字（如110001）
  * 金额和份额：在红利转投中通常相等
  * 多笔交易：每行数据都要单独提取
"""

    chat_bot = ChatBot(system_prompt=sys_prompt, model='InternVL3-38B')

    res = chat_bot.chat(markdown_content, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(res)

    # V1.5版本：后处理优化
    json_data = post_process_json_data(json_data, markdown_content)

    # V1.5版本：最终格式验证
    print("=== V1.5版本数值格式验证 ===")
    if isinstance(json_data, list):
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                print(f"交易 {i+1}:")
                for field in ['投资标的金额', '投资标的数量', '交易费用']:
                    if field in item:
                        value = item[field]
                        print(f"  {field}: {value} (类型: {type(value).__name__})")
                        if value == "/":
                            print(f"    ✓ 空值格式正确")
                        elif isinstance(value, str) and re.match(r'^-?\d+\.\d{2}$', value):
                            print(f"    ✓ 两位小数格式正确")
                        else:
                            print(f"    ⚠ 格式可能不符合V1.5要求")

    print("=== V1.5版本处理完成 ===")
    return json_data

def get_full_fn_list():
    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)

    # 只读取POC脱敏材料下的文件
    poc_path = os.path.join(project_root, "大模型样例", "POC脱敏材料")

    # V1.5版本：完整支持.png格式和其他图片格式
    file_types = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp', '.xlsx', '.xls', '.docx']

    full_fn_list = []

    print(f"=== V1.5版本开始扫描文件 ===")
    print(f"项目根目录: {project_root}")
    print(f"POC脱敏材料路径: {poc_path}")
    print(f"支持的文件类型: {file_types}")
    print()

    # 检查并添加POC脱敏材料下的相关文件
    print("=== 扫描POC脱敏材料文件夹 ===")
    poc_subdirs = ['非标红利转投（脱敏）', '非标卖出（脱敏）', '非标分红（脱敏）',
                   '非标买入（脱敏）', '赎回确认（脱敏）', '认购确认（脱敏）']

    total_files = 0
    png_files = 0
    for subdir in poc_subdirs:
        subdir_path = os.path.join(poc_path, subdir)
        print(f"检查文件夹: {subdir_path}")
        if os.path.exists(subdir_path):
            print(f"  文件夹存在，开始读取文件...")
            file_count = 0
            for fn in os.listdir(subdir_path):
                if os.path.splitext(fn)[1].lower() in file_types:
                    full_fn_list.append(os.path.join(subdir_path, fn))
                    file_count += 1
                    if fn.lower().endswith('.png'):
                        png_files += 1
                        print(f"    ✓ 添加PNG文件: {fn} (将使用超级优化模式)")
                    else:
                        print(f"    ✓ 添加文件: {fn}")
            print(f"  文件夹 {subdir} 共添加 {file_count} 个文件")
            total_files += file_count
        else:
            print(f"  文件夹不存在: {subdir_path}")

    print()
    print(f"=== V1.5版本扫描完成 ===")
    print(f"总共找到 {len(full_fn_list)} 个文件 (应为 {total_files} 个)")
    print(f"其中PNG文件 {png_files} 个，将使用超级优化处理")
    print()

    return full_fn_list

def test_al(cache_answer=False):
    """
    V1.5版本：测试所有文件，并保存答案
    """
    # 构建分支号
    var = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    full_fn_list = get_full_fn_list()

    if not full_fn_list:
        print("未找到任何可处理的文件！")
        return

    print(f"V1.5版本找到 {len(full_fn_list)} 个文件待处理")

    # 文件md5与原始文件名（非完整路径）的映射
    fn_md5_dict = {}
    deal_fn_list = []

    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)

    check_dir = os.path.join(project_root, "check", DIR_NAME, f"V1.5_{var}")
    os.makedirs(check_dir, exist_ok=True)

    # 确保answer目录存在
    answer_dir = os.path.join(project_root, "answer", DIR_NAME)
    os.makedirs(answer_dir, exist_ok=True)

    if not cache_answer:
        # 将run函数的内容写入check_dir
        with open(os.path.join(check_dir, "run_v1.5.py"), "w", encoding='utf-8') as f:
            try:
                # 优先尝试获取函数源代码
                source = inspect.getsource(run)
                f.write("# V1.5版本run函数源代码\n")
                f.write(source)
            except (TypeError, OSError):
                # 备选方案：创建带注释的存根文件
                f.write("# V1.5版本run函数未找到有效源代码\n")
                f.write("def run():\n    # 函数实现未捕获\n    pass\n")

    png_count = 0
    for fn in tqdm.tqdm(full_fn_list):
        try:
            file_md5 = cal_fn_md5(fn)
            answer_fn = os.path.join(project_root, "answer", DIR_NAME, f"{file_md5}_v1.5.json")
            check_fn = os.path.join(check_dir, f"{file_md5}.json")

            # 统计PNG文件
            if fn.lower().endswith('.png'):
                png_count += 1
                print(f"\n=== 处理PNG文件 {png_count} ===")
                print(f"文件: {os.path.basename(fn)}")

            if cache_answer:
                if os.path.exists(answer_fn):
                    continue
                else:
                    save_fn = answer_fn
            else:
                save_fn = check_fn

            fn_md5_dict[file_md5] = fn

            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    json_data = run(fn)
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"处理文件 {fn} 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        print(f"处理文件 {fn} 失败，已达到最大重试次数: {str(e)}")
                        # 创建错误响应
                        json_data = {
                            "error": f"处理失败: {str(e)}",
                            "file": fn,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "version": "V1.5"
                        }

            deal_fn_list.append(fn)

            with open(save_fn, "w", encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=4)

        except Exception as e:
            print(f"处理文件 {fn} 时发生严重错误: {str(e)}")
            # 继续处理下一个文件，不中断整个流程
            continue

    # 根据cache_answer参数决定fn_md5_dict.json的保存位置
    if cache_answer:
        # 选项1：保存到answer目录
        dict_save_dir = os.path.join(project_root, "answer", DIR_NAME)
        os.makedirs(dict_save_dir, exist_ok=True)
    else:
        # 选项2：保存到check目录（带时间戳）
        dict_save_dir = check_dir

    with open(os.path.join(dict_save_dir, "fn_md5_dict_v1.5.json"), "w", encoding='utf-8') as f:
        json.dump(fn_md5_dict, f, ensure_ascii=False, indent=4)

    if len(deal_fn_list) > 0:
        print(f"V1.5版本所有答案预存完成，共处理 {len(deal_fn_list)} 个文件！")
        print(f"其中PNG文件 {png_count} 个，已使用超级优化处理")
        print(f"结果保存在: {check_dir}")
    else:
        print("所有答案都已缓存，无新生成的答案！")

def test_format_numeric_value_v15():
    """V1.5版本：测试数值格式化函数，验证千分位分隔符处理"""
    print("=== V1.5版本数值格式化测试 ===")

    test_cases = [
        ("2,123.57", "2123.57"),
        ("1,234,567.89", "1234567.89"),
        ("￥2,123.57", "2123.57"),
        ("$1,234,567.89", "1234567.89"),
        ("100", "100.00"),
        ("100.5", "100.50"),
        ("0", "0.00"),
        ("-2,123.57", "-2123.57"),
        ("", "/"),
        (None, "/"),
        ("329961736.14", "329961736.14"),  # 关键测试用例
        ("12472553.63", "12472553.63"),   # 关键测试用例
    ]

    for input_val, expected in test_cases:
        result = format_numeric_value(input_val)
        status = "✓" if result == expected else "✗"
        print(f"{status} 输入: '{input_val}' → 输出: '{result}' (期望: '{expected}')")
        if result != expected:
            print(f"   错误：期望 '{expected}'，但得到 '{result}'")

    print("=== V1.5版本测试完成 ===\n")

def test_png_processing():
    """V1.5版本：测试PNG处理功能"""
    print("=== V1.5版本PNG处理测试 ===")

    # 检查图片处理库
    print(f"PIL库可用: {PIL_AVAILABLE}")
    print(f"OpenCV库可用: {CV2_AVAILABLE}")

    if not PIL_AVAILABLE and not CV2_AVAILABLE:
        print("⚠ 建议安装图片处理库以获得最佳PNG识别效果:")
        print("  pip install Pillow opencv-python")

    # 查找PNG测试文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    poc_path = os.path.join(project_root, "大模型样例", "POC脱敏材料")

    png_files = []
    for root, dirs, files in os.walk(poc_path):
        for file in files:
            if file.lower().endswith('.png'):
                png_files.append(os.path.join(root, file))

    print(f"找到 {len(png_files)} 个PNG文件")

    if png_files:
        test_file = png_files[0]
        print(f"测试文件: {os.path.basename(test_file)}")

        # 测试质量评估
        dummy_content = "投资者名称: 测试账号: 123456 金额: 1,234.56 数量: 100.00"
        score = evaluate_ocr_quality(dummy_content)
        print(f"质量评估测试: {score}")
    else:
        print("未找到PNG文件进行测试")

    print("=== PNG处理测试完成 ===\n")

if __name__ == "__main__":
    print("=== 非标交易确认单解析 V1.5版本 ===")
    print("主要特性：PNG识别准确率大幅提升")
    print()

    select_but = input("请选择操作：\n1. 生成标准答案\n2. 生成测试答案\n3. 测试数值格式化\n4. 测试PNG处理功能\n")
    if select_but == "1":
        test_al(cache_answer=True)
    elif select_but == "2":
        test_al(cache_answer=False)
    elif select_but == "3":
        test_format_numeric_value_v15()
    elif select_but == "4":
        test_png_processing()
    else:
        print("无效选择！")

# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 数据库初始化脚本
版本: 3.0
更新日期: 2025-01-06
"""
import os
import sys
from datetime import datetime, timedelta, date
import random
import json
from decimal import Decimal
from flask import Flask
from sqlalchemy.exc import IntegrityError
from sqlalchemy import inspect

# 导入所有模型
from models import (
    db, User, AnalysisRecord, PromptConfig, PromptVersion, MockData,
    CustomerSystemData, FileTag, ReviewRecord, StandardAnswer, ModelConfig,
    DashboardStats, UserActivity, GlobalSetting
)

# 导入配置
from config import Config

def create_app():
    """创建应用实例"""
    app = Flask(__name__)

    # 使用配置类
    app.config.from_object(Config)

    # 初始化扩展
    db.init_app(app)

    # 初始化应用配置（创建目录等）
    Config.init_app(app)

    return app

def verify_database_structure():
    """验证数据库结构"""
    print("=" * 60)
    print("验证数据库结构...")
    
    inspector = inspect(db.engine)
    tables = inspector.get_table_names()
    
    # 期望的表列表
    expected_tables = [
        'users', 'analysis_records', 'prompt_config', 'prompt_versions',
        'mock_data', 'customer_system_data', 'file_tags', 'review_records',
        'standard_answers', 'model_configs', 'dashboard_stats', 
        'user_activities', 'global_settings'
    ]
    
    print(f"数据库中的表 ({len(tables)}个):")
    for table in sorted(tables):
        status = "✓" if table in expected_tables else "?"
        print(f"  {status} {table}")
        
        # 显示表的列信息
        columns = inspector.get_columns(table)
        print(f"    列数: {len(columns)}")
        
        # 显示前几个重要列
        for i, col in enumerate(columns[:3]):
            print(f"      {col['name']} ({col['type']})")
        if len(columns) > 3:
            print(f"      ... 还有 {len(columns) - 3} 列")
        print()
    
    # 检查缺失的表
    missing_tables = set(expected_tables) - set(tables)
    if missing_tables:
        print(f"⚠️  缺失的表: {', '.join(missing_tables)}")
    else:
        print("✅ 所有期望的表都已创建")
    
    print("=" * 60)

def create_default_users():
    """创建默认用户"""
    print("创建默认用户...")
    
    users_data = [
        {
            'username': 'admin',
            'password': 'admin123',
            'role': 'admin',
            'email': '<EMAIL>',
            'phone': '***********'
        },
        {
            'username': 'analyst',
            'password': 'analyst123',
            'role': 'analyst',
            'email': '<EMAIL>',
            'phone': '13800138001'
        },
        {
            'username': 'user',
            'password': 'user123',
            'role': 'user',
            'email': '<EMAIL>',
            'phone': '13800138002'
        }
    ]
    
    created_count = 0
    for user_data in users_data:
        try:
            existing = User.query.filter_by(username=user_data['username']).first()
            if existing:
                print(f"  用户已存在: {user_data['username']}")
                continue
                
            user = User(
                username=user_data['username'],
                role=user_data['role'],
                email=user_data['email'],
                phone=user_data['phone'],
                status='active'
            )
            user.set_password(user_data['password'])
            
            db.session.add(user)
            db.session.commit()
            
            print(f"  ✓ 创建用户: {user_data['username']} ({user_data['role']})")
            created_count += 1
            
        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 用户创建失败: {user_data['username']} - {str(e)}")
    
    print(f"用户创建完成，新增 {created_count} 个用户")

def create_default_prompts():
    """创建默认提示词配置"""
    print("创建默认提示词配置...")

    # 从硬编码提示词文件导入
    try:
        from utils.hardcoded_prompts import get_default_prompts
        default_prompts_data = get_default_prompts()
        print(f"  从硬编码文件导入 {len(default_prompts_data)} 个提示词模板")
    except ImportError:
        print("  警告: 无法导入硬编码提示词，使用配置文件中的分析类型创建默认提示词")
        # 使用配置文件中的分析类型
        default_prompts_data = {}
        for analysis_type, type_name in Config.ANALYSIS_TYPES.items():
            default_prompts_data[analysis_type] = {
                'name': type_name,
                'prompt': f'你是一名{type_name}解析专家，请仔细分析上传的文档，提取关键信息并按照指定格式返回结果。\n\n请注意：\n1. 仔细识别文档中的所有关键字段\n2. 确保提取的信息准确无误\n3. 按照JSON格式返回结构化数据\n4. 如果某些字段无法识别，请标注为空值或"未识别"'
            }

    created_count = 0
    for analysis_type, prompt_data in default_prompts_data.items():
        try:
            # 检查是否已存在
            existing = PromptConfig.query.filter_by(
                analysis_type=analysis_type,
                prompt_key='base_prompt'
            ).first()
            
            if existing:
                print(f"  提示词已存在: {analysis_type}")
                continue
            
            # 处理不同的数据格式
            if isinstance(prompt_data, dict):
                prompt_content = prompt_data.get('prompt', '')
                description = prompt_data.get('name', analysis_type)
            elif isinstance(prompt_data, str):
                prompt_content = prompt_data
                description = analysis_type
            else:
                print(f"  ✗ 跳过无效的提示词数据: {analysis_type}")
                continue
            
            # 创建提示词配置
            prompt_config = PromptConfig(
                analysis_type=analysis_type,
                prompt_key='base_prompt',
                prompt_content=prompt_content,
                description=description,
                version='v1.0',
                is_active=True,
                is_default=True
            )
            
            db.session.add(prompt_config)
            db.session.commit()
            
            print(f"  ✓ 创建提示词: {analysis_type}")
            created_count += 1
            
        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 提示词创建失败: {analysis_type} - {str(e)}")
        except Exception as e:
            db.session.rollback()
            print(f"  ✗ 提示词创建异常: {analysis_type} - {str(e)}")
    
    print(f"提示词创建完成，新增 {created_count} 个配置")

def create_default_model_configs():
    """创建默认模型配置"""
    print("创建默认模型配置...")

    # 使用配置文件中的默认模型配置
    default_config = Config.DEFAULT_MODEL_CONFIG

    models_data = [
        {
            'model_id': default_config['model_id'],
            'model_name': default_config['model_name'],
            'api_url': default_config['api_url'],
            'api_key': default_config['api_key'],
            'vision_model': default_config.get('vision_model', ''),
            'timeout': default_config['timeout'],
            'max_tokens': default_config['max_tokens'],
            'temperature': Decimal(str(default_config['temperature'])),
            'is_active': True
        },
        {
            'model_id': 'gpt-4',
            'model_name': 'GPT-4',
            'api_url': 'https://api.openai.com/v1',
            'api_key': 'sk-your-openai-api-key-here',
            'vision_model': 'gpt-4-vision-preview',
            'timeout': 120,
            'max_tokens': 8192,
            'temperature': Decimal('0.70'),
            'is_active': False
        },
        {
            'model_id': 'claude-3-sonnet',
            'model_name': 'Claude 3 Sonnet',
            'api_url': 'https://api.anthropic.com/v1',
            'api_key': 'sk-ant-your-anthropic-api-key-here',
            'timeout': 90,
            'max_tokens': 4096,
            'temperature': Decimal('0.70'),
            'is_active': False
        },
        {
            'model_id': 'internvl3-38b',
            'model_name': 'InternVL3-38B',
            'api_url': 'http://192.168.10.236:23000/v1',
            'api_key': 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553',
            'vision_model': 'InternVL3-38B',
            'timeout': 60,
            'max_tokens': 8192,
            'temperature': Decimal('0.80'),
            'is_active': False
        }
    ]
    
    created_count = 0
    for model_data in models_data:
        try:
            existing = ModelConfig.query.filter_by(model_id=model_data['model_id']).first()
            if existing:
                print(f"  模型配置已存在: {model_data['model_id']}")
                continue
                
            model = ModelConfig(**model_data)
            db.session.add(model)
            db.session.commit()
            
            print(f"  ✓ 创建模型配置: {model_data['model_id']}")
            created_count += 1
            
        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 模型配置创建失败: {model_data['model_id']} - {str(e)}")
    
    print(f"模型配置创建完成，新增 {created_count} 个配置")

def create_global_settings():
    """创建全局设置"""
    print("创建全局设置...")

    # 使用配置文件中的值
    max_file_size_mb = Config.MAX_CONTENT_LENGTH // (1024 * 1024)
    allowed_extensions = ','.join(Config.ALLOWED_EXTENSIONS)

    settings_data = [
        {
            'key': 'system_name',
            'value': Config.SYSTEM_NAME,
            'description': '系统名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'system_version',
            'value': Config.SYSTEM_VERSION,
            'description': '系统版本',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'company_name',
            'value': Config.COMPANY_NAME,
            'description': '公司名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'auto_analysis',
            'value': 'true',
            'description': '上传后自动分析',
            'data_type': 'boolean',
            'is_public': True
        },
        {
            'key': 'max_file_size',
            'value': str(max_file_size_mb),
            'description': '最大文件大小(MB)',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'allowed_file_types',
            'value': allowed_extensions,
            'description': '允许的文件类型',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'records_per_page',
            'value': str(Config.RECORDS_PER_PAGE),
            'description': '每页记录数',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'mock_mode_enabled',
            'value': str(Config.MOCK_MODE_ENABLED).lower(),
            'description': '启用挡板模式',
            'data_type': 'boolean',
            'is_public': False
        },
        {
            'key': 'session_timeout',
            'value': '3600',
            'description': '会话超时时间(秒)',
            'data_type': 'integer',
            'is_public': False
        },
        {
            'key': 'enable_audit_log',
            'value': 'true',
            'description': '启用审计日志',
            'data_type': 'boolean',
            'is_public': False
        },
        {
            'key': 'analysis_types',
            'value': json.dumps(Config.ANALYSIS_TYPES, ensure_ascii=False),
            'description': '分析类型配置',
            'data_type': 'json',
            'is_public': True
        }
    ]
    
    created_count = 0
    for setting_data in settings_data:
        try:
            existing = GlobalSetting.query.filter_by(key=setting_data['key']).first()
            if existing:
                print(f"  设置已存在: {setting_data['key']}")
                continue
                
            setting = GlobalSetting(**setting_data)
            db.session.add(setting)
            db.session.commit()
            
            print(f"  ✓ 创建设置: {setting_data['key']}")
            created_count += 1
            
        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 设置创建失败: {setting_data['key']} - {str(e)}")
    
    print(f"全局设置创建完成，新增 {created_count} 个设置")

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")

    # 获取管理员用户ID
    admin_user = User.query.filter_by(role='admin').first()
    analyst_user = User.query.filter_by(role='analyst').first()

    if not admin_user:
        print("  警告: 未找到管理员用户，跳过示例数据创建")
        return

    # 使用配置文件中的分析类型
    analysis_types = list(Config.ANALYSIS_TYPES.keys())

    # 记录数量配置 - 根据分析类型动态分配
    base_counts = [50, 40, 30, 25, 20, 15]  # 递减的基础数量
    record_counts = {}
    for i, analysis_type in enumerate(analysis_types):
        record_counts[analysis_type] = base_counts[i] if i < len(base_counts) else 10
    
    total_created = 0
    for analysis_type in analysis_types:
        count = record_counts.get(analysis_type, 10)
        print(f"  创建 {analysis_type} 示例记录: {count} 条")
        
        for i in range(count):
            # 随机创建时间（过去30天内）
            created_at = datetime.now() - timedelta(days=random.randint(0, 30))
            
            # 随机状态分布
            status_choices = ['pending', 'processing', 'completed', 'failed']
            status_weights = [0.1, 0.15, 0.7, 0.05]  # 70%完成，5%失败
            status = random.choices(status_choices, weights=status_weights)[0]
            
            # 随机复核状态
            review_status_choices = ['pending', 'approved', 'rejected', 'needs_revision']
            review_status_weights = [0.4, 0.45, 0.1, 0.05]
            review_status = random.choices(review_status_choices, weights=review_status_weights)[0]
            
            # 创建分析记录
            record = AnalysisRecord(
                filename=f"{analysis_type}_sample_{i+1:03d}.pdf",
                analysis_type=analysis_type,
                status=status,
                review_status=review_status,
                review_priority=random.choice(['low', 'normal', 'high']),
                file_hash=f"hash_{analysis_type}_{i+1}",
                created_by=admin_user.id if i % 2 == 0 else (analyst_user.id if analyst_user else admin_user.id),
                created_at=created_at,
                updated_at=created_at
            )
            
            # 如果是已完成状态，添加结果数据
            if status == 'completed':
                # 模拟AI结果
                ai_result = {
                    "extracted_fields": {
                        "customer_name": f"客户{i+1:03d}",
                        "amount": round(random.uniform(1000, 1000000), 2),
                        "date": (created_at - timedelta(days=random.randint(1, 10))).strftime("%Y-%m-%d"),
                        "account_number": f"ACC{random.randint(100000, 999999)}"
                    },
                    "confidence": round(random.uniform(0.8, 1.0), 3)
                }
                record.set_ai_result(ai_result)
                
                # 设置准确率
                record.accuracy_score = Decimal(str(round(random.uniform(0.7, 1.0), 4)))
            
            db.session.add(record)
            total_created += 1
            
            # 批量提交
            if total_created % 50 == 0:
                db.session.commit()
    
    # 最终提交
    db.session.commit()
    print(f"示例数据创建完成，共创建 {total_created} 条分析记录")

def create_dashboard_stats():
    """创建仪表盘统计数据"""
    print("创建仪表盘统计数据...")

    # 使用配置文件中的分析类型创建过去30天的统计数据
    analysis_types = list(Config.ANALYSIS_TYPES.keys())
    
    created_count = 0
    for days_ago in range(30):
        stat_date = date.today() - timedelta(days=days_ago)
        
        for analysis_type in analysis_types:
            # 随机生成统计数据
            total_files = random.randint(5, 50)
            processed_files = random.randint(int(total_files * 0.7), total_files)
            success_count = random.randint(int(processed_files * 0.8), processed_files)
            failed_count = processed_files - success_count
            
            stats = DashboardStats(
                stat_date=stat_date,
                analysis_type=analysis_type,
                total_files=total_files,
                processed_files=processed_files,
                accuracy_rate=Decimal(str(round(random.uniform(0.85, 0.98), 4))),
                avg_processing_time=Decimal(str(round(random.uniform(10, 120), 2))),
                success_count=success_count,
                failed_count=failed_count
            )
            
            db.session.add(stats)
            created_count += 1
    
    db.session.commit()
    print(f"仪表盘统计数据创建完成，共创建 {created_count} 条记录")

def create_sample_mock_data():
    """创建示例挡板数据"""
    print("创建示例挡板数据...")

    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        print("  警告: 未找到管理员用户，跳过挡板数据创建")
        return

    mock_data_samples = [
        {
            'query_key': 'customer_info_query',
            'query_type': 'customer_system',
            'mock_result': {
                'customer_name': '张三',
                'customer_id': 'CUST001',
                'account_number': 'ACC123456789',
                'phone': '***********',
                'email': '<EMAIL>'
            },
            'description': '客户信息查询挡板数据'
        },
        {
            'query_key': 'account_balance_query',
            'query_type': 'account_system',
            'mock_result': {
                'account_number': 'ACC123456789',
                'balance': 100000.00,
                'currency': 'CNY',
                'last_update': '2025-01-06T10:00:00'
            },
            'description': '账户余额查询挡板数据'
        }
    ]

    created_count = 0
    for mock_data in mock_data_samples:
        try:
            existing = MockData.query.filter_by(
                query_key=mock_data['query_key'],
                query_type=mock_data['query_type']
            ).first()

            if existing:
                print(f"  挡板数据已存在: {mock_data['query_key']}")
                continue

            mock_record = MockData(
                query_key=mock_data['query_key'],
                query_type=mock_data['query_type'],
                description=mock_data['description'],
                created_by=admin_user.id
            )
            mock_record.set_mock_result(mock_data['mock_result'])

            db.session.add(mock_record)
            db.session.commit()

            print(f"  ✓ 创建挡板数据: {mock_data['query_key']}")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 挡板数据创建失败: {mock_data['query_key']} - {str(e)}")

    print(f"挡板数据创建完成，新增 {created_count} 条记录")

def create_sample_standard_answers():
    """创建示例标准答案"""
    print("创建示例标准答案...")

    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        print("  警告: 未找到管理员用户，跳过标准答案创建")
        return

    # 为每个分析类型创建一个标准答案模板
    created_count = 0
    for analysis_type, type_name in Config.ANALYSIS_TYPES.items():
        try:
            existing = StandardAnswer.query.filter_by(
                analysis_type=analysis_type,
                file_pattern='*_template.pdf'
            ).first()

            if existing:
                print(f"  标准答案已存在: {analysis_type}")
                continue

            # 创建标准答案模板
            standard_result = {
                'document_type': type_name,
                'extracted_fields': {
                    'document_number': '示例编号001',
                    'date': '2025-01-06',
                    'amount': 100000.00,
                    'customer_name': '示例客户',
                    'status': '有效'
                },
                'confidence_scores': {
                    'document_number': 0.95,
                    'date': 0.98,
                    'amount': 0.92,
                    'customer_name': 0.90,
                    'status': 0.88
                }
            }

            standard_answer = StandardAnswer(
                analysis_type=analysis_type,
                file_pattern='*_template.pdf',
                standard_result=standard_result,
                confidence_score=Decimal('0.9500'),
                created_by=admin_user.id
            )

            db.session.add(standard_answer)
            db.session.commit()

            print(f"  ✓ 创建标准答案: {analysis_type}")
            created_count += 1

        except IntegrityError as e:
            db.session.rollback()
            print(f"  ✗ 标准答案创建失败: {analysis_type} - {str(e)}")

    print(f"标准答案创建完成，新增 {created_count} 条记录")

def cleanup_old_data():
    """清理旧数据（可选）"""
    print("检查是否需要清理旧数据...")

    # 检查是否有数据
    record_count = AnalysisRecord.query.count()
    if record_count > 0:
        print(f"  发现 {record_count} 条现有分析记录")

        # 这里可以添加清理逻辑，比如删除测试数据
        # 为了安全，默认不删除
        print("  保留现有数据，如需清理请手动操作")
    else:
        print("  没有发现现有数据")

def upgrade_database():
    """升级数据库结构"""
    print("检查数据库结构升级...")

    try:
        # 检查是否需要添加新的全局设置
        existing_settings = {setting.key for setting in GlobalSetting.query.all()}

        # 需要的设置键
        required_settings = {
            'system_name', 'system_version', 'company_name', 'auto_analysis',
            'max_file_size', 'allowed_file_types', 'records_per_page',
            'mock_mode_enabled', 'session_timeout', 'enable_audit_log',
            'analysis_types'
        }

        missing_settings = required_settings - existing_settings
        if missing_settings:
            print(f"  发现缺失的设置项: {', '.join(missing_settings)}")
            print("  将在全局设置创建步骤中补充")
        else:
            print("  ✓ 全局设置完整")

        # 检查分析类型是否需要更新
        analysis_types_setting = GlobalSetting.query.filter_by(key='analysis_types').first()
        if analysis_types_setting:
            stored_types = analysis_types_setting.get_value()
            current_types = Config.ANALYSIS_TYPES

            if stored_types != current_types:
                print("  发现分析类型配置变更，将在设置更新中同步")
            else:
                print("  ✓ 分析类型配置一致")

        print("  数据库结构检查完成")

    except Exception as e:
        print(f"  警告: 数据库升级检查失败: {str(e)}")
        print("  将继续执行初始化流程")

def main(args=None):
    """主函数"""
    print("=" * 70)
    print("多场景智能化文档分析系统 - 数据库初始化")
    print("版本: 3.0")
    print("更新日期: 2025-01-06")
    print("=" * 70)

    # 创建应用上下文
    app = create_app()

    with app.app_context():
        try:
            # 1. 创建所有表
            print("1. 创建数据库表...")
            db.create_all()
            print("✓ 数据库表创建完成")

            # 2. 验证数据库结构
            verify_database_structure()

            # 3. 升级数据库结构
            upgrade_database()

            # 4. 清理旧数据（可选）
            cleanup_old_data()

            # 5. 创建默认用户
            create_default_users()

            # 6. 创建默认提示词
            create_default_prompts()

            # 7. 创建模型配置
            create_default_model_configs()

            # 8. 创建全局设置
            create_global_settings()

            # 9-12. 创建示例数据（可选）
            if not (args and args.skip_sample_data):
                # 9. 创建示例挡板数据
                create_sample_mock_data()

                # 10. 创建示例标准答案
                create_sample_standard_answers()

                # 11. 创建示例分析记录
                create_sample_data()

                # 12. 创建仪表盘统计数据
                create_dashboard_stats()
            else:
                print("跳过示例数据创建（根据命令行参数）")

            print("=" * 70)
            print("✅ 数据库初始化完成！")
            print()
            print("🔐 默认用户账号:")
            print("  管理员: admin / admin123")
            print("  分析师: analyst / analyst123")
            print("  普通用户: user / user123")
            print()
            print("📊 系统配置:")
            print(f"  系统名称: {Config.SYSTEM_NAME}")
            print(f"  系统版本: {Config.SYSTEM_VERSION}")
            print(f"  公司名称: {Config.COMPANY_NAME}")
            print(f"  支持的分析类型: {len(Config.ANALYSIS_TYPES)} 种")
            print()
            print("⚠️  重要提醒:")
            print("  1. 请及时修改默认用户密码")
            print("  2. 请配置正确的AI模型API密钥")
            print("  3. 请根据实际需求调整系统设置")
            print("=" * 70)

        except Exception as e:
            print(f"❌ 数据库初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='数据库初始化脚本')
    parser.add_argument('--skip-sample-data', action='store_true',
                       help='跳过示例数据创建')
    parser.add_argument('--only-structure', action='store_true',
                       help='仅创建数据库结构，不创建示例数据')
    parser.add_argument('--reset-settings', action='store_true',
                       help='重置全局设置（会覆盖现有设置）')

    args = parser.parse_args()

    # 根据参数调整执行流程
    if args.only_structure:
        args.skip_sample_data = True

    main(args)
"""
直接返回硬编码提示词的路由，绕过其他逻辑
"""
from flask import Blueprint, jsonify, current_app, request
from flask_login import login_required
from utils.auth_utils import require_permission
from utils.hardcoded_prompts import get_default_prompts

direct_prompts_bp = Blueprint('direct_prompts', __name__)

@direct_prompts_bp.route('/api/direct-prompts', methods=['POST'])
@login_required
def get_direct_prompt():
    """直接返回硬编码提示词"""
    data = request.get_json()
    prompt_type = data.get('type')
    
    # 获取硬编码提示词
    hardcoded_prompts = get_default_prompts()
    
    if not prompt_type:
        # 如果未指定类型，返回所有
        return jsonify({
            'success': True,
            'message': '获取所有硬编码提示词成功',
            'default_prompts': hardcoded_prompts
        })
    
    # 指定了类型，尝试获取对应提示词
    if prompt_type in hardcoded_prompts:
        # 找到对应类型
        prompt_content = hardcoded_prompts[prompt_type]
        return jsonify({
            'success': True,
            'message': f'获取 {prompt_type} 类型提示词成功',
            'default_prompts': {prompt_type: prompt_content}
        })
    
    # 类型不存在，尝试通过映射查找
    mapped_types = {
        'future': ['future', 'futures_account', 'futures_member'],
        'futures_member': ['futures_member', 'future', 'futures_account', 'non_standard_trade'],
        'futures_account': ['futures_account', 'future', 'futures_member'],
        'broker_interest': ['broker_interest'],
        'ningyin_fee': ['ningyin_fee'],
        'financial': ['financial'],
        'account_opening': ['account_opening', 'product_manual'],
        'non_standard_trade': ['non_standard_trade', 'futures_member'],
        'product_manual': ['product_manual', 'account_opening']
    }
    
    # 检查是否有映射类型
    if prompt_type in mapped_types:
        for alt_type in mapped_types[prompt_type]:
            if alt_type in hardcoded_prompts:
                prompt_content = hardcoded_prompts[alt_type]
                current_app.logger.info(f"通过映射找到 {prompt_type} -> {alt_type} 提示词")
                return jsonify({
                    'success': True,
                    'message': f'通过映射获取 {prompt_type} 类型提示词成功',
                    'default_prompts': {prompt_type: prompt_content}
                })
    
    # 未找到任何匹配项，返回错误
    return jsonify({
        'success': False,
        'message': f'未找到 {prompt_type} 类型的提示词',
        'available_types': list(hardcoded_prompts.keys())
    })
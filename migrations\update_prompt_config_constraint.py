"""
更新PromptConfig表唯一约束
"""
import sqlite3
import os
from flask import current_app

def update_constraint():
    """更新PromptConfig表约束，添加version到唯一约束中"""
    try:
        # 获取数据库路径
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
        print(f"尝试连接数据库: {db_path}")
        
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            print(f"数据库文件不存在: {db_path}")
            
            # 尝试寻找其他可能的位置
            alternative_db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
            if os.path.exists(alternative_db_path):
                print(f"找到替代数据库文件: {alternative_db_path}")
                db_path = alternative_db_path
            else:
                print(f"替代数据库文件也不存在: {alternative_db_path}")
                print(f"当前工作目录: {os.getcwd()}")
                print(f"尝试查找当前目录下的数据库文件")
                
                for file in os.listdir(os.getcwd()):
                    if file.endswith('.db'):
                        print(f"找到数据库文件: {file}")
                        db_path = os.path.join(os.getcwd(), file)
                        break
        
        # 创建连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 步骤1: 创建临时表
        cursor.execute('''
        CREATE TABLE temp_prompt_config (
            id INTEGER PRIMARY KEY,
            analysis_type VARCHAR(50) NOT NULL,
            prompt_key VARCHAR(100) NOT NULL,
            prompt_content TEXT NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_default BOOLEAN DEFAULT 0,
            version VARCHAR(20) DEFAULT 'v1.0',
            created_by INTEGER REFERENCES users(id),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(analysis_type, prompt_key, version)
        )
        ''')
        
        # 步骤2: 复制数据
        cursor.execute('''
        INSERT INTO temp_prompt_config 
        SELECT id, analysis_type, prompt_key, prompt_content, description, 
               is_active, is_default, version, created_by, created_at, updated_at
        FROM prompt_config
        ''')
        
        # 步骤3: 删除原表
        cursor.execute('DROP TABLE prompt_config')
        
        # 步骤4: 重命名临时表
        cursor.execute('ALTER TABLE temp_prompt_config RENAME TO prompt_config')
        
        # 提交更改
        conn.commit()
        
        print("PromptConfig表唯一约束已更新")
        return True
        
    except Exception as e:
        print(f"更新唯一约束失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == '__main__':
    update_constraint()
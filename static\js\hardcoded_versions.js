// hardcoded_versions.js - 硬编码真实版本数据
console.log('加载硬编码版本数据...');

// 硬编码的真实数据（按分析类型分组）
const HARDCODED_VERSIONS_BY_TYPE = {
  "financial": [
    {
      "id": 5,
      "version": "vmdpsmr",
      "name": "",
      "description": "",
      "is_active": false,
      "prompt_content": "请从图片中提取理财产品说明书的相关信息，严格按照JSON格式输出：\n\n请以JSON格式输出，格式如下：\n```json\n{\n  \"产品信息\": {\n    \"产品名称\": \"产品名称\",\n    \"产品代码\": \"产品代码\",\n    \"产品类型\": \"产品类型\",\n    \"管理人\": \"管理人\",\n    \"托管人\": \"托管人\",\n    \"登记备案\": \"登记备案\",\n    \"成立日期\": \"成立日期\",\n    \"产品期限\": \"产品期限\",\n    \"投资范围\": \"投资范围\",\n    \"预警线\": \"预警线\",\n    \"止损线\": \"止损线\"\n  },\n  \"费率结构\": {\n    \"认购费\": \"认购费\",\n    \"申购费\": \"申购费\",\n    \"赎回费\": \"赎回费\",\n    \"销售服务费\": \"销售服务费\",\n    \"固定管理费\": \"固定管理费\",\n    \"浮动管理费\": \"浮动管理费\",\n    \"托管费\": \"托管费\"\n  },\n  \"业绩比较基准\": \"业绩比较基准\",\n  \"估值方法\": \"估值方法\",\n  \"开放期安排\": \"开放期安排\"\n}\n```"
    }
  ],
  "future": [
    {
      "id": 16,
      "version": "v1753926598",
      "name": "强制刷新版本 (1753926598)",
      "description": "强制刷新版本 (1753926598)",
      "is_active": true,
      "prompt_content": "刷新"
    },
    {
      "id": 4,
      "version": "vmdpsme",
      "name": "future 版本 vmdpsme",
      "description": "future 版本 vmdpsme",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 6,
      "version": "vmdpsrz",
      "name": "future 版本 2025/7/30",
      "description": "future 版本 2025/7/30",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 7,
      "version": "vmdpsyc",
      "name": "future 版本 2025/7/30",
      "description": "future 版本 2025/7/30",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 8,
      "version": "vmdpt8i",
      "name": "future 版本 2025/7/30",
      "description": "future 版本 2025/7/30",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 9,
      "version": "vmdpti3",
      "name": "future 版本 2025/7/30",
      "description": "future 版本 2025/7/30",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 10,
      "version": "vmdptqi",
      "name": "vmdptqi - future 版本 2025/7/30 (0730-1829)",
      "description": "vmdptqi - future 版本 2025/7/30 (0730-1829)",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 11,
      "version": "vmdptvo",
      "name": "vmdptvo - future 版本 2025/7/30 (2025/07/30 18:33)",
      "description": "vmdptvo - future 版本 2025/7/30 (2025/07/30 18:33)",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 12,
      "version": "vmdpu8e",
      "name": "future 版本 2025/7/30",
      "description": "future 版本 2025/7/30",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 13,
      "version": "vmdqp65",
      "name": "future 版本 2025/7/31",
      "description": "future 版本 2025/7/31",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 14,
      "version": "vmdqpex",
      "name": "future 版本 2025/7/31",
      "description": "future 版本 2025/7/31",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    },
    {
      "id": 15,
      "version": "vmdqpjw",
      "name": "future 版本 2025/7/31",
      "description": "future 版本 2025/7/31",
      "is_active": false,
      "prompt_content": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```"
    }
  ],
  "test_type": [
    {
      "id": 1,
      "version": "v1.0",
      "name": "默认测试版本",
      "description": "默认测试版本",
      "is_active": true,
      "prompt_content": "这是一个测试提示词"
    },
    {
      "id": 2,
      "version": "v2.0",
      "name": "第二个测试版本",
      "description": "第二个测试版本",
      "is_active": false,
      "prompt_content": "这是另一个测试提示词"
    }
  ]
};

// 默认提示词（当没有版本时使用）
const DEFAULT_PROMPTS = {
  "future": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```",
  "futures_member": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```",
  "futures_account": "你是一名期货开户文件解析专家。请严格区分\"会员号\"（固定 4 位数字）和\"交易编码\"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n2. 资金账号：资金账户号码\n3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填\"/\"）\n4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为\"投机\"）\n5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填\"/\"）\n6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填\"/\"）\n\n=====================\n【交易所名称映射】\n- 上期所＝上海期货交易所／上海交易所\n- 大商所＝大连商品交易所／大连交易所\n- 郑商所＝郑州商品交易所／郑州交易所\n- 中金所＝中国金融期货交易所／金融交易所\n- 上能所＝上海能源交易所／能源中心\n- 广期所＝广州期货交易所／广州交易所\n\n=====================\n【账户用途映射】\n- 投机＝投机交易账户\n- 套利＝套利交易账户\n- 套保＝套期保值交易账户\n\n若文档未写明用途，默认\"投机\"。\n\n=====================\n【关键区别提醒】\n- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n\n=====================\n【输出 JSON 格式示例】\n```json\n{\n  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n  \"资金账号\": \"2120061\",\n  \"会员号\": {\n    \"上期所\": \"0121\",\n    \"大商所\": \"/\",\n    \"郑商所\": \"0059\",\n    \"中金所\": \"0170\",\n    \"上能所\": \"8059\",\n    \"广期所\": \"0021\"\n  },\n  \"交易编码\": {\n    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"郑商所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"中金所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"上能所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"},\n    \"广期所\": {\"投机\": \"********\", \"套利\": \"/\", \"套保\": \"/\"}\n  },\n  \"开始时间\": \"2025-01-01\",\n  \"结束时间\": \"/\"\n}\n```",
  "broker_interest": "请从图片中提取券商账户计息变更的相关信息，严格按照以下格式输出JSON数组：\n\n=====================【必须提取的字段】=====================\n1. **产品名称**：具体的产品或资产名称\n2. **产品类别**：区分是\"单产品\"还是\"全公司产品\"等\n3. **利率(年化)**：\n   • 如果是统一利率，格式为 `{\"all\": \"X.XX%\"}`\n   • 如果按客户类型分段，格式为 `{\"个人\": \"X.XX%\", \"非个人\": \"X.XX%\"}`\n   • 如果按时间分段，格式为 `{\"START:YYYY-MM-DD\": \"X.XX%\", \"YYYY-MM-DD:END\": \"X.XX%\"}`\n4. **开始时间**：变更生效的开始日期（YYYY-MM-DD格式）\n5. **截止时间**：变更的截止日期，如无明确截止则填\"\"  \n6. **计息天数**：年化计息的天数基础（如360天、365天等）\n7. **备注**：其他重要信息\n\n=====================【输出JSON数组示例】=====================\n```json\n[\n  {\n    \"产品名称\": \"汇添富远景成长一年持有期混合型基金\",\n    \"产品类别\": \"单产品\",\n    \"利率(年化)\": {\"all\": \"1.4%\"},\n    \"开始时间\": \"2025-01-02\",\n    \"截止时间\": \"\",\n    \"计息天数\": 360,\n    \"备注\": \"按月20日前结息至期货账户\"\n  }\n]\n```",
  "ningyin_fee": "你是一名费用变更文件解析专家。请从用户提供的宁银理财费用变更通知书中提取以下信息，并用JSON数组格式返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：理财产品的全称\n2. 变更类别：例如\"管理费率变更\"、\"托管费率变更\"、\"估值服务费变更\"等\n3. 变更前费率：变更前的费率值，如\"0.2%\"、\"0.04%\"，不含费率则填\"/\"\n4. 变更后费率：变更后的费率值，如\"0.3%\"、\"0.05%\"，不含费率则填\"/\"\n5. 生效日期：费率变更的生效日期（YYYY-MM-DD格式，缺失填\"/\"）\n6. 通知日期：通知发布的日期（YYYY-MM-DD格式，缺失填\"/\"）\n\n=====================\n【输出JSON数组示例】\n```json\n[\n  {\n    \"产品名称\": \"宁银理财宁欣固定收益12个月持有期第2期\",\n    \"变更类别\": \"管理费率变更\",\n    \"变更前费率\": \"0.4%\",\n    \"变更后费率\": \"0.2%\",\n    \"生效日期\": \"2025-02-01\",\n    \"通知日期\": \"2025-01-15\"\n  },\n  {\n    \"产品名称\": \"宁银理财宁欣固定收益12个月持有期第2期\",\n    \"变更类别\": \"托管费率变更\",\n    \"变更前费率\": \"0.03%\",\n    \"变更后费率\": \"0.02%\",\n    \"生效日期\": \"2025-02-01\",\n    \"通知日期\": \"2025-01-15\"\n  }\n]\n```\n\n注意：同一个通知中可能包含多个产品的费率变更，或者一个产品多个费率项目的变更，请分别提取为数组的不同项。",
  "financial": "请从图片中提取理财产品说明书的相关信息，严格按照JSON格式输出：\n\n请以JSON格式输出，格式如下：\n```json\n{\n  \"产品信息\": {\n    \"产品名称\": \"产品名称\",\n    \"产品代码\": \"产品代码\",\n    \"产品类型\": \"产品类型\",\n    \"管理人\": \"管理人\",\n    \"托管人\": \"托管人\",\n    \"登记备案\": \"登记备案\",\n    \"成立日期\": \"成立日期\",\n    \"产品期限\": \"产品期限\",\n    \"投资范围\": \"投资范围\",\n    \"预警线\": \"预警线\",\n    \"止损线\": \"止损线\"\n  },\n  \"费率结构\": {\n    \"认购费\": \"认购费\",\n    \"申购费\": \"申购费\",\n    \"赎回费\": \"赎回费\",\n    \"销售服务费\": \"销售服务费\",\n    \"固定管理费\": \"固定管理费\",\n    \"浮动管理费\": \"浮动管理费\",\n    \"托管费\": \"托管费\"\n  },\n  \"业绩比较基准\": \"业绩比较基准\",\n  \"估值方法\": \"估值方法\",\n  \"开放期安排\": \"开放期安排\"\n}\n```",
  "account_opening": "你是一名账户开户场景解析专家。请从用户提供的开户文件中提取以下信息，并用JSON格式返回。\n\n=====================\n【必须提取的字段】\n1. 客户名称：开户客户的真实姓名或机构名称\n2. 客户类型：个人/机构/产品\n3. 证件类型：身份证/护照/营业执照等\n4. 证件号码：客户提供的证件号码\n5. 联系方式：手机号码或联系电话\n6. 联系地址：客户的联系地址\n7. 账户类型：需要区分是哪种类型的账户\n8. 开户银行：办理开户的银行名称\n9. 银行卡号：客户的银行卡号码\n10. 开户日期：格式为 YYYY-MM-DD",
  "non_standard_trade": "你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。\n\n=====================\n【必须提取的字段】\n1. 投资者名称：通常指代客户姓名，一般是资管计划的名称\n2. 投资者账号：通常指代客户的资金账号\n3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填\"/\"）\n4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回\n5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等\n6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填\"/\"）\n7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）",
  "product_manual": "你是一名产品手册解析专家。请从用户提供的产品手册文档中提取以下信息，并用JSON格式返回。\n\n=====================\n【必须提取的字段】\n1. 产品名称：产品的正式名称\n2. 产品代码：产品的唯一标识码\n3. 产品管理人：负责管理产品的机构名称\n4. 产品托管人：负责托管产品资产的机构\n5. 产品类型：如固定收益、权益类、混合类等\n6. 投资期限：产品的投资周期长度\n7. 风险等级：产品的风险评级\n8. 募集规模：计划募集的资金规模\n9. 投资门槛：最低投资金额要求\n10. 业绩比较基准：产品业绩的参考标准"
};

function getHardcodedVersions() {
    return HARDCODED_VERSIONS_BY_TYPE;
}

// 获取当前分析类型
function getCurrentAnalysisType() {
    const promptTypeSelect = document.getElementById('promptTypeSelect');
    if (promptTypeSelect) {
        return promptTypeSelect.value;
    }
    return 'future'; // 默认类型
}

// 根据分析类型获取版本数据
function getVersionsForCurrentType() {
    const currentType = getCurrentAnalysisType();
    console.log('当前分析类型:', currentType);
    
    const versions = HARDCODED_VERSIONS_BY_TYPE[currentType] || [];
    console.log(`获取到 ${currentType} 类型的版本数据:`, versions.length, '个版本');
    
    return versions;
}

// 获取默认提示词
function getDefaultPromptForType(analysisType) {
    return DEFAULT_PROMPTS[analysisType] || DEFAULT_PROMPTS['future'];
}

// 覆盖所有版本相关的函数
function loadPromptConfigWithHardcoded() {
    console.log('使用硬编码数据加载版本配置');
    
    // 显示加载状态
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
    
    // 获取当前类型的版本数据
    const currentVersions = getVersionsForCurrentType();
    
    // 使用硬编码数据
    allVersions = currentVersions.map(version => ({
        id: version.id,
        version: version.version,
        name: version.name,
        description: version.description,
        prompt: version.prompt_content,
        is_active: Boolean(version.is_active),
        is_current: Boolean(version.is_active),
        created_at: new Date().toISOString()
    }));
    
    console.log('硬编码版本列表:', allVersions);
    
    // 隐藏加载状态
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
    
    // 更新版本选择器
    updateVersionSelectorWithHardcoded();
    
    // 显示调试信息
    // 调试面板已移除
    
    // 加载当前激活版本的提示词或默认提示词
    loadCurrentActiveVersionPrompt();
}

function updateVersionSelectorWithHardcoded() {
    console.log('使用硬编码数据更新版本选择器');
    
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect) {
        console.error('找不到版本选择器元素');
        return;
    }
    
    // 清空现有选项
    versionSelect.innerHTML = '';
    
    if (!allVersions || allVersions.length === 0) {
        console.log('没有可用的版本，显示默认选项');
        const option = document.createElement('option');
        option.value = 'default';
        option.textContent = '默认提示词';
        versionSelect.appendChild(option);
        
        // 加载默认提示词
        loadDefaultPrompt();
        return;
    }
    
    console.log('开始渲染硬编码版本选项，总数:', allVersions.length);
    
    // 按激活状态排序
    const sortedVersions = [...allVersions].sort((a, b) => {
        // 激活版本优先
        if (a.is_active && !b.is_active) return -1;
        if (!a.is_active && b.is_active) return 1;
        // 然后按ID降序
        return b.id - a.id;
    });
    
    sortedVersions.forEach((version, index) => {
        const option = document.createElement('option');
        option.value = version.id;
        
        // 构建显示文本 - 使用版本号和版本名称拼接
        let displayText = `${version.version} - ${version.name}`;
        if (version.is_active) {
            displayText = `⭐ ${displayText}`;
        }
        if (version.version === 'v1.0') {
            displayText += ' (默认)';
        }
        
        option.textContent = displayText;
        
        // 添加调试属性
        option.setAttribute('data-version-id', version.id);
        option.setAttribute('data-version-number', version.version);
        option.setAttribute('data-version-name', version.name);
        option.setAttribute('data-is-active', version.is_active);
        
        versionSelect.appendChild(option);
        
        console.log(`添加硬编码选项 ${index + 1}:`, {
            id: version.id,
            version: version.version,
            name: version.name,
            is_active: version.is_active,
            display_text: displayText
        });
    });
    
    console.log('硬编码版本选择器更新完成，选项数量:', versionSelect.options.length);
}

// 加载默认提示词
function loadDefaultPrompt() {
    console.log('加载默认提示词');
    
    const currentType = getCurrentAnalysisType();
    const defaultPrompt = getDefaultPromptForType(currentType);
    
    const systemPromptElement = document.getElementById('systemPrompt');
    if (systemPromptElement) {
        systemPromptElement.value = defaultPrompt;
        console.log('已加载默认提示词，长度:', defaultPrompt.length);
    } else {
        console.error('找不到systemPrompt元素');
    }
}

// 加载当前激活版本的提示词
function loadCurrentActiveVersionPrompt() {
    console.log('加载当前激活版本的提示词');
    
    // 如果没有版本，加载默认提示词
    if (!allVersions || allVersions.length === 0) {
        console.log('没有可用版本，加载默认提示词');
        loadDefaultPrompt();
        return;
    }
    
    // 查找激活版本
    const activeVersion = allVersions.find(v => v.is_active);
    if (activeVersion) {
        console.log('找到激活版本:', activeVersion);
        
        // 设置版本选择器为激活版本
        const versionSelect = document.getElementById('versionSelect');
        if (versionSelect) {
            versionSelect.value = activeVersion.id;
            console.log('设置版本选择器为激活版本:', activeVersion.id);
        }
        
        // 加载激活版本的提示词
        loadPromptForVersion(activeVersion);
    } else {
        console.log('未找到激活版本，使用第一个版本');
        if (allVersions.length > 0) {
            const firstVersion = allVersions[0];
            console.log('使用第一个版本:', firstVersion);
            
            // 设置版本选择器为第一个版本
            const versionSelect = document.getElementById('versionSelect');
            if (versionSelect) {
                versionSelect.value = firstVersion.id;
                console.log('设置版本选择器为第一个版本:', firstVersion.id);
            }
            
            // 加载第一个版本的提示词
            loadPromptForVersion(firstVersion);
        }
    }
}

// 为指定版本加载提示词
function loadPromptForVersion(version) {
    console.log('为版本加载提示词:', version);
    
    const systemPromptElement = document.getElementById('systemPrompt');
    if (systemPromptElement) {
        systemPromptElement.value = version.prompt || '';
        console.log('已加载提示词，长度:', (version.prompt || '').length);
    } else {
        console.error('找不到systemPrompt元素');
    }
}

// 硬编码调试面板功能已移除

// 覆盖更新当前版本函数
function updateCurrentVersionWithHardcoded() {
    console.log('使用硬编码数据更新当前版本');
    
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect || versionSelect.value === '') {
        showMessage('请先选择一个版本', 'warning');
        return;
    }
    
    // 如果选择的是默认提示词，不执行更新
    if (versionSelect.value === 'default') {
        showMessage('默认提示词无需更新', 'info');
        return;
    }
    
    const selectedVersionId = parseInt(versionSelect.value);
    console.log('选中的版本ID:', selectedVersionId);
    
    // 在硬编码数据中查找版本
    const selectedVersion = allVersions.find(v => v.id === selectedVersionId);
    if (selectedVersion) {
        console.log('在硬编码数据中找到版本:', selectedVersion);
        
        // 显示加载状态
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
        
        // 模拟更新操作
        setTimeout(() => {
            // 隐藏加载状态
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
            
            showMessage('当前版本更新成功', 'success');
            
            // 重新加载版本配置以确保显示正确
            loadPromptConfigWithHardcoded();
        }, 1000);
        
        return;
    }
    
    console.log('在硬编码数据中未找到版本');
    showMessage('未找到选中的版本', 'error');
}

// 覆盖版本选择变化处理函数
function onVersionChangeWithHardcoded() {
    console.log('使用硬编码数据处理版本选择变化');
    
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect || versionSelect.value === '') {
        console.log('没有选中的版本');
        return;
    }
    
    // 如果选择的是默认提示词
    if (versionSelect.value === 'default') {
        console.log('选择默认提示词');
        loadDefaultPrompt();
        return;
    }
    
    const selectedVersionId = parseInt(versionSelect.value);
    console.log('选中的版本ID:', selectedVersionId);
    
    // 在硬编码数据中查找版本
    const selectedVersion = allVersions.find(v => v.id === selectedVersionId);
    if (selectedVersion) {
        console.log('在硬编码数据中找到版本:', selectedVersion);
        loadPromptForVersion(selectedVersion);
        return;
    }
    
    console.log('在硬编码数据中未找到版本');
}

// 处理功能类型选择变化
function onAnalysisTypeChange() {
    console.log('功能类型发生变化，重新加载版本数据');
    
    // 重新加载版本配置
    loadPromptConfigWithHardcoded();
}

// 重置为默认功能
function resetToDefaultWithHardcoded() {
    console.log('重置为默认功能');
    
    // 显示加载状态
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
    
    // 模拟重置操作
    setTimeout(() => {
        // 隐藏加载状态
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        
        // 加载默认提示词
        loadDefaultPrompt();
        
        // 设置版本选择器为默认
        const versionSelect = document.getElementById('versionSelect');
        if (versionSelect) {
            versionSelect.value = 'default';
        }
        
        showMessage('已重置为默认提示词', 'success');
    }, 1000);
}

// 强制覆盖所有相关函数
console.log('硬编码函数已加载');
window.loadPromptConfig = loadPromptConfigWithHardcoded;
window.updateVersionSelector = updateVersionSelectorWithHardcoded;
// window.showDebugInfo 已移除
window.updateCurrentVersion = updateCurrentVersionWithHardcoded;
window.onVersionChange = onVersionChangeWithHardcoded;
window.loadPromptConfigWithHardcoded = loadPromptConfigWithHardcoded;
window.updateVersionSelectorWithHardcoded = updateVersionSelectorWithHardcoded;
// window.showDebugInfoWithHardcoded 已移除
window.loadCurrentActiveVersionPrompt = loadCurrentActiveVersionPrompt;
window.loadPromptForVersion = loadPromptForVersion;
window.loadDefaultPrompt = loadDefaultPrompt;
window.resetToDefault = resetToDefaultWithHardcoded;
window.getHardcodedVersions = getHardcodedVersions;
window.onAnalysisTypeChange = onAnalysisTypeChange;

// 监听版本选择器变化
document.addEventListener('DOMContentLoaded', function() {
    const versionSelect = document.getElementById('versionSelect');
    if (versionSelect) {
        versionSelect.addEventListener('change', onVersionChangeWithHardcoded);
    }
    
    // 监听功能类型选择器变化
    const promptTypeSelect = document.getElementById('promptTypeSelect');
    if (promptTypeSelect) {
        promptTypeSelect.addEventListener('change', onAnalysisTypeChange);
        console.log('已监听功能类型选择器变化');
    }
});

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器错误 - {{ SYSTEM_NAME }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .error-message {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #ef4444;
            color: #ef4444;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: #ef4444;
            border-color: #ef4444;
            transform: translateY(-2px);
        }
        
        .error-icon {
            font-size: 4rem;
            color: #ef4444;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .error-details {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .error-details h6 {
            color: #dc2626;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .error-details p {
            color: #991b1b;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .suggestions {
            background-color: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .suggestions h6 {
            color: #333;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .suggestions ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .suggestions li {
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .back-link {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: translateX(-5px);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .back-link {
                position: static;
                display: inline-block;
                margin-bottom: 2rem;
            }
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
        
        .refresh-btn {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰 -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-gear" style="font-size: 2.5rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-tools" style="font-size: 3.5rem;"></i>
        </div>
    </div>
    
    <!-- 返回链接 -->
    <a href="javascript:history.back()" class="back-link d-none d-md-block">
        <i class="bi bi-arrow-left me-2"></i>
        返回上一页
    </a>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-exclamation-octagon"></i>
        </div>
        
        <div class="error-code">500</div>
        
        <h1 class="error-title">服务器内部错误</h1>
        
        <p class="error-message">
            抱歉，服务器遇到了一个意外错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在紧急处理中。
        </p>
        
        <div class="error-details">
            <h6><i class="bi bi-info-circle me-2"></i>错误信息</h6>
            <p>
                <strong>错误时间：</strong><span id="errorTime"></span><br>
                <strong>错误代码：</strong>HTTP 500 Internal Server Error<br>
                <strong>请求路径：</strong><span id="requestPath"></span>
            </p>
        </div>
        
        <div class="suggestions">
            <h6><i class="bi bi-lightbulb me-2"></i>您可以尝试：</h6>
            <ul>
                <li>刷新页面重新尝试</li>
                <li>稍后再次访问</li>
                <li>清除浏览器缓存</li>
                <li>联系技术支持报告问题</li>
            </ul>
        </div>
        
        <div class="error-actions">
            <button onclick="location.reload()" class="btn btn-primary refresh-btn">
                <i class="bi bi-arrow-clockwise me-2"></i>
                刷新页面
            </button>
            <a href="/" class="btn btn-outline-primary">
                <i class="bi bi-house me-2"></i>
                返回首页
            </a>
            <button onclick="history.back()" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-2"></i>
                返回上一页
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                如果问题持续存在，请联系技术支持：
                <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                <br>
                错误ID: <span id="errorId"></span>
            </small>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置错误时间
            const now = new Date();
            document.getElementById('errorTime').textContent = now.toLocaleString('zh-CN');
            
            // 设置请求路径
            document.getElementById('requestPath').textContent = window.location.pathname;
            
            // 生成错误ID
            const errorId = 'ERR-' + Date.now().toString(36).toUpperCase();
            document.getElementById('errorId').textContent = errorId;
            
            // 页面加载动画
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
            
            // 自动刷新倒计时（可选）
            let countdown = 30;
            const refreshBtn = document.querySelector('.refresh-btn');
            
            // 取消自动刷新功能，改为手动刷新
            // const countdownInterval = setInterval(() => {
            //     countdown--;
            //     refreshBtn.innerHTML = `<i class="bi bi-arrow-clockwise me-2"></i>刷新页面 (${countdown}s)`;
            //     
            //     if (countdown <= 0) {
            //         clearInterval(countdownInterval);
            //         location.reload();
            //     }
            // }, 1000);
            
            // 点击刷新按钮时清除倒计时
            refreshBtn.addEventListener('click', function() {
                // clearInterval(countdownInterval);
                this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>刷新中...';
                this.disabled = true;
            });
        });
        
        // 记录500错误（可选）
        if (typeof gtag !== 'undefined') {
            gtag('event', 'server_error', {
                'error_code': '500',
                'page_location': window.location.href,
                'page_title': document.title
            });
        }
        
        // 发送错误报告到服务器（可选）
        fetch('/api/error-report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                error_type: '500',
                url: window.location.href,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString()
            })
        }).catch(err => {
            console.log('Error report failed:', err);
        });
    </script>
</body>
</html>

# 期货交易会员解析 - 本地环境适配版本
import sys
import os
# 添加当前项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from util_ai import ChatBot
from utils import fn_to_markdown_v2, markdown_json_to_dict
import json
import datetime

def run_future_account_analysis(file_path):
    """
    运行期货账户分析
    """
    usage_model = 'InternVL3-38B'
    
    try:
        if file_path.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(
                file_path, 
                convert_to_scanned=False, 
                ai_seal=True, 
                ai_model='InternVL3-38B', 
                add_ocr_info=False
            )
        else:
            # 如果是图片则先转为pdf
            markdown_content, seal_img_list = fn_to_markdown_v2(
                file_path, 
                convert_to_scanned=True, 
                ai_seal=True, 
                ai_model='InternVL3-38B'
            )    
            
        chatbot = ChatBot(model=usage_model,
        system_prompt="""你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝能源所／上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
        """
        )
        
        if len(seal_img_list) > 0:
            response = chatbot.chat_with_img(markdown_content, img_url=seal_img_list)
        else:
            response = chatbot.chat(markdown_content)
            
        json_data = markdown_json_to_dict(response)
        return json_data
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        return {"error": str(e)}

def test_single_file():
    """
    测试单个文件
    """
    file_path = input("请输入要分析的文件路径: ").strip()
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    print(f"正在分析文件: {file_path}")
    result = run_future_account_analysis(file_path)
    
    # 保存结果
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"future_account_result_{timestamp}.json"
    
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=4)
    
    print(f"分析完成，结果已保存到: {output_file}")
    print("分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    print("期货账户分析工具 - 本地版本")
    print("=" * 50)
    
    select_option = input("请选择操作：\n1. 分析单个文件\n2. 退出\n请输入选择 (1-2): ").strip()
    
    if select_option == "1":
        test_single_file()
    elif select_option == "2":
        print("退出程序")
    else:
        print("无效选择！")

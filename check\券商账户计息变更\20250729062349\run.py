def run(fn):
    usage_model = 'InternVL3-38B'
    if fn.endswith(".pdf"):
        markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model='InternVL3-38B')
    else:
        # 如果是图片则先转为pdf
        markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B')
    sys_prompt = """你是一位资深的银行托管部经理，负责从客户提供的文件内容（邮件、公告、合同、通知等）中**抽取基金计息变更要素**，并按以下要求结构化输出 JSON 数组。

=====================【字段定义】=====================
1. 【产品名称】
   • 如果正文**出现了具体的XX基金、XX系列基金、XX产品**名称，就逐一完整提取这些名称；  
   • 只有在正文出现 “本公司全部产品 / 全公司所有基金 / X X 公司所有产品”等措辞，**且未列举任何具体产品**时，才将“X X 公司”作为【产品名称】，并认定为“全公司产品”。
   • 当产品类别为“全公司产品”时，【产品名称】必定为XX公司、XX证券、XX基金等。

2. 【产品类别】枚举值：单产品 / 系列产品 / 全公司产品  
   • 判断依据同上：列举多只不同基金 ⇒ “单产品”（多行输出）；  
     明确写“系列基金 / 系列产品” ⇒ “系列产品”；  
     出现“全公司全部产品”且未列举 ⇒ “全公司产品”。

3. 【利率(年化)】——*嵌套 JSON*  
   用 **键值对** 表示分段利率，遵循以下规则：  
   • **单一利率** → `{"all": "X.X%"}`  
   • **按客户类型分段** → 外层键为 “个人”“非个人”“机构”等；值可以是：  
     - 单一值 → `"0.10%"`  
     - **再按日期分段** → 内层对象，键格式：  
       - `START:YYYY-MM-DD` 表示 *起始至该日（含）*  
       - `YYYY-MM-DD:END` 表示 *该日起（含）至结束*  
       - `YYYY-MM-DD至YYYY-MM-DD` 表示闭区间  
     例：`"非个人": {"START:2024-11-10": "0.15%", "2024-11-11:END": "0.10%"}`  
   • **仅按日期分段**（不区分客户类型） → 直接用内层对象：  
     `{"START:2025-01-01": "1.5%", "2025-07-01:END": "1.2%"}`  
   • 多维分段时，优先按 **客户类型** 外层、**日期** 内层。
   • 注意：如未给出具体的利率，但告知了在原利率基础上调整，则利率格式为"BASE+X%"、"BASE-X%"。

4. 【开始时间】【截止时间】  
   • 如文档给出单一“计息起始日”，则写入【开始时间】；【截止时间】留空，除非正文给出明确结束日期。 如果文档未给出明确的开始时间，则使用落款日期作为开始时间。
   • 如果分段计息，每条分段各自写一行 JSON（推荐）或在【利率(年化)】内注明日期区间并将最早日期写入【开始时间】，最晚日期写入【截止时间】。
   • 当日期区间未出现具体的日（即缺少DD），则使用格式`YYYY-MM-None`。

5. 【计息天数】  
   • 常见值 360 / 365 / “实际天数”。若文档只出现“/360”或“/365”，直接填数字；若写“按实际天数”，填“实际天数”；否则留空。

6. 【备注】  
   • 放无法归入前述字段但对计息有影响的信息，如“利率随市场调整”“按月结息至期货账户”等。  
   • 对于按客户类型分段的说明，也可以简要复述，以便人工复核。

=====================【格式要求】=====================
• 日期全部转为 `YYYY-MM-DD`。  如未出现具体的日（即缺少DD），则使用格式`YYYY-MM-None`。
• 跨页或 HTML 表格拆分的内容需合并后再识别。  
• 输出 **JSON 数组**，字段顺序固定，示例如下：

```json
[
  {
    "产品名称": "汇添富远景成长一年持有期混合型基金",
    "产品类别": "单产品",
    "利率(年化)": {"all": "1.4%"},
    "开始时间": "2025-01-02",
    "截止时间": "",
    "计息天数": 360,
    "备注": "按月20日前结息至期货账户；利率随市场利率调整"
  },
  {
    "产品名称": "国泰君安证券股份有限公司",
    "产品类别": "全公司产品",
    "利率(年化)": {"个人": "0.10%", "非个人": {"START:2024-11-10": "0.15%", "2024-11-11:END": "0.10%"}},
    "开始时间": "2024-11-11",
    "截止时间": "",
    "计息天数": "",
    "备注": "经纪/两融/期权/贵金属保证金统一执行"
  }
]
```
"""
    chat_bot = ChatBot(system_prompt=sys_prompt, model=usage_model)

    res = chat_bot.chat(markdown_content, top_p=0.8, temperature=0.2)
    json_data = markdown_json_to_dict(res)
    return json_data

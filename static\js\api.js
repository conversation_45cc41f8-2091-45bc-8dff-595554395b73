/**
 * API工具类 - 封装了所有API调用
 */
const API = {
    /**
     * 发送GET请求
     * @param {string} url - API地址
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回响应数据
     */
    get: function(url, params = {}) {
        const queryString = Object.keys(params)
            .filter(key => params[key] !== undefined && params[key] !== null)
            .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
            .join('&');
        
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送POST请求
     * @param {string} url - API地址
     * @param {Object} data - 请求体数据
     * @returns {Promise} - 返回响应数据
     */
    post: function(url, data = {}) {
        return fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送PUT请求
     * @param {string} url - API地址
     * @param {Object} data - 请求体数据
     * @returns {Promise} - 返回响应数据
     */
    put: function(url, data = {}) {
        return fetch(url, {
            method: 'PUT',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送DELETE请求
     * @param {string} url - API地址
     * @returns {Promise} - 返回响应数据
     */
    delete: function(url) {
        return fetch(url, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 上传文件
     * @param {string} url - API地址
     * @param {FormData} formData - 表单数据
     * @param {Function} onProgress - 进度回调
     * @returns {Promise} - 返回响应数据
     */
    upload: function(url, formData, onProgress) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.open('POST', url);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            // 进度事件
            if (onProgress) {
                xhr.upload.onprogress = function(event) {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                };
            }
            
            // 完成事件
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('解析响应失败'));
                    }
                } else {
                    reject(new Error(`HTTP error! status: ${xhr.status}`));
                }
            };
            
            // 错误事件
            xhr.onerror = function() {
                reject(new Error('请求失败'));
            };
            
            xhr.send(formData);
        });
    }
};

// Utils 对象在 main.js 中定义，这里不重复定义以避免冲突

// 添加到全局作用域
window.API = API;
// Utils 在 main.js 中导出
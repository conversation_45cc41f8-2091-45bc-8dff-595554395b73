"""
修复后的delete_prompt_version函数
"""

@main_bp.route('/api/prompt-versions/<analysis_type>/<int:version_id>', methods=['DELETE'])
@login_required
@require_permission('admin')
def delete_prompt_version(analysis_type, version_id):
    """删除提示词版本，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'删除版本，类型: {analysis_type}，版本ID: {version_id}')
    
    # 获取要删除的版本
    version_to_delete = db.session.get(PromptConfig, version_id)
    if not version_to_delete or version_to_delete.analysis_type != analysis_type:
        return jsonify({'success': False, 'message': '指定版本不存在或不属于该分析类型'}), 404
    
    # 检查是否为当前激活版本
    if version_to_delete.is_active:
        return jsonify({'success': False, 'message': '无法删除当前激活的版本，请先激活其他版本'}), 400
    
    try:
        db.session.delete(version_to_delete)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'版本 {version_to_delete.version} 已删除'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除版本失败: {e}')
        return jsonify({
            'success': False,
            'message': f'删除版本失败: {str(e)}'
        }), 500
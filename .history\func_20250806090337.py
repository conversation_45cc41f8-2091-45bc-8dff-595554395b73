from util_img_process import *
import re
from util_ai import ChatBot
from mineru_pdf import trans_pdf_to_markdown
import os
import json
import pandas as pd
from datetime import datetime
import docx
from openpyxl import load_workbook
import xlrd
import PyPDF2
from PIL import Image
import email
import uuid

def markdown_to_json(markdown_text):
    """
    将markdown文本转换为JSON格式，自动去除markdown代码块标记，兼容不同换行符，增加异常处理。
    """
    import json
    # 去除首尾空白
    text = markdown_text.strip()

    # 去除<think>标签和内容
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

    if '```json' in text:
        # 使用re匹配```json和```之间的内容
        text = re.search(r'```json(.*?)```', text, re.DOTALL).group(1)
        text = text.strip()
    elif '```' in text:
        text = re.search(r'```(.*?)```', text, re.DOTALL).group(1)
        text = text.strip()
    else:
        text = text.strip()
    try:
        json_data = json.loads(text)
        return json_data
    except Exception as e:
        raise ValueError(f"JSON解析失败: {e}\n原始内容: {text}")


# ==================== 非标交易确认单解析核心模块 ====================

def process_file_to_markdown(file_path):
    """
    将不同格式的文件转换为markdown格式
    支持：PDF、Excel、图片、Word文档、邮件文件
    """
    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        if file_ext == '.pdf':
            # 处理PDF文件
            result = trans_pdf_to_markdown(
                file_path,
                parse_method='auto',
                backend='vlm-sglang-engine'
            )
            markdown_content = ""
            for fn_name, data in result['results'].items():
                markdown_content += data['md_content']
            return markdown_content

        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            # 处理图片文件 - 使用OCR方式
            result = trans_pdf_to_markdown(
                file_path,
                parse_method='ocr',
                backend='vlm-sglang-engine'
            )
            markdown_content = ""
            for fn_name, data in result['results'].items():
                markdown_content += data['md_content']
            return markdown_content

        elif file_ext in ['.xlsx', '.xls']:
            # 处理Excel文件（支持新旧格式）
            return process_excel_file(file_path)

        elif file_ext == '.docx':
            # 处理Word文档
            return process_docx_file(file_path)

        elif file_ext == '.eml':
            # 处理邮件文件
            return process_eml_file(file_path)

        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    except Exception as e:
        print(f"文件处理错误: {e}")
        return None


def process_excel_file(file_path):
    """
    处理Excel文件，转换为markdown格式，保持数字精度
    """
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        markdown_content = ""

        if file_ext == '.xlsx':
            # 处理新版Excel文件
            try:
                workbook = load_workbook(file_path, data_only=True)
                for sheet_name in workbook.sheetnames:
                    worksheet = workbook[sheet_name]
                    markdown_content += f"\n## {sheet_name}\n\n"

                    # 转换为DataFrame然后转为markdown表格
                    data = []
                    for row in worksheet.iter_rows(values_only=True):
                        row_data = []
                        for cell_value in row:
                            if cell_value is None:
                                cell_value = ""
                            elif isinstance(cell_value, (int, float)):
                                # 保持数字精度，使用高精度字符串转换
                                if isinstance(cell_value, float) and cell_value.is_integer():
                                    cell_value = int(cell_value)
                                else:
                                    # 保持原始浮点数精度
                                    cell_value = f"{cell_value:.10g}"
                            row_data.append(str(cell_value))

                        if any(cell != "" for cell in row_data):
                            data.append(row_data)

                    if data:
                        df = pd.DataFrame(data[1:], columns=data[0] if data else [])
                        markdown_content += df.to_markdown(index=False, floatfmt='.10g') + "\n\n"

            except Exception as e:
                print(f"处理.xlsx文件时出错: {e}")
                return None

        elif file_ext == '.xls':
            # 处理老版Excel文件
            return process_xls_file(file_path)

        return markdown_content

    except Exception as e:
        print(f"Excel文件处理错误: {e}")
        return None


def process_xls_file(file_path):
    """
    处理.xls文件（老版Excel格式），保持数字精度
    """
    try:
        # 读取老版Excel文件
        workbook = xlrd.open_workbook(file_path)
        markdown_content = ""

        for sheet_index in range(workbook.nsheets):
            worksheet = workbook.sheet_by_index(sheet_index)
            sheet_name = workbook.sheet_names()[sheet_index]
            markdown_content += f"\n## {sheet_name}\n\n"

            # 转换为DataFrame然后转为markdown表格
            data = []
            for row_idx in range(worksheet.nrows):
                row_data = []
                for col_idx in range(worksheet.ncols):
                    cell_value = worksheet.cell_value(row_idx, col_idx)
                    # 处理不同类型的单元格值，保持数字精度
                    if isinstance(cell_value, float):
                        # 保持浮点数的原始精度，使用高精度字符串转换
                        if cell_value.is_integer():
                            # 只有当浮点数确实是整数时才转换为int
                            cell_value = int(cell_value)
                        else:
                            # 保持原始浮点数精度，使用高精度字符串格式化
                            # 使用.10g格式保留10位有效数字，避免四舍五入
                            cell_value = f"{cell_value:.10g}"
                    row_data.append(str(cell_value) if cell_value != '' else "")

                if any(cell != "" for cell in row_data):
                    data.append(row_data)

            if data:
                df = pd.DataFrame(data[1:], columns=data[0] if data else [])
                # 保持数字精度，不使用科学计数法，保留足够的小数位
                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + "\n\n"

        return markdown_content

    except Exception as e:
        print(f".xls文件处理错误: {e}")
        return None


def process_docx_file(file_path):
    """
    处理Word文档，转换为markdown格式
    """
    try:
        doc = docx.Document(file_path)
        markdown_content = ""

        # 处理段落
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                markdown_content += paragraph.text + "\n"

        # 处理表格
        for table in doc.tables:
            markdown_content += "\n"
            for i, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())

                if i == 0:
                    # 表头
                    markdown_content += "| " + " | ".join(row_data) + " |\n"
                    markdown_content += "| " + " | ".join(["---"] * len(row_data)) + " |\n"
                else:
                    # 数据行
                    markdown_content += "| " + " | ".join(row_data) + " |\n"
            markdown_content += "\n"

        return markdown_content

    except Exception as e:
        print(f"Word文档处理错误: {e}")
        return None


def process_eml_file(file_path):
    """
    处理.eml文件（邮件格式），转换为markdown格式
    """
    try:
        # 读取邮件文件
        with open(file_path, 'rb') as f:
            msg = email.message_from_bytes(f.read())

        markdown_content = ""

        # 提取邮件头信息
        markdown_content += f"# 邮件信息\n\n"
        markdown_content += f"**发件人**: {msg.get('From', 'N/A')}\n"
        markdown_content += f"**收件人**: {msg.get('To', 'N/A')}\n"
        markdown_content += f"**主题**: {msg.get('Subject', 'N/A')}\n"
        markdown_content += f"**日期**: {msg.get('Date', 'N/A')}\n\n"

        # 提取邮件正文
        markdown_content += f"## 邮件正文\n\n"

        if msg.is_multipart():
            # 多部分邮件
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # 只处理正文，不处理附件
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        markdown_content += body + "\n\n"
                    except:
                        try:
                            body = part.get_payload(decode=True).decode('gbk', errors='ignore')
                            markdown_content += body + "\n\n"
                        except:
                            markdown_content += "[无法解码的文本内容]\n\n"

                elif content_type == "text/html" and "attachment" not in content_disposition:
                    try:
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        try:
                            html_body = part.get_payload(decode=True).decode('gbk', errors='ignore')
                        except:
                            html_body = "[无法解码的HTML内容]"

                    # 简单的HTML标签清理
                    clean_text = re.sub('<[^<]+?>', '', html_body)
                    # 清理多余的空白字符
                    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
                    markdown_content += clean_text + "\n\n"
        else:
            # 单部分邮件
            try:
                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                markdown_content += body + "\n\n"
            except:
                try:
                    body = msg.get_payload(decode=True).decode('gbk', errors='ignore')
                    markdown_content += body + "\n\n"
                except:
                    markdown_content += "[无法解码的邮件内容]\n\n"

        return markdown_content

    except Exception as e:
        print(f".eml文件处理错误: {e}")
        return None


def parse_transaction_document(file_path, save_result=True, output_dir="./output"):
    """
    解析交易确认单文档的主函数
    """
    print(f"开始解析文档...")
    print(f"开始处理文件: {file_path}")

    # 步骤1: 转换文件为markdown格式
    print("正在转换文件为markdown格式...")
    markdown_content = process_file_to_markdown(file_path)

    if not markdown_content:
        return {
            'success': False,
            'error': '文件转换失败',
            'file_path': file_path
        }

    print(f"文件转换完成，内容长度: {len(markdown_content)} 字符")

    # 步骤2: 使用大模型解析交易信息
    print("正在使用大模型解析交易信息...")
    try:
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('non_standard_trade', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            system_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的非标交易解析提示词
            system_prompt = """你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 投资者名称：通常指代客户姓名，一般是资管计划的名称
2. 投资者账号：通常指代客户的资金账号
3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回
5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填"/"）
8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填"/"）
9. 交易费用：一般申购、赎回、买入、卖出交易中，会标明交易费用（数字格式，保持原始精度不要四舍五入，缺失填"/"）

=====================
【重要注意事项】
- 所有数字字段（投资标的金额、投资标的数量、交易费用）必须保持原始精度，不要进行四舍五入
- 如果原始数据是31006.5，输出应该是"31006.5"，不要变成"31007"或"31006"
- 如果原始数据是1386040，输出应该是"1386040"，不要变成"1.38604e+06"

=====================
【业务类型映射】
- 分红：分红派息、现金分红、股息分红等
- 红利转投：红利再投资、分红转投等
- 买入：买入、购买等
- 卖出：卖出、赎回卖出等
- 认购：认购、新基金认购等
- 申购：申购、基金申购等
- 赎回：赎回、基金赎回等

=====================
【输出JSON格式】
如果文档包含多笔交易，请返回数组格式。确保数字字段保持原始精度，不要四舍五入。单笔交易示例：
{
  "投资者名称": "某某资管计划",
  "投资者账号": "123456789",
  "业务日期": "2024-01-01",
  "业务类型": "申购",
  "投资标的名称": "某某基金",
  "投资标的代码": "ABC123",
  "投资标的金额": "1000000.00",
  "投资标的数量": "100000.00",
  "交易费用": "1000.00"
}
        """
        
        # 初始化聊天机器人（已优化数字精度处理）
        chatbot = ChatBot(
            model='qwen3-32b',
            system_prompt=system_prompt
        )

        response = chatbot.chat(markdown_content)
        print("大模型解析完成")

        # 步骤3: 解析JSON结果
        json_str = response.strip()
        if json_str.startswith('```json'):
            json_str = json_str[7:]
        if json_str.endswith('```'):
            json_str = json_str[:-3]
        json_str = json_str.strip()

        parsed_result = json.loads(json_str)

        # 步骤4: 保存结果
        if save_result:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.splitext(os.path.basename(file_path))[0]

            # 保存JSON结果
            result_file = os.path.join(output_dir, f"{filename}_{timestamp}_result.json")
            os.makedirs(output_dir, exist_ok=True)

            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_result, f, ensure_ascii=False, indent=2)

            # 保存Markdown内容
            markdown_file = os.path.join(output_dir, f"{filename}_{timestamp}_markdown.md")
            with open(markdown_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"结果已保存到: {result_file}")
            print(f"Markdown内容已保存到: {markdown_file}")

        return {
            'success': True,
            'parsed_result': parsed_result,
            'markdown_content': markdown_content,
            'file_path': file_path
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'file_path': file_path,
            'markdown_content': markdown_content
        }


def non_standard_trade_analysis(file_path):
    """
    非标交易确认单解析 - 统一入口函数
    """
    try:
        # 默认返回格式
        default_result = {
            "交易信息": {
                "交易类型": "/",
                "交易金额": "/",
                "交易日期": "/",
                "交易对手": "/"
            },
            "产品信息": {
                "产品名称": "/",
                "产品代码": "/"
            },
            "确认信息": {
                "确认状态": "/",
                "确认日期": "/",
                "确认机构": "/"
            }
        }
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return default_result
        
        # 调用实际的解析函数
        result = parse_transaction_document(file_path, save_result=False)
        
        if result and result.get('success'):
            parsed_result = result.get('parsed_result')
            if parsed_result:
                # 如果解析成功，返回解析结果
                if isinstance(parsed_result, dict):
                    return {**default_result, **parsed_result}
                elif isinstance(parsed_result, list) and len(parsed_result) > 0:
                    # 如果是列表，返回第一个元素
                    return {**default_result, **parsed_result[0]}
                else:
                    return default_result
            else:
                return default_result
        else:
            error_msg = result.get('error', '解析失败') if result else '解析函数返回空结果'
            print(f"非标交易确认单解析失败: {error_msg}")
            return default_result
            
    except Exception as e:
        print(f"非标交易确认单解析失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


# ==================== 批量处理功能 ====================

def batch_process_documents(input_dir, output_dir="./output", supported_extensions=None, recursive=True):
    """
    批量处理文档（支持递归处理子目录）

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        supported_extensions: 支持的文件扩展名列表
        recursive: 是否递归处理子目录

    Returns:
        dict: 处理结果字典，包含详细信息和汇总数据
    """
    if supported_extensions is None:
        supported_extensions = ['.pdf', '.xlsx', '.xls', '.jpg', '.jpeg', '.png', '.docx', '.bmp', '.gif', '.tiff', '.webp', '.eml']

    results = []
    all_transactions = []  # 汇总所有交易数据

    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在 - {input_dir}")
        return {'success': False, 'error': f'目录不存在: {input_dir}'}

    # 获取所有支持的文件（递归或非递归）
    files_to_process = []
    if recursive:
        # 递归遍历所有子目录
        for root, dirs, files in os.walk(input_dir):
            for filename in files:
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_extensions:
                    files_to_process.append(os.path.join(root, filename))
    else:
        # 只处理当前目录
        for filename in os.listdir(input_dir):
            if os.path.isfile(os.path.join(input_dir, filename)):
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_extensions:
                    files_to_process.append(os.path.join(input_dir, filename))

    print(f"找到 {len(files_to_process)} 个文件需要处理")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 逐个处理文件
    for i, file_path in enumerate(files_to_process, 1):
        print(f"\n处理第 {i}/{len(files_to_process)} 个文件: {os.path.basename(file_path)}")

        try:
            result = parse_transaction_document(file_path, save_result=True, output_dir=output_dir)

            # 添加文件相对路径信息
            if result:
                result['relative_path'] = os.path.relpath(file_path, input_dir)
                result['file_name'] = os.path.basename(file_path)

            results.append(result)

            if result and result.get('success'):
                print(f"✓ 处理成功")

                # 收集交易数据用于汇总
                parsed_data = result.get('parsed_result')
                if parsed_data:
                    if isinstance(parsed_data, list):
                        for transaction in parsed_data:
                            transaction['源文件'] = os.path.basename(file_path)
                            transaction['文件路径'] = result['relative_path']
                            all_transactions.append(transaction)
                    else:
                        parsed_data['源文件'] = os.path.basename(file_path)
                        parsed_data['文件路径'] = result['relative_path']
                        all_transactions.append(parsed_data)
            else:
                print(f"✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}")

        except Exception as e:
            print(f"✗ 处理异常: {e}")
            results.append({
                'success': False,
                'file_path': file_path,
                'relative_path': os.path.relpath(file_path, input_dir),
                'file_name': os.path.basename(file_path),
                'error': str(e)
            })

    # 生成统计信息
    successful_count = sum(1 for r in results if r and r.get('success'))
    failed_count = len(files_to_process) - successful_count

    print(f"\n批量处理完成: {successful_count}/{len(files_to_process)} 个文件处理成功")
    print(f"成功解析的交易记录总数: {len(all_transactions)}")

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存详细批量处理报告
    report_file = os.path.join(output_dir, f"batch_report_{timestamp}.json")
    batch_report = {
        'timestamp': timestamp,
        'input_dir': input_dir,
        'output_dir': output_dir,
        'total_files': len(files_to_process),
        'successful_files': successful_count,
        'failed_files': failed_count,
        'total_transactions': len(all_transactions),
        'processing_details': results
    }

    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(batch_report, f, ensure_ascii=False, indent=2)

    # 保存交易数据汇总
    summary_file = os.path.join(output_dir, f"transactions_summary_{timestamp}.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(all_transactions, f, ensure_ascii=False, indent=2)

    print(f"批量处理报告已保存到: {report_file}")
    print(f"交易数据汇总已保存到: {summary_file}")

    return {
        'success': True,
        'timestamp': timestamp,
        'report_file': report_file,
        'summary_file': summary_file,
        'batch_report': batch_report,
        'all_transactions': all_transactions,
        'results': results
    }


# ==================== 结果对比功能 ====================

def compare_batch_results(summary_file1, summary_file2, output_dir="./output"):
    """
    对比两次批量处理的结果

    Args:
        summary_file1: 第一次处理的汇总文件路径
        summary_file2: 第二次处理的汇总文件路径
        output_dir: 输出目录

    Returns:
        dict: 对比结果
    """
    try:
        # 读取两个汇总文件
        with open(summary_file1, 'r', encoding='utf-8') as f:
            data1 = json.load(f)

        with open(summary_file2, 'r', encoding='utf-8') as f:
            data2 = json.load(f)

        # 创建文件名到交易数据的映射
        def create_file_mapping(data):
            mapping = {}
            for transaction in data:
                file_name = transaction.get('源文件', 'unknown')
                if file_name not in mapping:
                    mapping[file_name] = []
                mapping[file_name].append(transaction)
            return mapping

        mapping1 = create_file_mapping(data1)
        mapping2 = create_file_mapping(data2)

        # 对比分析
        all_files = set(mapping1.keys()) | set(mapping2.keys())

        comparison_result = {
            'summary': {
                'first_batch': {
                    'file': summary_file1,
                    'total_transactions': len(data1),
                    'total_files': len(mapping1)
                },
                'second_batch': {
                    'file': summary_file2,
                    'total_transactions': len(data2),
                    'total_files': len(mapping2)
                },
                'differences': {
                    'transaction_count_diff': len(data2) - len(data1),
                    'file_count_diff': len(mapping2) - len(mapping1)
                }
            },
            'file_level_comparison': {},
            'new_files': [],
            'removed_files': [],
            'modified_files': []
        }

        # 文件级别对比
        for file_name in all_files:
            transactions1 = mapping1.get(file_name, [])
            transactions2 = mapping2.get(file_name, [])

            if not transactions1 and transactions2:
                # 新增文件
                comparison_result['new_files'].append({
                    'file_name': file_name,
                    'transaction_count': len(transactions2)
                })
            elif transactions1 and not transactions2:
                # 删除文件
                comparison_result['removed_files'].append({
                    'file_name': file_name,
                    'transaction_count': len(transactions1)
                })
            elif transactions1 and transactions2:
                # 对比交易数据
                if len(transactions1) != len(transactions2):
                    comparison_result['modified_files'].append({
                        'file_name': file_name,
                        'first_batch_count': len(transactions1),
                        'second_batch_count': len(transactions2),
                        'difference': len(transactions2) - len(transactions1)
                    })

                # 详细对比（简化版，只对比数量和基本信息）
                comparison_result['file_level_comparison'][file_name] = {
                    'first_batch_transactions': len(transactions1),
                    'second_batch_transactions': len(transactions2),
                    'difference': len(transactions2) - len(transactions1)
                }

        # 保存对比结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_file = os.path.join(output_dir, f"batch_comparison_{timestamp}.json")

        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_result, f, ensure_ascii=False, indent=2)

        print(f"\n=== 批量处理结果对比 ===")
        print(f"第一次处理: {len(data1)} 笔交易，{len(mapping1)} 个文件")
        print(f"第二次处理: {len(data2)} 笔交易，{len(mapping2)} 个文件")
        print(f"交易数量变化: {len(data2) - len(data1):+d}")
        print(f"文件数量变化: {len(mapping2) - len(mapping1):+d}")

        if comparison_result['new_files']:
            print(f"\n新增文件 ({len(comparison_result['new_files'])} 个):")
            for file_info in comparison_result['new_files'][:5]:  # 只显示前5个
                print(f"  + {file_info['file_name']} ({file_info['transaction_count']} 笔交易)")
            if len(comparison_result['new_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['new_files']) - 5} 个新增文件")

        if comparison_result['removed_files']:
            print(f"\n删除文件 ({len(comparison_result['removed_files'])} 个):")
            for file_info in comparison_result['removed_files'][:5]:
                print(f"  - {file_info['file_name']} ({file_info['transaction_count']} 笔交易)")
            if len(comparison_result['removed_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['removed_files']) - 5} 个删除文件")

        if comparison_result['modified_files']:
            print(f"\n修改文件 ({len(comparison_result['modified_files'])} 个):")
            for file_info in comparison_result['modified_files'][:5]:
                print(f"  ~ {file_info['file_name']} ({file_info['first_batch_count']} -> {file_info['second_batch_count']})")
            if len(comparison_result['modified_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['modified_files']) - 5} 个修改文件")

        print(f"\n对比结果已保存到: {comparison_file}")

        return {
            'success': True,
            'comparison_file': comparison_file,
            'comparison_result': comparison_result
        }

    except Exception as e:
        print(f"对比过程中发生错误: {e}")
        return {
            'success': False,
            'error': str(e)
        }


# ==================== Excel导出功能 ====================

def export_summary_to_excel(summary_file, output_dir="./output"):
    """
    将汇总JSON文件导出为Excel
    """
    if not os.path.exists(summary_file):
        print(f"汇总文件不存在: {summary_file}")
        return None

    try:
        # 读取JSON文件
        with open(summary_file, 'r', encoding='utf-8') as f:
            all_transactions = json.load(f)

        if not all_transactions:
            print("汇总文件中没有交易记录")
            return None

        # 生成Excel文件名
        base_name = os.path.splitext(os.path.basename(summary_file))[0]
        excel_file = os.path.join(output_dir, f"{base_name}.xlsx")

        # 创建DataFrame
        df = pd.DataFrame(all_transactions)

        # 重新排列列的顺序
        columns_order = ['源文件', '文件路径', '投资者名称', '投资者账号', '业务日期', '业务类型',
                        '投资标的名称', '投资标的代码', '投资标的金额', '投资标的数量', '交易费用']

        # 只保留存在的列
        existing_columns = [col for col in columns_order if col in df.columns]
        # 添加其他存在但不在预定义列表中的列
        other_columns = [col for col in df.columns if col not in existing_columns]
        final_columns = existing_columns + other_columns

        df = df[final_columns]

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 导出Excel
        df.to_excel(excel_file, index=False)

        print(f"汇总数据已导出到Excel文件: {excel_file}")
        print(f"共导出 {len(all_transactions)} 笔交易记录")
        print(f"包含列: {', '.join(final_columns)}")

        return excel_file

    except Exception as e:
        print(f"导出Excel时发生错误: {e}")
        return None


# ==================== 依赖检查和安装 ====================

def check_and_install_dependencies():
    """
    检查和安装依赖库
    """
    import subprocess
    import sys

    required_packages = ['tabulate', 'openpyxl', 'xlrd', 'python-docx', 'PyPDF2', 'Pillow']

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装完成")


def future_account_analysis(img_path):
    """
    期货账户分析（对应期货交易会员解析v1.3）
    """
    try:
        # 标准化返回格式的默认值
        default_result = {
            "产品名称": "/",
            "资金账号": "/",
            "会员号": {
                "上期所": "/",
                "大商所": "/",
                "郑商所": "/",
                "中金所": "/",
                "上能所": "/",
                "广期所": "/"
            },
            "交易编码": {
                "上期所": {"投机": "/", "套利": "/", "套保": "/"},
                "大商所": {"投机": "/", "套利": "/", "套保": "/"},
                "郑商所": {"投机": "/", "套利": "/", "套保": "/"},
                "中金所": {"投机": "/", "套利": "/", "套保": "/"},
                "上能所": {"投机": "/", "套利": "/", "套保": "/"},
                "广期所": {"投机": "/", "套利": "/", "套保": "/"}
            },
            "开始时间": "/",
            "结束时间": "/"
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('futures_account', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的期货交易会员解析v1.3.ipynb中的prompt
            img_prompt = """你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或产品的正式名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者函件落款日，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户"""
        
        # 判断文件类型并处理
        file_ext = img_path.lower()
        is_image = any(file_ext.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'])
        
        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值，确保结构完整
                    for key in default_result:
                        if key not in result:
                            result[key] = default_result[key]
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"期货账户图片分析失败: {e}")
                return default_result
        else:
            # PDF文件处理
            try:
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result
                
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一名期货开户文件解析专家。请根据用户提供的期货开户/备案文件内容，提取相关信息并按JSON格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
                )
                
                response = chatbot.chat(markdown_content + "\n\n请按照期货账户分析的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值，确保结构完整
                    for key in default_result:
                        if key not in result:
                            result[key] = default_result[key]
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"期货账户PDF分析失败: {e}")
                return default_result
                
    except Exception as e:
        print(f"期货账户分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def broker_interest_change_analysis(img_path):
    """
    券商账户计息变更分析（对应券商账户计息变更v1.3）
    """
    try:
        # 默认返回格式
        default_result = [
            {
                "产品名称": "/",
                "产品类别": "单产品",
                "利率(年化)": {"all": "/"},
                "开始时间": "/",
                "截止时间": "/",
                "计息天数": "/",
                "备注": "/"
            }
        ]
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('broker_interest', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的券商账户计息变更v1.3.ipynb中的prompt
            img_prompt = """请从图片中提取券商账户计息变更的相关信息，严格按照以下格式输出JSON数组：

=====================【必须提取的字段】=====================
1. **产品名称**：具体的产品或资产名称
2. **产品类别**：区分是"单产品"还是"全公司产品"等
3. **利率(年化)**：
   • 如果是统一利率，格式为 `{"all": "X.XX%"}`
   • 如果按客户类型分段，格式为 `{"个人": "X.XX%", "非个人": "X.XX%"}`
   • 如果按时间分段，格式为 `{"START:YYYY-MM-DD": "X.XX%", "YYYY-MM-DD:END": "X.XX%"}`
4. **开始时间**：变更生效的开始日期（YYYY-MM-DD格式）
5. **截止时间**：变更的截止日期，如无明确截止则填""  
6. **计息天数**：年化计息的天数基础（如360天、365天等）
7. **备注**：其他重要信息

=====================【输出JSON数组示例】=====================
```json
[
  {
    "产品名称": "汇添富远景成长一年持有期混合型基金",
    "产品类别": "单产品",
    "利率(年化)": {"all": "1.4%"},
    "开始时间": "2025-01-02",
    "截止时间": "",
    "计息天数": 360,
    "备注": "按月20日前结息至期货账户"
  }
]
```
"""

        # 判断文件类型并处理
        file_ext = img_path.lower()
        is_image = any(file_ext.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'])
        
        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, list) and len(result) > 0:
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"券商计息变更图片分析失败: {e}")
                return default_result
        else:
            # PDF文件处理
            try:
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result
                
                chatbot_system_prompt = """你是一位资深的银行托管部经理，负责从客户提供的文件中抽取基金计息变更要素，并按JSON数组格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
                
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt=chatbot_system_prompt
                )
                
                response = chatbot.chat(markdown_content + "\n\n请按照券商账户计息变更的标准格式输出JSON数组结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, list) and len(result) > 0:
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"券商计息变更PDF分析失败: {e}")
                return default_result
                
    except Exception as e:
        print(f"券商账户计息变更分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}



def ningyin_fee_change_analysis(img_path):
    """
    宁银理财费用变更分析（对应宁银理财费用变更样例）
    """
    try:
        # 默认返回格式
        default_result = {
            "产品信息": {
                "产品名称": "/",
                "产品代码": "/"
            },
            "费用变更": [
                {
                    "费用类型": "/",
                    "变更前费率": "/",
                    "变更后费率": "/",
                    "变更幅度": "/"
                }
            ],
            "变更信息": {
                "生效日期": "/",
                "公告日期": "/",
                "变更原因": "/",
                "公告编号": "/"
            }
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 判断文件类型
        file_ext = img_path.lower()
        image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        is_image = any(file_ext.endswith(ext) for ext in image_formats)
        is_pdf = file_ext.endswith('.pdf')

        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('ningxia_bank_fee', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的宁银理财费用变更样例中的prompt
            img_prompt = """请从图片中提取宁银理财费用变更的相关信息，严格按照JSON格式输出：

请以JSON格式输出，格式如下：
```json
{
  "产品信息": {
    "产品名称": "产品名称",
    "产品代码": "产品代码"
  },
  "费用变更": [
    {
      "费用类型": "管理费",
      "变更前费率": "0.50%",
      "变更后费率": "0.30%",
      "变更幅度": "下调0.20%"
    }
  ],
  "变更信息": {
    "生效日期": "2024-01-01",
    "公告日期": "2023-12-15",
    "变更原因": "优惠活动",
    "公告编号": "NBYC2023001"
  }
}
```
"""

        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"宁银费用变更图片分析失败: {e}")
                return default_result
        elif is_pdf:
            # PDF文件处理
            try:
                # 使用process_file_to_markdown进行PDF转换
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result

                # 使用ChatBot进行markdown内容分析
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一个宁银理财费用变更分析专家，请根据用户提供的费用变更公告内容，提取相关信息。
                    请注意，输出格式必须为json格式，不要输出其他内容。

                    # 示例输出(仅供参考，请根据实际情况输出)
                    ```json
                    {
                      "产品信息": {
                        "产品名称": "宁银理财XX产品",
                        "产品代码": "NBYC001"
                      },
                      "费用变更": [
                        {
                          "费用类型": "管理费",
                          "变更前费率": "0.50%",
                          "变更后费率": "0.30%",
                          "变更幅度": "下调0.20%"
                        }
                      ],
                      "变更信息": {
                        "生效日期": "2024-01-01",
                        "公告日期": "2023-12-15",
                        "变更原因": "优惠活动",
                        "公告编号": "NBYC2023001"
                      }
                    }
                    ```
                    """
                )
                response = chatbot.chat(markdown_content + "\n\n请按照宁银理财费用变更的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
                    
            except Exception as e:
                print(f"宁银费用变更PDF分析失败: {e}")
                return default_result
        else:
            print(f"不支持的文件格式: {file_ext}")
            return default_result
            
    except Exception as e:
        print(f"宁银理财费用变更分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def account_opening_analysis(img_path):
    """
    账户开户场景解析
    """
    try:
        # 默认返回格式
        default_result = {
            "客户姓名": "/",
            "账户类型": "/",
            "账户号码": "/",
            "开户日期": "/",
            "开户网点": "/",
            "身份证号": "/",
            "联系电话": "/",
            "地址": "/",
            "初始存款": "/",
            "业务类型": "/"
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('account_opening', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的账户开户场景解析中的prompt
            img_prompt = """你是一名银行账户开户文件解析专家。请从用户提供的账户开户相关文件中提取信息，并用 JSON 返回。

【必须提取的字段】
1. 客户姓名：开户客户的姓名或机构名称
2. 账户类型：账户的类型（如储蓄账户、支票账户、企业账户等）
3. 账户号码：新开立的账户号码
4. 开户日期：账户开立的日期（YYYY-MM-DD格式；缺失填"/"）
5. 开户网点：办理开户业务的银行网点名称
6. 身份证号：客户的身份证号码或统一社会信用代码
7. 联系电话：客户的联系电话（缺失填"/"）
8. 地址：客户的联系地址（缺失填"/"）
9. 初始存款：开户时的初始存款金额（缺失填"/"）
10. 业务类型：开户业务的具体类型（如个人储蓄、企业基本户等）

```json
{
  "客户姓名": "张三",
  "账户类型": "储蓄账户",
  "账户号码": "1234567890123456",
  "开户日期": "2024-01-15",
  "开户网点": "XX银行XX支行",
  "身份证号": "123456789012345678",
  "联系电话": "13800138000",
  "地址": "XX省XX市XX区XX街道XX号",
  "初始存款": "1000.00",
  "业务类型": "个人储蓄开户"
}
```
"""

        # 判断文件类型并处理
        file_ext = img_path.lower()
        is_image = any(file_ext.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'])
        
        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"账户开户图片分析失败: {e}")
                return default_result
        else:
            # PDF文件处理
            try:
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result
                
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一名银行账户开户文件解析专家。请根据用户提供的开户文件内容，提取相关信息并按JSON格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
                )
                
                response = chatbot.chat(markdown_content + "\n\n请按照账户开户场景的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"账户开户PDF分析失败: {e}")
                return default_result
                
    except Exception as e:
        print(f"账户开户场景分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def financial_product_analysis(img_path):
    """
    理财产品说明书分析（对应理财产品说明书v1.3）
    """
    try:
        # 默认返回格式
        default_result = {
            "销售机构": ["/"]
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 判断文件类型
        file_ext = img_path.lower()
        image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        is_image = any(file_ext.endswith(ext) for ext in image_formats)
        is_pdf = file_ext.endswith('.pdf')

        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('wealth_management', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的理财产品说明书v1.3中的prompt
            img_prompt = """请从图片中提取理财产品说明书的销售机构信息，严格按照JSON格式输出：

请注意，输出格式必须为json格式，不要输出其他内容。

# 示例输出(仅供参考，请根据实际情况输出)
```json
{
  "销售机构": [
    "宁波银行股份有限公司",
    "交通银行股份有限公司"
  ]
}
```
"""

        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"理财产品图片分析失败: {e}")
                return default_result
        
        elif is_pdf:
            # PDF文件处理
            try:
                # 使用process_file_to_markdown进行PDF转换
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result

                # 相关文档检索 - 查找销售机构信息
                father_docs = markdown_content.split("\n\n")
                refer_docs = []
                keywords = ["销售", "代销", "代理销售"]
                keywords2 = ["公司", "银行"]
                for father_doc in father_docs:
                    is_match = False
                    for keyword in keywords:
                        if keyword in father_doc:
                            for keyword2 in keywords2:
                                if keyword2 in father_doc:
                                    refer_docs.append(father_doc)
                                    is_match = True
                                    break
                    if is_match:
                        break
                
                refer_doc_text = "\n".join(refer_docs) if refer_docs else markdown_content[:2000]  # 如果没找到相关文档，使用前2000字符

                # 使用ChatBot进行markdown内容分析
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。
                    注意：请勿捏造数据，请根据实际情况输出。
                    请注意，输出格式必须为json格式，不要输出其他内容。

                    # 示例输出(仅供参考，请根据实际情况输出)
                    ```json
                    {
                      "销售机构": [
                        "XX银行股份有限公司"
                      ]
                    }
                    ```
                    """
                )
                response = chatbot.chat(refer_doc_text + "\n\n请按照理财产品说明书的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    return {**default_result, **result}
                else:
                    return default_result
                    
            except Exception as e:
                print(f"理财产品PDF分析失败: {e}")
                return default_result
        else:
            print(f"不支持的文件格式: {file_ext}")
            return default_result
            
    except Exception as e:
        print(f"理财产品说明书分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def financial_product_manual_analysis(img_path):
    """
    理财产品说明书分析
    """
    # 判断文件类型
    file_ext = img_path.lower()
    
    # 支持的图片格式
    image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
    is_image = any(file_ext.endswith(ext) for ext in image_formats)
    is_pdf = file_ext.endswith('.pdf')
    
    if not (is_image or is_pdf):
        raise ValueError(f"不支持的文件格式，仅支持PDF和图片格式 {image_formats}")
    
    # 如果是图片文件，先转换为PDF
    if is_image:
        # 生成临时PDF文件路径
        import tempfile
        temp_dir = os.path.dirname(img_path)
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        pdf_path = os.path.join(temp_dir, f"temp_{base_name}.pdf")
        
        # 将图片转换为PDF
        image_to_pdf(img_path, pdf_path)
        
        # 删除临时PDF的标志
        should_delete_pdf = True
    else:
        # 直接使用PDF文件
        pdf_path = img_path
        should_delete_pdf = False
    
    try:
        # 生成markdown文件路径
        markdown_path = pdf_path.replace('.pdf', '.md')
        
        # PDF转markdown
        pdf_to_markdown(pdf_path, markdown_path)
        
        # 读取markdown内容
        with open(markdown_path, "r", encoding='utf-8') as f:
            markdown_content = f.read()
        
        # 删除临时markdown文件
        if os.path.exists(markdown_path):
            os.remove(markdown_path)

        father_docs = markdown_content.split("\n\n")
        # 相关文档检索
        refer_docs = []
        keywords = ["销售", "代销", "代理销售"]
        keywords2 = ["公司", "银行"]
        for father_doc in father_docs:
            is_match = False
            for keyword in keywords:
                if keyword in father_doc:
                    for keyword2 in keywords2:
                        if keyword2 in father_doc:
                            refer_docs.append(father_doc)
                            is_match = True
                            break
            if is_match:
                break
        refer_doc_text = "\n".join(refer_docs)
        
        # 使用ChatBot进行markdown内容分析
        chatbot = ChatBot(
            model='qwen3-32b',
            system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，回答用户的问题。
            请注意，输出格式必须为json格式，不要输出其他内容。

            # 示例输出(仅供参考，请根据实际情况输出)
            ```json
            {
              "sell_coms": [
                "宁波银行股份有限公司",
                "交通银行股份有限公司"
              ]
            }
            ```
            """
            )
        response = chatbot.chat(refer_doc_text + "\n/no_think")
        return markdown_to_json(response)
        
    finally:
        # 如果是从图片转换的临时PDF，需要删除
        if should_delete_pdf and os.path.exists(pdf_path):
            os.remove(pdf_path)
            print(f"已删除临时PDF文件: {pdf_path}")
from openai import OpenAI

class ChatBot:
    def __init__(self, model="qwen3-32b", system_prompt=""):
        self.client = OpenAI(
            api_key="sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553",
            base_url="http://192.168.10.236:23000/v1"
            )
        self.model = model
        self.history = []
        if system_prompt != "":
            self.history.append({"role": "system", "content": system_prompt})

    def chat(self, messages, temperature=0.8, max_tokens=8192, top_p=0.8) -> str:
        """
        与大模型对话
        
        Args:
            messages: list[dict]
            temperature: float
            max_tokens: int
            top_p: float

        Returns:
            str: 大模型的回答
        """
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        elif isinstance(messages, list):
            if not isinstance(messages[0], dict):
                messages = [{"role": "user", "content": messages}]
        else:
            raise ValueError("messages must be a string or a list of dict")
        response = self.client.chat.completions.create(
            model=self.model,
            messages=self.history + messages,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p)
        return response.choices[0].message.content
    
    def chat_with_img(self, messages, img_url=None, temperature=0.8, max_tokens=8192, top_p=0.8) -> str:
        """
        与大模型进行包含图片的对话
        
        Args:
            messages: str or list[dict] - 文本消息
            img_url: str - 图片URL或base64编码
            temperature: float
            max_tokens: int
            top_p: float

        Returns:
            str: 大模型的回答
        """
        if isinstance(messages, str):
            content = [{"type": "text", "text": messages}]
            if img_url:
                # 处理img_url可能为列表的情况
                if isinstance(img_url, list):
                    # 如果是列表，取第一个元素
                    img_url = img_url[0] if img_url else None
                
                if img_url and isinstance(img_url, str):
                    # 如果是base64编码，需要添加前缀
                    if img_url.startswith('data:image'):
                        image_url = img_url
                    else:
                        # 假设是base64编码
                        image_url = f"data:image/jpeg;base64,{img_url}"
                    content.append({"type": "image_url", "image_url": {"url": image_url}})
            
            messages = [{"role": "user", "content": content}]
        elif isinstance(messages, list):
            if not isinstance(messages[0], dict):
                messages = [{"role": "user", "content": messages}]
        else:
            raise ValueError("messages must be a string or a list of dict")
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=self.history + messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p)
            return response.choices[0].message.content
        except Exception as e:
            print(f"图片对话失败: {e}")
            # 如果图片对话失败，尝试只用文本
            if isinstance(messages, str):
                return self.chat(messages, temperature, max_tokens, top_p)
            else:
                # 提取文本部分
                text_content = ""
                for msg in messages:
                    if isinstance(msg.get('content'), list):
                        for item in msg['content']:
                            if item.get('type') == 'text':
                                text_content += item.get('text', '')
                    elif isinstance(msg.get('content'), str):
                        text_content += msg['content']
                return self.chat(text_content, temperature, max_tokens, top_p) if text_content else "图片处理失败"
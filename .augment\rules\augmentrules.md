---
type: "manual"
---

1. 历史数据保护原则
- 任何修改必须保证历史数据不被删除
- 所有数据库变更必须提供迁移方案
- 添加新字段时必须设置默认值并兼容旧数据

2. 沟通规则
- 回复必须使用中文
- 尊重用户需求，提供可执行的解决方案

3. 代码规范
- 不得删除代码中的注释
- 代码修改应保持一致的风格和缩进
- 所有新功能必须向后兼容

4. 依赖管理
- 有新的python依赖包需同步更新到requirements.txt
- 注明版本号，确保环境一致性

5. 测试应用及启动应用自动执行，无需手动点击

6. python 依赖包管理
安装包，会有网络问题下载不到，用这个方式可以完成：
pip install -r requirements.txt \
  --trusted-host pypi.tuna.tsinghua.edu.cn \
  --trusted-host pypi.org \
  --trusted-host files.pythonhosted.org

7. 如果制作docker镜像，不需要帮我 build ，如果我有这样的要求，请用这个方式：
  - docker build --platform linux/amd64 -t {镜像名称:版本号} .
  
  
8. 如果需求设计到前端页面，那么需要考虑如下的设计。
  为一个高端金融量化交易平台制作前端视觉效果图，要求整体风格兼具科技感与金融感，呈现专业、稳健、现代化且富有未来感的质感。  
  a. 色彩与材质  
   - 主色调：深邃钢铁蓝（#0A1F44）、午夜灰（#121925），辅以金属银（#C0C0C0）与闪耀金（#FFD700）点缀；  
   - 背景质感：轻微网格或激光切割纹理，辅以半透明光晕与微粒渐变，营造暗夜科技氛围；  
  b. 布局与结构  
   - 采用大气的模块化卡片布局，卡片边缘内凹或玻璃质感（Glassmorphism）；  
   - 顶部保留简洁导航栏，左侧LOGO + 平台名称，右侧用户头像与快捷操作；  
   - 中央主视区：大型 3D 折线／蜡烛图仪表盘，辅以实时数值面板；底部或侧边可放策略列表、风险监控、告警中心等卡片；  
  c. 排版与字体  
   - 字体：无衬线字体（如 Inter、Roboto），标题加粗、大号（24–32px），正文字号（14–16px）简洁易读；  
   - 文本颜色：主文字米白（#F5F5F5），次级信息浅灰（#A0A0A0），高亮数据使用荧光蓝或金色；  
  d. 交互动效  
   - 图表 hover 时平滑高亮轨迹，并在靠近数据点时渐显浮窗（Tooltip）；  
   - 卡片加载时使用轻微弹簧或淡入动画；按钮与图标的点击反馈以 100–200ms 的缩放或色块渐变为主；  
   - 粒子流或线性光束在背景缓慢移动，增强未来科技氛围；  
  e. 组件与细节  
   - 自定义图表组件：支持深色主题，带发光边框、刻度渐变线；  
   - 风险监控模块：动态仪表盘（Gauge），实时波动采用“扫光”效果；  
   - 操作按钮：扁平化、带微弱光晕，悬浮时外发射柔和阴影；  
  f. 适配与响应  
   - 响应式设计，关键仪表盘与主导航在移动端收合为抽屉式侧栏；  
   - 保证 1920×1080 及以上分辨率下画面细节清晰，兼容 Retina／4K 显示；  
  g. 整体氛围  
   - 视觉重心稳健、科技感强烈，兼顾金融专业与未来主义；  
   - 页面整体保持留白，突出核心数据与交互区，让用户一目了然，感受到“掌控全局”的专业力量。

9. 如果项目和大模型有关系，需要把大模型的配置文件放出来。
  - 包含：key、url、model_name  等。
  - 如果需要使用大模型，请使用openai的sdk。
  - 还需要做一个模型没有调用模型的模拟效果。

10.自动执行run Terminal



   
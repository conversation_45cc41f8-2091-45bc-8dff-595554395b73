{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from mineru_pdf import *\n", "from util_ai import ChatBot\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["oragin_fn = \"/app/宁波银行POC/test_data/大模型样例/理财产品说明书/渝农商理财产品说明书.md\"\n", "if oragin_fn.lower().endswith('.pdf'):\n", "    out_pdf_fn = oragin_fn\n", "else:\n", "    # 将图片转为pdf\n", "    fn_end = oragin_fn.split('.')[-1]  \n", "    fix_fn = \".\".join(oragin_fn.split('.')[:-1]) + \".pdf\"\n", "    out_pdf_fn = convert_images_to_pdf(oragin_fn, fix_fn)\n", "\n", "markdown_data = trans_pdf_to_markdown(out_pdf_fn)\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["father_docs = markdown_content.split(\"\\n\\n\")\n", "son_docs = {}\n", "for father_doc in father_docs:\n", "    son_docs[father_doc] = father_doc.split(\"\\n\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 相关文档检索\n", "refer_docs = []\n", "keywords = [\"销售\", \"代销\", \"代理销售\"]\n", "keywords2 = [\"公司\", \"银行\"]\n", "for father_doc in father_docs:\n", "    is_match = False\n", "    for keyword in keywords:\n", "        if keyword in father_doc:\n", "            for keyword2 in keywords2:\n", "                if keyword2 in father_doc:\n", "                    refer_docs.append(father_doc)\n", "                    is_match = True\n", "                    break\n", "        if is_match:\n", "            break"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["refer_doc_text = \"\\n\".join(refer_docs)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["['1）管理人/理财产品管理人：指广银理财有限责任公司。  \\n2）托管人/理财产品托管人：指宁波银行股份有限公司。  \\n3）销售机构：包括直销机构及代销机构  \\n直销机构：广银理财有限责任公司  \\n代销机构：指与理财产品管理人签订相关协议，提供包括理财产品宣传推广、投资者风险承受能力评估和投资者适当性管理、理财产品份额认购/申购/回、协助管理人与投资者订立理财产品合同、协助管理人与投资者沟通及进行信息披露、接受投资省咨询和客户维护等销售服务的机构。  \\n4）理财产品份额持有人/持有人：指其认购/申购申请，经过管理人确认认购/申购成功，从面持有理财产品份额的投资者。5）合格投资者：根据《关于规范金融机构资产管理业务的指导意见》（银发[2018]106号）、《商业银行理财业务监督管理办法（银保监会令[2018]6号）规定，合格投资者是指具备相应风险识别能力和风险承受能力，投资于单只理财产品不低于定金额且符合下列条件的自然人、法人或者依法成立的其他组织：（一）具有2年以上投资经历，且满足家庭金融净资产不低于300万元人民币，或者家庭金融资产不低于500万元人民币，或者近3年本人年均收入不低于40万元人民币：（二）最近1年末净资产不低于1000万元人民币的法人或者依法成立的其他组织：（三）国务院银行业监督管理机构规定的其他情形。6）投资台作机构：包括但不限于理财产品所投资资产管理产品的发行机构、根据合同约定从事理财产品受托投资的机构以及与理财产品投资管理相关的投资顾问等：主要职责为进行受托资金的投资管理，根据合同约定从事受托投资或提供投资顾问等服务，具体职责以管理人与投资合作机构签署的合同为准。本理财产品投资合作机构依据监管要求进行名单制管理，合作机构按照管理人相关制度流程选任，符合准入标准和程序。  \\n7）金融监管总局/银保监会：均指国家金融监督管理总局（暨原中国银行保险监督管理委员会）。  \\n8）监管机构：指对本理财产品的理财产品管理人、所投资的信托计划/资管计划的管理人/受托人，相关投资顾间（如有）等实施监督管理的机构，包括但不限于中国人民银行、金融监管总局、中国证券监督管理委员会、外汇管理局、银行业理财登记托管中心及其派出机构等。  \\n2.法律文件用语  \\n1）《产品说明书》：指《广银理财安鑫封闭式私募理财产品宁远71号产品说明书》，以及对该文件的有效修改或补充。2）《风险揭示书》：指作为广银理财安鑫封闭式私募理财产品宁远71号销售文件不可分割组成部分的《风险揭示书》，以及对该文件的有效修改或补充。  \\n3）理财产品投资协议书》：指由投资者和管理人签署的，作为广银理财安鑫封闭式私募理财产品宁远71号销售文件不可分割组成部分的《理财产品投资协议书》，以及对该文件的有效修改或补充。  \\n4）《销售（代理销售）协议书》：指由投资者和销售机构签署的，作为广银理财安鑫封闭式私募理财产品宁远71号销售文件不可分割组成部分的《销售（代理销售）协议书》，以及对该文件的有效修改或补充。  \\n5）《投资者权益须知》：指作为广银理财安鑫封闭式私募理财产品宁远71号销售文件不可分割组成部分的《投资者权益须知》，以及对该文件的有效修改或补充。  \\n6）理财产品合同：指《产品说明书》、《风险揭示书》和《理财产品投资协议书》的总称。  \\n7）销售文件：指《产品说明书》、《风险揭示书》、《理财产品投资协议书》以及《销售（代理销售）协议书》和《投资者权益须知》。  \\n3.理财产品用语  \\n1）理财产品产品：指广银理财作为管理人发行的，本《产品说明书》“三、理财产品要素”载明的理财产品。  \\n2）理财产品资金：指理财产品募集资金，以及管理人管理、运用、处分该等理财产品募集资金而取得且归于理财产品所有的货币资金  \\n3）理财产品费用：指管理人为成立理财产品及处理理财产品事务目的而支出的所有费用，包括但不限于销售费、托管费、固定管理费、浮动管理费、资金汇划费、结算费、交易费用（包括但不限于交易佣金、合费用等）、理财产品验资费、审计费、律师费、信息披露费、清算费、执行费用等相关费用，具体以实际发生为准。',\n", " '7）理财产品收益：指投资者投资理财产品获得的投资收益，该收益为其获得分配的全部理财产品利益中扣除投资本金的部分8）理财产品单位份额净值：指理财产品份额的单位净值，即每1份理财产品份额以人民币计价的价格，保留至小数点后第四位，四位以后四舍五入。除销售文件或相关信息披露文件另有约定外，理财产品将按该单位份额净值进行份额认购，申购及赎回确认。如按照上述保留位数的份额净值对投资者的认购、申购或赎回确认可能引起理财产品份额净值较大波动的，或导致理财产品到期清算产生较大偏差的，为维护投资者利益，经管理人与托管人协商一致后，可以临时增加理财产品单位份额净值的保留位数并以此进行确认，具体保留位数以届时信息披露为准。  \\n9）理财产品单位份额累计净值：指理财产品份额的单位净值与产品成立后历次累计单位收益分配的总和。  \\n10）理财产品估值：指计算评估理财产品资产以确定理财产品份额单位净值的过程。  \\n11）业绩比较基准：业绩比较基准是管理人根据市场环境、产品性质、投资策略、过往表现等因素对产品设定的投资日标业绩比较基准不是预期收益率，不代表本产品的未来表现和实际收益，亦不构成管理人对本产品收益的承诺。业绩比较基准作为计算浮动管理费（如有）的依据。本产品为净值型产品，业绩表现将随市场波动，具有不确定性，投资者所能获得的最终收益以管理人实际支付的为准。管理人应根据市场研判、投资策略、产品性质等因素的变化，审慎决定调整业绩比较基准并进行信息披露。  \\n12）认购：指在理财产品的募集期内，投资者根据《产品说明书》约定申请购买理财产品份额的行为。  \\n13）申购/赎回：指在理财产品的开放期内，投资者向理财产品管理人提出申请对理财产品份额进行购买或卖出的行为。本产品不开放申购赎回。  \\n14）巨额赎回：开放式理财产品单个开放日净回申请（赎回申请份额总数-申购申请份额总数）超过前一日终理财产品总份额的10%的回行为，金融监管总局另有规定的除外。本产品不开放申购赎回。  \\n15）产品份额类别：指管理人有权根据不同客群销售安排情况、投资者购买理财产品的金额、适合的投资者，代销机构等因素，对投资者所持有的理财产品份额设置不同的理财产品份额类别，产品份额类别为本产品项下某一类产品份额。管理人有权新增或减少产品份额类别，并对不同类别的产品份额设定不同的投资起始金额、产品费率、单一客户持有上限等产品要素16）7个工作日可变现资产：指包括可在交易所、银行间市场正常交易的股票、债券、非金融企业债务融资工具、期货及期权合约以及同业存单，7个工作日内到期或可支取的买入返售、银行存款，7个工作日内能够确认收到的各类应收款项等。17）流动性受限资产：指由于法律法规、监管、合同或操作障碍等原因无法以合理价格予以变现的资产，包括到期日在10个交易日以上的逆回购与银行定期存款（含协议约定有条件提前支取的银行存款）、距可赎回日在10个交易日以上的资产管理产品、停牌股票、流通受限的新股及非公开发行股票、资产支持证券（票据），因发行人债务违约无法进行转让或交易的债券和非金融企业债务融资工具，以及其他流动性受限资产。  \\n4.相关账户用语  \\n托管账户：指理财产品管理人以理财产品的名义在托管人处单独开立的资金管理账户，理财产品资金的一切收支活动，均须通过该账户进行。  \\n5.期间与日期  \\n1）交易日及交易时间：自产品成立日（不含当日）起的每个上海证券交易所和深圳证券交易所的正常交易日为交易日，交易日的9:00至17:00为交易时间。  \\n2）工作日/银行工作日：指除中国法定节假日和公休日外的其他日。  \\n3）募集期：指理财产品成立前，理财产品管理人接受理财产品认购的时间。  \\n4理财产品成立日产品成立日：指达到《产品说明书》约定的成立条件后理财产品成立的日期。  \\n5）理财产品到期日/终止日：指理财产品实际终止之日，根据实际情况，是指理财产品预计到期日，或管理人根据本产品说明书》约定宣布本理财产品早于理财产品预计到期日而终止之日或宣布本理财产品延长后的终止之日（含延长后的到期终',\n", " '6）产品存续期：指自理财产品成立日起，至理财产品终止日的期间。  \\n7）估值日：本产品的估值日为每周四（如遇节假日则顺延至下一交易日）及法律法规规定需要对外披露单位份额净值信息的其他时间。  \\n8）开放日/期：提交申购赎回申请的日期。申购、赎回期内的申购、赎回申请，将以开放日的净值进行确认。本产品不开放申购赎回。  \\n9）中购、赎回确认日：对投资者申购回交易有效性进行确认的日期。本产品不开放申购赎回。  \\n10）清算期：自本理财产品终止日（不含）至投资者理财本金及收益到账日（含）之间为本理财产品的清算期，清算期原则上不得超过5个工作日，如管理人预计清算期超过5个工作日的，管理人将及时按照理财产品说明书中约定的方式向投资者进行信息披露。  \\n11）冷静期：指投资者在购买私募理财产品后，享有24小时的投资冷静期。在投资冷静期内，如果投资者改变决定，应立即在冷静期内向销售机构提出解除相应理财产品销售文件的申请，销售机构将遵从投资者意愿，解除已签订的销售文件，并在2个交易口内将投资者全部理财本金退还至投资者账户。投资冷静期自投资者签署销售文件后起算。  \\n12）节假日临时调整：如因国家相关主管部门临时调整节假日安排，导致产品原定开放日、估值日等日期安排发生变化的，原则上采用本产品说明书约定的方式对原定日期安排进行调整，如有特殊安排，以管理人届时的信息披露或通知为准。6.相关事件用语  \\n不可抗力：指理财产品各方不能预见、不能避免且不能克服的客观情况，该事件妨碍、影响或延误任何一方依相关理财产品文件履行其全部或部分义务。该事件包括但不限于：  \\na.地震、台风、海啸、洪水、火灾、停电、瘟疫：  \\nb.战争、政变、恐怖主义行动、骚乱、罢工：  \\nc.新的适用法律法规或国家政策的颁布或实施、对原适用法律法规或国家政策的修改：  \\nd.监管机构强制要求终止理财产品（该等要求不可归咎于任何一方）：  \\ne.因理财产品各方和/或其关联方运营网络系统遭受黑客攻击、电信部门技术调整或故障等原因而造成的理财产品各方和/或其关联方之服务、营业的中断或者延迟。  \\n7.其他  \\n$1 ) \\\\pi . =$ 指人民币元。  \\n2）法律法规/适用法律法规：指在理财产品文件签署日和履行过程中，中国任何立法机构、政府部门、银行间交易商协会、银行业理财登记托管中心、证券交易所、证券登记结算机构、相关行业协会等依法颁布的，适用于本理财产品相关事宜的法律、行政法规、地方法规、部委规章、地方政府规章、规范性文件自律规则、通知、须知、业务指南、指引或操作规程，及其有效的修改、更新或补充，但香港特别行政区、澳门特别行政区及台湾地区的法律法规除外。',\n", " '<html><body><table><tr><td>产品名称</td><td>广银理财安鑫封闭式私募理财产品宁远71号</td></tr><tr><td>产品编号</td><td>【AXFBNY071S】</td></tr><tr><td>产品份额类别</td><td>后续理财产品份额类别如有新增、减少和变更，管理人将在本产品说明书约定的信息披露渠道进行 披露。</td></tr><tr><td>产品简称</td><td>安鑫封闭宁远71号</td></tr><tr><td>产品登记编码</td><td>全国银行业理财信息登记系统登记编码为：【Z7006625A000012】 投资者可以根据该登记编码在中国理财网（网址：https://ww.chinawealh.com.cn）查询产品信息.</td></tr><tr><td>理财币种</td><td>人民币</td></tr><tr><td>产品类型</td><td>固定收益类、非保本浮动收益型</td></tr><tr><td>募集方式</td><td>私募</td></tr><tr><td>运作方式</td><td>封闭式净值型（自成立之日起【371】天为封闭期）</td></tr><tr><td>产品管理人</td><td>名称：广银理财有限责任公司</td></tr><tr><td rowspan=\"2\">产品托管人</td><td>住所：中国（上海）自由贸易试验区陆家嘴东路166号31层</td></tr><tr><td>名称：宁波银行股份有限公司</td></tr><tr><td rowspan=\"2\">产品销售机构</td><td>住所：浙江省宁波市鄞州区宁东路345号</td></tr><tr><td>名称：宁波银行股份有限公司</td></tr><tr><td rowspan=\"3\"></td><td>住所：浙江省宁波市鄞州区宁东路345号</td></tr><tr><td>后续销售机构如有新增、减少和变更，管理人将在本产品说明书约定的信息披露渠道进行披露。</td></tr><tr><td>本理财产品目前尚未确定具体理财投资合作机构，具体以管理人后续信息披露为准。</td></tr><tr><td rowspan=\"4\">风险评级</td><td>【PR2（中低风险）】，产品的风险评级仅是广银理财有限责任公司内部测评结果，仅供客户参考。</td></tr><tr><td>理财产品通过代理销售机构渠道销售的，理财产品评级应当以代理销售机构最终披露的评级结果为</td></tr><tr><td>准。销售评级与管理人产品评级结果不一致的，代理销售机构应当采用对应较高风险等级的评级结</td></tr><tr><td>果并予以披露。</td></tr><tr><td rowspan=\"3\">发行对象</td><td>适合【稳健型、平衡型、进取型、激进型】的个人投资者。本理财产品投资者人数不超过200人</td></tr><tr><td>本&quot;发行对象&quot;的划分与表述为管理人设置的表述，仅供参考。代销机构在销售本产品时，其对发行</td></tr><tr><td>对象的划分与表述可能与产品管理人存在差异，即采用代销机构内部设置的标准。</td></tr><tr><td rowspan=\"4\">产品期限</td><td>【371】天（以理财产品实际存续天数为准）。在理财产品存续期间，如发生以下事件，管理人有</td></tr><tr><td>权提前终止本产品：法律法规、监管规定及相关金融政策出现重大调整，市场或所投资资产出现重</td></tr><tr><td>大变化并影响到本产品的正常运作，以及其他原因导致管理人认为本产品已经不适合继续为投资者</td></tr><tr><td>实现投资目标。如果管理人决定提前终止理财产品，将于提前终止日前2个工作日通过约定的信息</td></tr><tr><td rowspan=\"4\">产品规模</td><td>披露渠道通知投资者。 本理财产品规模下限为【1.85】亿元。管理人可根据市场情况等设置或调整产品规模上限，并及时</td></tr><tr><td>通过约定的信息披露渠道进行披露。若本理财产品募集资金低于规模下限，管理人有权根据实际情</td></tr><tr><td>况成立本期理财产品，亦有权宣布该期产品不成立。若管理人宣布该期产品不成立时，将最迟于原</td></tr><tr><td>定成立日后2个工作日内进行信息披露并于原定成立日后3个交易日内将投资者认购本金退还至投 资者结算账户。</td></tr><tr><td rowspan=\"5\">业绩比较基准</td><td>【3.00】%年</td></tr><tr><td>业绩比较基准测算依据：基于本产品投资范围和投资限制，本产品拟投资资产主要包含债权类资产， 根据产品说明书约定投资范围内各类资产的收益率水平、投资比例、市场环境进行静态测算和情景</td></tr><tr><td>分析，并考虑相关税费成本、流动性管理等对组合收益的影响，综合以上情况作为业绩比较基准的</td></tr><tr><td>测算依据。</td></tr><tr><td>业绩比较基准是管理人根据市场环境、产品性质、投资策略、过往表现等因素对产品设定的投资目</td></tr></table></body></html>',\n", " '管理人根据市场变化和理财产品运行情况适时对投资范围、投资品种或投资比例进行调整，并按有关规定事先进行信息披露。超出销售文件约定比例的，除高风险类型的理财产品超出比例范围投资较低风险资产外，应当先取得投资者书面同意，并在全国银行业理财信息登记系统做好理财产品信息登记：投资者不接受的，可按照销售文件约定或信息披露文件提前赎回理财产品。',\n", " '2.投资者通过销售渠道提交认/申购申请，即视为投资者同意及授权广银理财有限责任公司作为本理财产品的管理人，将募集资金进行投资和执行相关操作，并且有权代表理财产品行使基础资产交易项下与其他交易各方进行交易所享有的全部权利。']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["refer_docs"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"销售机构\": [\n", "    \"宁波银行股份有限公司\"\n", "  ]\n", "}\n", "```\n"]}], "source": ["chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。\n", "    注意：请勿捏造数据，请根据实际情况输出。\n", "    请注意，输出格式必须为json格式，不要输出其他内容。\n", "\n", "    # 示例输出(仅供参考，请根据实际情况输出)\n", "    ```json\n", "    {\n", "      \"销售机构\": [\n", "        \"XX银行股份有限公司\",\n", "        \"XX银行股份有限公司\n", "      ]\n", "    }\n", "    ```\n", "    \"\"\"\n", "    )\n", "response = chatbot.chat(refer_doc_text)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
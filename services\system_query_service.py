# -*- coding: utf-8 -*-
"""
系统查询服务
"""
import json
import requests
from datetime import datetime
from flask import current_app
from services.database_service import DatabaseService

class SystemQueryService:
    """系统查询服务类"""
    
    def __init__(self):
        self.db_service = DatabaseService()
    
    def extract_query_keys_from_ai_result(self, ai_result, analysis_type):
        """从AI结果中提取查询关键字"""
        try:
            query_keys = []
            
            if not isinstance(ai_result, dict):
                return query_keys
            
            # 根据不同的分析类型提取不同的关键字
            if analysis_type == 'futures_account':
                # 期货账户分析 - 提取账户号、客户名称等
                extracted_fields = ai_result.get('extracted_fields', {})
                if 'account_number' in extracted_fields:
                    query_keys.append(extracted_fields['account_number'])
                if 'customer_name' in extracted_fields:
                    query_keys.append(extracted_fields['customer_name'])
                if 'contract_code' in extracted_fields:
                    query_keys.append(extracted_fields['contract_code'])
                    
            elif analysis_type == 'wealth_management':
                # 理财产品分析 - 提取产品代码、产品名称等
                extracted_fields = ai_result.get('extracted_fields', {})
                if 'product_code' in extracted_fields:
                    query_keys.append(extracted_fields['product_code'])
                if 'product_name' in extracted_fields:
                    query_keys.append(extracted_fields['product_name'])
                if 'issuer' in extracted_fields:
                    query_keys.append(extracted_fields['issuer'])
                    
            elif analysis_type == 'broker_interest':
                # 券商计息变更 - 提取券商名称、变更类型等
                extracted_fields = ai_result.get('extracted_fields', {})
                if 'broker_name' in extracted_fields:
                    query_keys.append(extracted_fields['broker_name'])
                if 'change_type' in extracted_fields:
                    query_keys.append(extracted_fields['change_type'])
                if 'effective_date' in extracted_fields:
                    query_keys.append(extracted_fields['effective_date'])
                    
            # 注意：futures_account 已在上面处理，这里删除重复条件
                    
            elif analysis_type == 'ningyin_fee':
                # 宁银费用变更 - 提取费用类型、变更内容等
                extracted_fields = ai_result.get('extracted_fields', {})
                if 'fee_type' in extracted_fields:
                    query_keys.append(extracted_fields['fee_type'])
                if 'change_content' in extracted_fields:
                    query_keys.append(extracted_fields['change_content'])
                if 'effective_date' in extracted_fields:
                    query_keys.append(extracted_fields['effective_date'])
                    
            elif analysis_type == 'non_standard_trade':
                # 非标交易确认单 - 提取交易编号、产品名称等
                extracted_fields = ai_result.get('extracted_fields', {})
                if 'trade_id' in extracted_fields:
                    query_keys.append(extracted_fields['trade_id'])
                if 'product_name' in extracted_fields:
                    query_keys.append(extracted_fields['product_name'])
                if 'counterparty' in extracted_fields:
                    query_keys.append(extracted_fields['counterparty'])
            
            # 去重并过滤空值
            query_keys = list(set([key for key in query_keys if key and str(key).strip()]))
            
            return query_keys
            
        except Exception as e:
            current_app.logger.error(f"提取查询关键字失败: {str(e)}")
            return []
    
    def query_customer_system(self, query_keys, analysis_type):
        """查询客户系统"""
        try:
            # 检查是否使用挡板模式
            if current_app.config.get('MOCK_MODE_ENABLED', False):
                return self.get_mock_system_result(query_keys, analysis_type)
            
            # 这里应该调用实际的客户系统API
            # 由于没有实际的客户系统，这里返回模拟结果
            return self.get_mock_system_result(query_keys, analysis_type)
            
        except Exception as e:
            current_app.logger.error(f"查询客户系统失败: {str(e)}")
            return {
                'success': False,
                'error': f'查询客户系统失败: {str(e)}'
            }
    
    def get_mock_system_result(self, query_keys, analysis_type):
        """获取挡板系统查询结果"""
        try:
            system_results = []
            
            for query_key in query_keys:
                # 查询挡板数据
                mock_data = self.db_service.get_mock_data(query_key, f'system_{analysis_type}')
                
                if mock_data:
                    system_results.append({
                        'query_key': query_key,
                        'found': True,
                        'data': mock_data.get_mock_result()
                    })
                else:
                    # 生成默认的模拟数据
                    mock_result = self.generate_default_mock_data(query_key, analysis_type)
                    system_results.append({
                        'query_key': query_key,
                        'found': True,
                        'data': mock_result
                    })
            
            return {
                'success': True,
                'results': system_results,
                'total_queries': len(query_keys),
                'found_count': len([r for r in system_results if r['found']]),
                'query_time': 0.1,
                'is_mock': True
            }
            
        except Exception as e:
            current_app.logger.error(f"获取挡板系统结果失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取挡板系统结果失败: {str(e)}'
            }
    
    def generate_default_mock_data(self, query_key, analysis_type):
        """生成默认的模拟数据"""
        base_data = {
            'query_key': query_key,
            'query_time': datetime.now().isoformat(),
            'source_system': 'mock_system'
        }
        
        if analysis_type == 'futures_account':
            return {
                **base_data,
                'account_info': {
                    'account_number': query_key,
                    'customer_name': f'客户_{query_key[-4:]}',
                    'account_status': 'active',
                    'balance': 1000000.00,
                    'available_balance': 800000.00,
                    'margin_ratio': 0.15
                }
            }
            
        elif analysis_type == 'wealth_management':
            return {
                **base_data,
                'product_info': {
                    'product_code': query_key,
                    'product_name': f'理财产品_{query_key}',
                    'product_type': '净值型',
                    'risk_level': 'R3',
                    'expected_return': '4.5%',
                    'min_amount': 10000,
                    'status': 'active'
                }
            }
            
        elif analysis_type == 'broker_interest':
            return {
                **base_data,
                'interest_info': {
                    'broker_name': query_key,
                    'current_rate': '3.5%',
                    'new_rate': '3.8%',
                    'change_date': datetime.now().strftime('%Y-%m-%d'),
                    'change_reason': '市场利率调整'
                }
            }
            
        elif analysis_type == 'account_opening':
            return {
                **base_data,
                'account_info': {
                    'customer_name': f'客户_{query_key}',
                    'account_type': '储蓄账户',
                    'account_number': f'6222{query_key}',
                    'opening_date': datetime.now().strftime('%Y-%m-%d'),
                    'branch_name': '宁波银行XX支行',
                    'id_number': f'330100{query_key}',
                    'business_type': '个人储蓄开户'
                }
            }
            
        elif analysis_type == 'ningyin_fee':
            return {
                **base_data,
                'fee_info': {
                    'fee_type': query_key,
                    'current_fee': '0.1%',
                    'new_fee': '0.12%',
                    'change_date': datetime.now().strftime('%Y-%m-%d'),
                    'applicable_products': ['理财产品', '基金产品']
                }
            }
            
        elif analysis_type == 'non_standard_trade':
            return {
                **base_data,
                'trade_info': {
                    'trade_id': query_key,
                    'trade_type': '非标债权',
                    'amount': 5000000.00,
                    'interest_rate': '6.5%',
                    'maturity_date': '2025-12-31',
                    'status': 'confirmed'
                }
            }
        
        return base_data
    
    def query_system_data(self, ai_result, analysis_type):
        """查询系统数据"""
        try:
            # 从AI结果中提取查询关键字
            query_keys = self.extract_query_keys_from_ai_result(ai_result, analysis_type)
            
            if not query_keys:
                return {
                    'success': True,
                    'results': [],
                    'total_queries': 0,
                    'found_count': 0,
                    'message': '未找到可查询的关键字'
                }
            
            # 查询客户系统
            return self.query_customer_system(query_keys, analysis_type)
            
        except Exception as e:
            current_app.logger.error(f"查询系统数据失败: {str(e)}")
            return {
                'success': False,
                'error': f'查询系统数据失败: {str(e)}'
            }
    
    def save_customer_system_data(self, query_key, query_type, data, description=None):
        """保存客户系统数据"""
        try:
            return self.db_service.create_customer_system_data(
                query_key=query_key,
                query_type=query_type,
                data_json=data,
                description=description
            )
        except Exception as e:
            current_app.logger.error(f"保存客户系统数据失败: {str(e)}")
            return None
    
    def get_customer_system_data(self, query_key, query_type):
        """获取客户系统数据"""
        try:
            from models import CustomerSystemData
            
            data = CustomerSystemData.query.filter_by(
                query_key=query_key,
                query_type=query_type,
                is_active=True
            ).first()
            
            return data.get_data_json() if data else None
            
        except Exception as e:
            current_app.logger.error(f"获取客户系统数据失败: {str(e)}")
            return None

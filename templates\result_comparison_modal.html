<!-- 三列结果对比弹窗样式 -->
<style>
/* 浅色商务风格样式 */
.modal-fullscreen .modal-content {
    border-radius: 0;
    border: none;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #e9ecef;
}

.info-item {
    text-align: center;
}

.info-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.info-value {
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
    word-break: break-all;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.completed { background: #d4edda; color: #155724; }
.status-badge.failed { background: #f8d7da; color: #721c24; }
.status-badge.processing { background: #fff3cd; color: #856404; }

.comparison-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #e9ecef;
}

.comparison-column {
    height: 600px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.ai-column {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #2196f3;
}

.expected-column {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    border: 2px solid #4caf50;
}

.comparison-column:last-child {
    background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
    border: 2px solid #ff9800;
}

.column-header {
    background: rgba(255,255,255,0.9);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.column-badge {
    margin-left: auto;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
}

.ai-badge { background: #2196f3; color: white; }
.expected-badge { background: #4caf50; color: white; }
.comparison-badge { background: #ff9800; color: white; }

.column-content {
    padding: 1rem;
    height: calc(100% - 60px);
    overflow-y: auto;
    background: rgba(255,255,255,0.7);
}

.field-row {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-left: 3px solid #e9ecef;
}

.field-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

.field-value {
    color: #6c757d;
    font-size: 0.9rem;
    word-break: break-all;
}

.comparison-icon {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.comparison-match { color: #28a745; }
.comparison-mismatch { color: #dc3545; }

.accuracy-summary {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #e9ecef;
}

.accuracy-item {
    text-align: center;
}

.accuracy-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.accuracy-bar-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.accuracy-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.accuracy-progress {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.field-progress { background: linear-gradient(90deg, #28a745, #20c997); }
.overall-progress { background: linear-gradient(90deg, #007bff, #6610f2); }

.accuracy-text {
    font-weight: 700;
    color: #495057;
    min-width: 50px;
}

.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #6c757d;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

.btn {
    border-radius: 6px;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
}

.btn-light {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

/* 编辑模式样式 */
.field-edit-input {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
}

.field-edit-input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    outline: 0;
}
</style>

<!-- 三列结果对比弹窗 -->
<div class="modal fade" id="resultComparisonModal" tabindex="-1" aria-labelledby="resultComparisonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content" style="background: #f8f9fa;">
            <div class="modal-header bg-white border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <div class="modal-icon me-3">
                        <i class="bi bi-clipboard-data text-primary fs-4"></i>
                    </div>
                    <div>
                        <h5 class="modal-title mb-0" id="resultComparisonModalLabel">结果对比分析</h5>
                        <small class="text-muted" id="modalRecordInfo">记录详情</small>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- 记录基本信息卡片 -->
                <div class="info-card mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">文件名</label>
                                <div class="info-value" id="modal-filename">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-item">
                                <label class="info-label">分析类型</label>
                                <div class="info-value" id="modal-analysis-type">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-item">
                                <label class="info-label">状态</label>
                                <div class="info-value">
                                    <span class="status-badge" id="modal-status">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-item">
                                <label class="info-label">字段准确率</label>
                                <div class="info-value text-success fw-bold" id="modal-field-accuracy">-</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-item">
                                <label class="info-label">整体准确率</label>
                                <div class="info-value text-primary fw-bold" id="modal-accuracy-score">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 三列对比区域 -->
                <div class="comparison-container">
                    <div class="row g-3">
                        <!-- 第一列：AI识别内容 (大列) -->
                        <div class="col-md-5">
                            <div class="comparison-column ai-column">
                                <div class="column-header">
                                    <i class="bi bi-robot"></i>
                                    <span>AI识别内容</span>
                                    <div class="column-badge ai-badge">AI</div>
                                </div>
                                <div class="column-content" id="ai-result-content">
                                    <div class="loading-state">
                                        <div class="spinner-border spinner-border-sm me-2"></div>
                                        加载中...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第二列：预期正确结果 (大列) -->
                        <div class="col-md-5">
                            <div class="comparison-column expected-column">
                                <div class="column-header">
                                    <i class="bi bi-check-circle"></i>
                                    <span>预期正确结果</span>
                                    <div class="column-badge expected-badge">预期</div>
                                    <button class="btn btn-sm btn-outline-light ms-auto" id="edit-expected-btn">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </button>
                                </div>
                                <div class="column-content" id="expected-result-content">
                                    <div class="loading-state">
                                        <div class="spinner-border spinner-border-sm me-2"></div>
                                        加载中...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第三列：对比结果 (小列) -->
                        <div class="col-md-2">
                            <div class="comparison-column comparison-column">
                                <div class="column-header">
                                    <i class="bi bi-bar-chart"></i>
                                    <span>对比</span>
                                    <div class="column-badge comparison-badge">√/×</div>
                                </div>
                                <div class="column-content" id="comparison-result-content">
                                    <div class="loading-state">
                                        <div class="spinner-border spinner-border-sm me-2"></div>
                                        加载中...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 准确率统计条 -->
                <div class="accuracy-summary mt-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="accuracy-item">
                                <div class="accuracy-label">字段级别准确率</div>
                                <div class="accuracy-bar-container">
                                    <div class="accuracy-bar">
                                        <div class="accuracy-progress field-progress" id="field-accuracy-bar" style="width: 0%"></div>
                                    </div>
                                    <span class="accuracy-text" id="field-accuracy-text">0%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="accuracy-item">
                                <div class="accuracy-label">整体准确率</div>
                                <div class="accuracy-bar-container">
                                    <div class="accuracy-bar">
                                        <div class="accuracy-progress overall-progress" id="overall-accuracy-bar" style="width: 0%"></div>
                                    </div>
                                    <span class="accuracy-text" id="overall-accuracy-text">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 新增：未匹配字段展示区域 -->
                <div class="info-card mt-4" id="unmatched-fields-card" style="display:none;">
                    <h6>未匹配字段</h6>
                    <ul id="unmatched-fields-list" class="list-group list-group-flush mb-0"></ul>
                </div>
            </div>

            <div class="modal-footer bg-white border-0 shadow-sm">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 关闭
                </button>
                <button type="button" class="btn btn-outline-primary" id="recalculate-accuracy-btn">
                    <i class="bi bi-arrow-clockwise"></i> 重新计算
                </button>
                <button type="button" class="btn btn-success" id="save-expected-result-btn" style="display: none;">
                    <i class="bi bi-save"></i> 保存修改
                </button>
                <!-- 新增：审核理由输入框 -->
                <textarea class="form-control ms-3" id="review-reason" rows="1" placeholder="请输入审核理由" style="max-width: 280px;"></textarea>
                <!-- 新增：自动切换开关 -->
                <div class="form-check form-switch ms-3 align-self-center">
                    <input class="form-check-input" type="checkbox" id="auto-switch-toggle">
                    <label class="form-check-label" for="auto-switch-toggle">自动切换下一个文件</label>
                </div>
                <!-- 新增：审核通过/不通过按钮 -->
                <button type="button" class="btn btn-outline-success ms-auto" id="approve-btn">
                    <i class="bi bi-check-circle"></i> 审核通过
                </button>
                <button type="button" class="btn btn-outline-danger ms-2" id="reject-btn">
                    <i class="bi bi-x-circle"></i> 审核不通过
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 字段编辑弹窗 -->
<div class="modal fade" id="fieldEditModal" tabindex="-1" aria-labelledby="fieldEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fieldEditModalLabel">编辑字段</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="field-edit-form">
                    <div class="mb-3">
                        <label for="field-path" class="form-label">字段路径</label>
                        <input type="text" class="form-control" id="field-path" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="field-value" class="form-label">字段值</label>
                        <textarea class="form-control" id="field-value" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-field-btn">保存</button>
            </div>
        </div>
    </div>
</div>

<style>
.result-content {
    max-height: 400px;
    overflow-y: auto;
    font-size: 0.9em;
}

.field-item {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.field-item.match {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.field-item.mismatch {
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.field-path {
    font-weight: bold;
    color: #495057;
    font-size: 0.85em;
}

.field-value {
    margin-top: 4px;
    font-family: 'Courier New', monospace;
}

.field-value.ai-value {
    color: #0066cc;
}

.field-value.expected-value {
    color: #28a745;
}

.field-edit-btn {
    float: right;
    margin-top: -2px;
}

.accuracy-progress {
    height: 20px;
}

.accuracy-label {
    font-size: 0.85em;
    font-weight: bold;
}
</style>

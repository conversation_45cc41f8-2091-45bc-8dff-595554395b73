<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ SYSTEM_NAME }}{% endblock %}</title>

    <!-- CSS样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css';">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css';">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">

    <!-- JavaScript库和工具 -->
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>

    {% block extra_css %}{% endblock %}
</head>
<body class="modern-body">
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <div class="brand-icon">
                    <img src="{{ url_for('static', filename='img/40_40.png') }}" alt="系统Logo">
                </div>
                <div class="brand-text">
                    <div class="brand-title">多场景智能化文档</div>
                    <div class="brand-subtitle">分析系统</div>
                </div>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="bi bi-chevron-left"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            {% if current_user.is_authenticated %}
            <div class="menu-section">
                <div class="menu-title">主要功能</div>
                <a href="{{ url_for('main.index') }}" class="menu-item">
                    <i class="bi bi-house"></i>
                    <span>首页</span>
                </a>
                <a href="{{ url_for('main.document_analysis') }}" class="menu-item">
                    <i class="bi bi-file-earmark-text"></i>
                    <span>文档分析</span>
                </a>
            </div>

            <div class="menu-section">
                <div class="menu-title">数据管理</div>
                <a href="{{ url_for('main.records') }}" class="menu-item">
                    <i class="bi bi-database"></i>
                    <span>记录管理</span>
                </a>
                <a href="{{ url_for('main.file_management') }}" class="menu-item">
                    <i class="bi bi-files"></i>
                    <span>文件管理</span>
                </a>
                <a href="{{ url_for('main.review') }}" class="menu-item">
                    <i class="bi bi-clipboard-check"></i>
                    <span>复核管理</span>
                </a>
            </div>

            {% if current_user.role == 'admin' %}
            <div class="menu-section">
                <div class="menu-title">系统管理</div>
                <a href="{{ url_for('main.user_management') }}" class="menu-item">
                    <i class="bi bi-people"></i>
                    <span>用户管理</span>
                </a>
                <a href="{{ url_for('main.model_config') }}" class="menu-item">
                    <i class="bi bi-cpu"></i>
                    <span>模型配置</span>
                </a>
                <a href="{{ url_for('main.prompt_config') }}" class="menu-item">
                    <i class="bi bi-pencil-square"></i>
                    <span>提示词管理</span>
                </a>
                <!-- <a href="{{ url_for('main.prompt_version_management') }}" class="menu-item">
                    <i class="bi bi-pencil-square"></i>
                    <span>提示词管理</span>
                </a> -->
                <a href="{{ url_for('main.mock_config') }}" class="menu-item">
                    <i class="bi bi-database"></i>
                    <span>挡板配置</span>
                </a>
                <a href="{{ url_for('main.system_config') }}" class="menu-item">
                    <i class="bi bi-gear"></i>
                    <span>系统设置</span>
                </a>
            </div>
            {% endif %}

            <div class="menu-section">
                <div class="menu-title">帮助支持</div>
                <a href="{{ url_for('main.help') }}" class="menu-item">
                    <i class="bi bi-question-circle"></i>
                    <span>帮助中心</span>
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <nav class="top-navbar">
            <div class="navbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="bi bi-list"></i>
                </button>
                <div class="page-title">
                    {% block page_title %}首页{% endblock %}
                </div>
            </div>

            <div class="navbar-right">
                {% if current_user.is_authenticated %}
                <div class="navbar-item">
                    <button class="notification-btn" data-bs-toggle="dropdown">
                        <i class="bi bi-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <div class="dropdown-header">
                            <h6>通知消息</h6>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon bg-primary">
                                <i class="bi bi-file-earmark-check"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">分析完成</div>
                                <div class="notification-text">文档分析已完成，准确率95%</div>
                                <div class="notification-time">2分钟前</div>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">待复核</div>
                                <div class="notification-text">有3个记录等待复核</div>
                                <div class="notification-time">10分钟前</div>
                            </div>
                        </div>
                        <div class="dropdown-footer">
                            <a href="#" class="text-center">查看全部通知</a>
                        </div>
                    </div>
                </div>

                <div class="navbar-item">
                    <div class="user-dropdown" data-bs-toggle="dropdown">
                        <div class="user-avatar">
                            {{ current_user.username[0].upper() }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ current_user.username }}</div>
                            <div class="user-role">{{ current_user.role }}</div>
                        </div>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                    <div class="dropdown-menu dropdown-menu-end user-menu">
                        <div class="dropdown-header">
                            <div class="user-avatar-large">
                                {{ current_user.username[0].upper() }}
                            </div>
                            <div>
                                <div class="fw-semibold">{{ current_user.username }}</div>
                                <div class="text-muted small">{{ current_user.email or '未设置邮箱' }}</div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                            <i class="bi bi-person me-2"></i>个人资料
                        </a>
                        <a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>退出登录
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="navbar-item">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-sm">登录</a>
                </div>
                <div class="navbar-item">
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-sm">注册</a>
                </div>
                {% endif %}
            </div>
        </nav>

        <!-- 页面内容区域 -->
        <div class="content-wrapper">
            <!-- 消息提示 -->
            <div id="messageContainer"></div>

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>

        <!-- 页脚 -->
        <footer class="content-footer">
            <div class="footer-content">
                <div class="footer-left">
                    <span>&copy; 2024 {{ COMPANY_NAME }}. {{ SYSTEM_NAME }} v{{ SYSTEM_VERSION }}</span>
                </div>
                <div class="footer-right">
                    <a href="{{ url_for('main.help') }}">帮助</a>
                    <a href="#">隐私政策</a>
                    <a href="#">服务条款</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 移动端遮罩层 -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"
            onerror="loadFallbackScript('https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js')"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
            onerror="loadFallbackScript('https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js')"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"
            onerror="loadFallbackScript('https://cdnjs.cloudflare.com/ajax/libs/axios/1.4.0/axios.min.js')"></script>

    <script>
        // 备用脚本加载函数
        function loadFallbackScript(src) {
            const script = document.createElement('script');
            script.src = src;
            script.onerror = function() {
                console.warn('Failed to load script:', src);
                // 如果所有CDN都失败，创建一个简单的axios替代
                if (src.includes('axios') && typeof axios === 'undefined') {
                    window.axios = {
                        get: function(url, config) {
                            return fetch(url, {method: 'GET', ...config}).then(r => ({data: r.json()}));
                        },
                        post: function(url, data, config) {
                            return fetch(url, {method: 'POST', body: data, ...config}).then(r => ({data: r.json()}));
                        },
                        put: function(url, data, config) {
                            return fetch(url, {method: 'PUT', body: JSON.stringify(data), headers: {'Content-Type': 'application/json'}, ...config}).then(r => ({data: r.json()}));
                        },
                        defaults: {headers: {common: {}}},
                        interceptors: {response: {use: function() {}}}
                    };
                }
                if (src.includes('jquery') && typeof $ === 'undefined') {
                    // 创建简单的jQuery替代
                    window.$ = window.jQuery = function(selector) {
                        if (typeof selector === 'string') {
                            return {
                                on: function() { return this; },
                                val: function() { return ''; },
                                html: function() { return this; },
                                empty: function() { return this; },
                                each: function() { return this; }
                            };
                        }
                        return selector;
                    };
                    console.warn('Using fallback jQuery implementation');
                }
            };
            document.head.appendChild(script);
        }

        // 检查axios是否加载成功
        setTimeout(function() {
            if (typeof axios === 'undefined') {
                console.warn('Axios not loaded, creating fallback');
                window.axios = {
                    get: function(url, config) {
                        return fetch(url, {method: 'GET', ...config}).then(r => r.json().then(data => ({data})));
                    },
                    post: function(url, data, config) {
                        const options = {method: 'POST', ...config};
                        if (data instanceof FormData) {
                            options.body = data;
                        } else {
                            options.body = JSON.stringify(data);
                            options.headers = {'Content-Type': 'application/json', ...options.headers};
                        }
                        return fetch(url, options).then(r => r.json().then(data => ({data})));
                    },
                    put: function(url, data, config) {
                        return fetch(url, {
                            method: 'PUT',
                            body: JSON.stringify(data),
                            headers: {'Content-Type': 'application/json'},
                            ...config
                        }).then(r => r.json().then(data => ({data})));
                    },
                    defaults: {headers: {common: {}}},
                    interceptors: {response: {use: function(success, error) {
                        // 简单的拦截器实现
                        return {success, error};
                    }}}
                };
            }

            // 检查jQuery是否加载成功
            if (typeof $ === 'undefined') {
                console.warn('jQuery not loaded, creating fallback');
                window.$ = window.jQuery = function(selector) {
                    if (typeof selector === 'function') {
                        // 模拟 $(document).ready()
                        if (document.readyState === 'loading') {
                            document.addEventListener('DOMContentLoaded', selector);
                        } else {
                            selector();
                        }
                        return;
                    }

                    if (typeof selector === 'string') {
                        const elements = document.querySelectorAll(selector);
                        return {
                            length: elements.length,
                            on: function(event, handler) {
                                elements.forEach(el => el.addEventListener(event, handler));
                                return this;
                            },
                            val: function(value) {
                                if (value !== undefined) {
                                    elements.forEach(el => el.value = value);
                                    return this;
                                }
                                return elements[0] ? elements[0].value : '';
                            },
                            html: function(content) {
                                if (content !== undefined) {
                                    elements.forEach(el => el.innerHTML = content);
                                    return this;
                                }
                                return elements[0] ? elements[0].innerHTML : '';
                            },
                            empty: function() {
                                elements.forEach(el => el.innerHTML = '');
                                return this;
                            },
                            each: function(callback) {
                                elements.forEach((el, index) => callback.call(el, index, el));
                                return this;
                            }
                        };
                    }
                    return selector;
                };
            }
        }, 1000);
    </script>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // 侧边栏控制
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const menuToggle = document.getElementById('menuToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            // 侧边栏切换
            function toggleSidebar() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }

            // 移动端侧边栏显示/隐藏
            function toggleMobileSidebar() {
                sidebar.classList.toggle('mobile-show');
                sidebarOverlay.classList.toggle('show');
                document.body.classList.toggle('sidebar-open');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            if (menuToggle) {
                menuToggle.addEventListener('click', toggleMobileSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', toggleMobileSidebar);
            }

            // 设置当前页面菜单项为活跃状态
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });

            // 响应式处理
            function handleResize() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('expanded');
                } else {
                    sidebar.classList.remove('mobile-show');
                    sidebarOverlay.classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });

        // 全局JavaScript函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                // 确保axios可用
                const httpClient = window.axios || {
                    post: (url) => fetch(url, {method: 'POST'}).then(r => r.json().then(data => ({data})))
                };

                httpClient.post('/auth/logout')
                    .then(response => {
                        if (response.data.success) {
                            window.location.href = '/auth/login';
                        } else {
                            showMessage('退出失败：' + response.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        showMessage('退出失败，请稍后重试', 'error');
                    });
            }
        }

        function showMessage(message, type = 'info', duration = 5000) {
            const container = document.getElementById('messageContainer');
            const iconMap = {
                'success': 'bi-check-circle',
                'error': 'bi-exclamation-triangle',
                'warning': 'bi-exclamation-triangle',
                'info': 'bi-info-circle'
            };

            const alertClass = type === 'error' ? 'alert-danger' :
                              type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-info';

            const icon = iconMap[type] || 'bi-info-circle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show modern-alert" role="alert">
                    <i class="bi ${icon} me-2"></i>
                    <div>${message}</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            container.innerHTML = alertHtml;

            // 自定义持续时间后自动消失
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, duration);
        }

        // 设置axios默认配置（如果axios可用）
        setTimeout(function() {
            if (window.axios && axios.defaults) {
                axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

                // 全局错误处理
                axios.interceptors.response.use(
                    response => response,
                    error => {
                        if (error.response && error.response.status === 401) {
                            window.location.href = '/auth/login';
                        } else if (error.response && error.response.status === 403) {
                            showMessage('权限不足', 'error');
                        }
                        return Promise.reject(error);
                    }
                );
            }
        }, 1500);
    </script>
</body>
</html>
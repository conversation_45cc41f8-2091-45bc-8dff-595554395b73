"""
创建系统配置项
"""
from flask import Flask
from sqlalchemy.exc import IntegrityError
from models import db, GlobalSetting

def create_app():
    """创建应用实例"""
    app = Flask(__name__)
    
    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///document_analysis_system.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # 初始化扩展
    db.init_app(app)
    
    return app

def create_system_configs():
    """创建系统配置项"""
    configs = [
        # 系统配置
        {
            'key': 'system_name',
            'value': '多场景智能化文档分析系统',
            'description': '系统名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'system_version',
            'value': '2.0.0',
            'description': '系统版本',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'company_name',
            'value': '宁波银行',
            'description': '公司名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'auto_analysis',
            'value': 'true',
            'description': '上传后自动分析',
            'data_type': 'boolean',
            'is_public': True
        },
        # API配置
        {
            'key': 'api_url',
            'value': 'https://api.openai.com/v1',
            'description': 'API地址',
            'data_type': 'string',
            'is_public': False
        },
        {
            'key': 'api_key',
            'value': 'sk-your-api-key',
            'description': 'API密钥',
            'data_type': 'string',
            'is_public': False
        },
        {
            'key': 'model_name',
            'value': 'gpt-4',
            'description': '模型名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'max_tokens',
            'value': '4096',
            'description': '最大令牌数',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'temperature',
            'value': '0.7',
            'description': '温度参数',
            'data_type': 'float',
            'is_public': True
        },
        # 存储配置
        {
            'key': 'upload_limit',
            'value': '10',
            'description': '上传大小限制(MB)',
            'data_type': 'integer',
            'is_public': True
        },
        {
            'key': 'storage_path',
            'value': 'uploads',
            'description': '存储路径',
            'data_type': 'string',
            'is_public': False
        },
        # UI配置
        {
            'key': 'theme',
            'value': 'light',
            'description': '界面主题',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'language',
            'value': 'zh-CN',
            'description': '界面语言',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'default_page_size',
            'value': '20',
            'description': '默认每页数量',
            'data_type': 'integer',
            'is_public': True
        },
        # 安全配置
        {
            'key': 'session_timeout',
            'value': '30',
            'description': '会话超时时间(分钟)',
            'data_type': 'integer',
            'is_public': False
        },
        {
            'key': 'enable_2fa',
            'value': 'false',
            'description': '启用双因素认证',
            'data_type': 'boolean',
            'is_public': False
        },
        {
            'key': 'password_policy',
            'value': '{"min_length": 8, "require_special": true, "require_number": true, "require_uppercase": true}',
            'description': '密码策略',
            'data_type': 'json',
            'is_public': False
        }
    ]
    
    created = 0
    updated = 0
    skipped = 0
    
    for config in configs:
        try:
            # 检查配置项是否已存在
            existing = GlobalSetting.query.filter_by(key=config['key']).first()
            
            if existing:
                # 如果存在但为空，则更新
                if not existing.value:
                    existing.value = config['value']
                    existing.data_type = config['data_type']
                    existing.is_public = config['is_public']
                    db.session.commit()
                    updated += 1
                    print(f"更新配置项: {config['key']}")
                else:
                    skipped += 1
                    print(f"配置项已存在: {config['key']}")
            else:
                # 如果不存在，则创建
                setting = GlobalSetting(**config)
                db.session.add(setting)
                db.session.commit()
                created += 1
                print(f"创建配置项: {config['key']}")
        except IntegrityError:
            db.session.rollback()
            print(f"配置项创建失败: {config['key']}")
    
    print(f"\n完成配置项创建：创建 {created} 项，更新 {updated} 项，跳过 {skipped} 项")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        create_system_configs()
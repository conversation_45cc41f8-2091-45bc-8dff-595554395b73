def run(fn):
    usage_model = 'InternVL3-38B'
    markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
    refer_docs, refer_doc_text = rag_search_neighbor(markdown_content, keywords=["销售", "代销", "代理销售"], keywords2=["公司", "银行"])

    chatbot = ChatBot(
    model=usage_model,
    system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。
    注意：请勿捏造数据，请根据实际情况输出。
    请注意：
    - 输出格式必须为json格式，不要输出其他内容。
    - 如不存在销售机构，则输出销售机构为本银行

    # 示例输出(仅供参考，请根据实际情况输出)
    ```json
    {
      "销售机构": [
        "XX银行股份有限公司",
        "XX银行股份有限公司
      ]
    }
    ```
    """
    )
    response = chatbot.chat(refer_doc_text, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(response)
    return json_data

# -*- coding: utf-8 -*-
# 非标交易确认单解析
import sys
import os
import importlib.util

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

print("[INFO] 正在加载必要模块...")

# 使用 importlib 直接加载 utils.py 文件，避免命名冲突
utils_file_path = os.path.join(project_root, 'utils.py')
if not os.path.exists(utils_file_path):
    print(f"[ERROR] 找不到 utils.py 文件: {utils_file_path}")
    sys.exit(1)

spec = importlib.util.spec_from_file_location("utils_direct", utils_file_path)
utils_module = importlib.util.module_from_spec(spec)

# 临时修改 sys.modules 避免循环导入
original_utils = sys.modules.get('utils')
sys.modules['utils'] = utils_module

try:
    spec.loader.exec_module(utils_module)
    print("[SUCCESS] utils.py 模块加载成功")
except Exception as e:
    print(f"[ERROR] utils.py 模块加载失败: {e}")
    if original_utils:
        sys.modules['utils'] = original_utils
    else:
        sys.modules.pop('utils', None)
    sys.exit(1)

# 导入 ChatBot
try:
    from util_ai import ChatBot
    print("[SUCCESS] ChatBot 导入成功")
except Exception as e:
    print(f"[ERROR] ChatBot 导入失败: {e}")
    sys.exit(1)

# 从 utils.py 获取需要的函数
try:
    fn_to_markdown_v2 = utils_module.fn_to_markdown_v2
    process_file_to_base64 = utils_module.process_file_to_base64
    markdown_json_to_dict = utils_module.markdown_json_to_dict
    cal_fn_md5 = utils_module.cal_fn_md5
    print("[SUCCESS] 所有函数导入成功")
except Exception as e:
    print(f"[ERROR] 函数导入失败: {e}")
    sys.exit(1)
import json
import tqdm
import datetime
import inspect
import time
import pandas as pd
import io

DIR_NAME = "非标交易确认单"

def run(fn):
    usage_model = 'InternVL3-38B'
    
    # 判断文件类型
    file_ext = os.path.splitext(fn)[1].lower()
    
    # 对Excel文件特殊处理
    if file_ext in ['.xlsx', '.xls']:
        try:
            # 读取Excel文件，保持原始精度，不进行任何数值转换
            df = pd.read_excel(fn, dtype=str, engine='openpyxl')  # 直接以字符串格式读取所有列，使用openpyxl引擎
            
            # 处理NaN值，但保持其他值的原始格式
            df = df.fillna('')  # 将NaN替换为空字符串
            
            # 将DataFrame转换为Markdown表格，保持原始格式
            markdown_content = "# Excel文件内容\n\n**重要提示：以下表格中的所有数值必须严格按照显示的格式进行提取，包括所有小数位数和负号，不得进行任何精度优化！**\n\n"
            
            # 自定义to_markdown函数，保持原始精度
            def custom_to_markdown(df):
                if df.empty:
                    return "| | |\n|---|---|"
                
                # 获取列名
                headers = list(df.columns)
                markdown_table = "| " + " | ".join(str(h) for h in headers) + " |\n"
                markdown_table += "|" + "|".join("---" for _ in headers) + "|\n"
                
                # 添加数据行
                for _, row in df.iterrows():
                    row_values = []
                    for col in headers:
                        value = row[col]
                        # 保持原始值，包括负号和精度，严格按照字符串处理
                        if pd.isna(value) or value == '' or str(value).lower() in ['nan', 'none', '']:
                            row_values.append('')
                        else:
                            # 确保完全按照原始字符串输出，不进行任何格式化
                            original_str = str(value).strip()
                            row_values.append(original_str)
                    markdown_table += "| " + " | ".join(row_values) + " |\n"
                
                return markdown_table
            
            markdown_content += custom_to_markdown(df)
            
            # 添加调试信息，显示原始数据
            markdown_content += "\n\n**【数据精度检查】**：请严格按照上表中显示的数值进行提取，特别注意小数位数！\n"
            
            # 不需要印章信息
            seal_img_list = []
        except Exception as e:
            print(f"Excel文件读取错误: {str(e)}")
            raise Exception(f"Excel文件读取错误: {str(e)}")
    # 对Word文件特殊处理
    elif file_ext in ['.docx', '.doc']:
        try:
            import docx2txt
            # 提取文本内容
            text = docx2txt.process(fn)
            markdown_content = f"# Word文档内容\n\n{text}"
            seal_img_list = []
        except ImportError:
            print("缺少docx2txt库，请安装: pip install docx2txt")
            raise Exception("缺少docx2txt库，请安装: pip install docx2txt")
        except Exception as e:
            print(f"Word文件读取错误: {str(e)}")
            raise Exception(f"Word文件读取错误: {str(e)}")
    # PDF和图片文件使用原有逻辑
    else:
        if fn.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model=usage_model)
        else:
            # 如果是图片则先转为pdf
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
    
    # 如果是Excel或Word文件，跳过VLM OCR处理
    if file_ext not in ['.xlsx', '.xls', '.docx', '.doc']:
        # VL2OCR
        ocr_bot = ChatBot(model='InternVL3-38B')
        img_list = process_file_to_base64(fn)
        vlm_info = ocr_bot.chat_with_img("你是一名OCR专家。请从用户提供的图片中提取有关印章信息、落款日期的全部有效信息",img_url=img_list, max_tokens=100)
        markdown_content += f"\n# 补充信息：\n{vlm_info}"
    
    sys_prompt = """你是一位资深的银行托管部经理，负责从客户提供的非标交易确认单中**抽取交易要素**，并按以下要求结构化输出 JSON 数组。

**【关键提醒】**：在处理数值时，你必须像复制粘贴一样，完全按照原文档中显示的字符进行提取，不得进行任何数值处理！

=====================【字段定义】=====================
1. 【投资者名称】：通常指代客户姓名，一般是资管计划的名称
2. 【投资者账号】：通常指代客户的资金账号
3. 【业务日期】：对应某一笔交易的日期（YYYY-MM-DD格式）
4. 【业务类型】：需要提取文件中代表当前类型的文字，并映射到下面的选项中：分红、红利转投、买入、卖出、认购、申购、赎回。一般来说确认单文档只对应一个业务类型、一笔交易，但是对账单文件可能会列示多笔交易，对应多个业务类型
5. 【投资标的名称】：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 【投资标的代码】：投资标的的代码，多为数字和字母的组合，也可能为空
7. 【投资标的金额】：实际交易的确认金额（必须保持原文档中的精确格式，包括小数位数和负号，不得进行任何数值优化）
8. 【投资标的数量】：文档中可能用份额来描述（必须保持原文档中的精确格式，包括小数位数和负号，不得进行任何数值优化）
9. 【交易费用】：一般申购、赎回、买入、卖出交易中，会标明交易费用，没有则可以为空（必须保持原文档中的精确格式，包括小数位数和负号）

=====================【业务类型识别规则】=====================
**重要：必须严格按照以下规则识别业务类型，并映射到标准术语**

**标准业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回

**识别关键词映射表**：
• **分红** ← 关键词：分红、股息、利息分配、收益分配、现金分红、分红派息
• **红利转投** ← 关键词：红利转投、分红转投、红利再投资、分红再投资、红利转份额、分红转份额
• **买入** ← 关键词：买入、购买、投资、成交买入
• **卖出** ← 关键词：卖出、出售、变现、成交卖出、兑付、到期兑付
• **认购** ← 关键词：认购、首次认购、新发认购、认购确认
• **申购** ← 关键词：申购、追加申购、申购确认、增购
• **赎回** ← 关键词：赎回、赎回确认、提取、退出、部分赎回、全部赎回

**识别步骤**：
1. 首先在文档中寻找明确的业务类型标识（如"业务类型"、"交易类型"、"操作类型"等字段）
2. 如果没有明确字段，则根据文档标题、内容描述中的关键词进行识别
3. 将识别到的原始词汇映射到上述7个标准业务类型之一
4. 如果无法确定，优先根据文档的主要内容和金额流向判断

=====================【数值提取增强规则】=====================
**针对投资标的金额、投资标的数量、交易费用字段的特殊处理**：

1. **多重查找策略**：
   - 优先查找明确标注的字段（如"确认金额"、"交易金额"、"份额"、"数量"等）
   - 查找表格中的数值列
   - 查找文本中的货币符号后的数值（¥、￥、RMB等）
   - 查找带有千分位分隔符的数值（如1,234,567.89）

2. **数值识别模式**：
   - 正数：123456.78、1,234,567.89、￥123,456.78
   - 负数：-123456.78、-1,234,567.89、-￥123,456.78
   - 零值：0、0.00、0.000等

3. **容错处理**：
   - 如果某个数值字段在文档中确实存在但难以精确定位，不要填写"/"
   - 仔细查看表格的每一行每一列
   - 注意数值可能出现在不同的位置（表头、表尾、备注等）

4. **精度保持**：
   - 完全按照原文档显示的格式输出，包括所有小数位
   - 保留千分位分隔符（如果原文档有的话）
   - 保留负号和货币符号前缀

=====================【格式要求】=====================
• 日期全部转为 `YYYY-MM-DD` 格式
• 金额和数量字段为字符串格式，保持原始精度和负号，不要进行数值转换
• 业务类型必须是以下7个之一：分红、红利转投、买入、卖出、认购、申购、赎回
• 输出 **JSON 数组**，字段顺序固定，示例如下：

```json
[
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-15",
    "业务类型": "申购",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "100,000.00",
    "投资标的数量": "10,000.00",
    "交易费用": "150.00"
  },
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-16",
    "业务类型": "分红",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "-5,000.00",
    "投资标的数量": "/",
    "交易费用": "/"
  }
]
```

**【严格精度要求 - 必须遵守】**
1. 如果文档中确实没有相关信息，对应字段填写"/"
2. **金额和数量字段的精度要求（绝对不可违反）**：
   - 如果原文档显示"329,961,736.14"，必须输出"329,961,736.14"
   - 如果原文档显示"100.00"，必须输出"100.00"
   - 如果原文档显示"-5,000.123"，必须输出"-5,000.123"
   - 严禁进行任何数值格式化、精度优化或小数位截断
3. **负号保留（绝对不可省略）**：
   - 如果原文档中的数值带有负号"-"，输出时必须保留该负号
4. **字符串完全匹配原则**：
   - 将数值字段视为字符串，完全按照原文档中的字符进行复制
   - 不要进行任何数学计算或格式转换
   - 保持原始的所有字符，包括小数点后的每一位数字和千分位分隔符

**【特别注意】**：
- 仔细检查文档中的每个表格、每行数据
- 数值字段绝对不能轻易填写"/"，除非确实在文档中找不到任何相关数值
- 业务类型必须严格按照映射规则转换为标准术语
"""
    chat_bot = ChatBot(system_prompt=sys_prompt, model='InternVL3-38B')

    res = chat_bot.chat(markdown_content, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(res)
    return json_data

def get_full_fn_list():
    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    
    # 只读取POC脱敏材料下的文件
    poc_path = os.path.join(project_root, "大模型样例", "POC脱敏材料")
    
    file_types = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp', '.xlsx', '.xls', '.docx']

    full_fn_list = []
    
    print(f"=== 开始扫描文件 ===")
    print(f"项目根目录: {project_root}")
    print(f"POC脱敏材料路径: {poc_path}")
    print(f"支持的文件类型: {file_types}")
    print()
    
    # 检查并添加POC脱敏材料下的相关文件
    print("=== 扫描POC脱敏材料文件夹 ===")
    poc_subdirs = ['非标红利转投（脱敏）', '非标卖出（脱敏）', '非标分红（脱敏）', 
                   '非标买入（脱敏）', '赎回确认（脱敏）', '认购确认（脱敏）']
    
    total_files = 0
    for subdir in poc_subdirs:
        subdir_path = os.path.join(poc_path, subdir)
        print(f"检查文件夹: {subdir_path}")
        if os.path.exists(subdir_path):
            print(f"  文件夹存在，开始读取文件...")
            file_count = 0
            for fn in os.listdir(subdir_path):
                if os.path.splitext(fn)[1].lower() in file_types:
                    full_fn_list.append(os.path.join(subdir_path, fn))
                    file_count += 1
                    print(f"    ✓ 添加文件: {fn}")
            print(f"  文件夹 {subdir} 共添加 {file_count} 个文件")
            total_files += file_count
        else:
            print(f"  文件夹不存在: {subdir_path}")
    
    print()
    print(f"=== 扫描完成 ===")
    print(f"总共找到 {len(full_fn_list)} 个文件 (应为 {total_files} 个)")
    print()

    return full_fn_list

def test_al(cache_answer=False):
    """
    测试所有文件，并保存答案
    """
    # 构建分支号
    var = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    full_fn_list = get_full_fn_list()
    
    if not full_fn_list:
        print("未找到任何可处理的文件！")
        return
    
    print(f"找到 {len(full_fn_list)} 个文件待处理")
    
    # 文件md5与原始文件名（非完整路径）的映射
    fn_md5_dict = {}
    deal_fn_list = []
    
    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    
    check_dir = os.path.join(project_root, "check", DIR_NAME, var)
    os.makedirs(check_dir, exist_ok=True)
    
    # 确保answer目录存在
    answer_dir = os.path.join(project_root, "answer", DIR_NAME)
    os.makedirs(answer_dir, exist_ok=True)
    
    if not cache_answer:
        # 将run函数的内容写入check_dir
        with open(os.path.join(check_dir, "run.py"), "w", encoding='utf-8') as f:
            try:
                # 优先尝试获取函数源代码
                source = inspect.getsource(run)
                f.write(source)
            except (TypeError, OSError):
                # 备选方案：创建带注释的存根文件
                f.write("# run函数未找到有效源代码\n")
                f.write("def run():\n    # 函数实现未捕获\n    pass\n")

    for fn in tqdm.tqdm(full_fn_list):
        try:
            file_md5 = cal_fn_md5(fn)
            answer_fn = os.path.join(project_root, "answer", DIR_NAME, f"{file_md5}.json")
            check_fn = os.path.join(check_dir, f"{file_md5}.json")
            
            if cache_answer:
                if os.path.exists(answer_fn):
                    continue
                else:
                    save_fn = answer_fn
            else:
                save_fn = check_fn
                
            fn_md5_dict[file_md5] = fn
            
            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    json_data = run(fn)
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"处理文件 {fn} 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        print(f"处理文件 {fn} 失败，已达到最大重试次数: {str(e)}")
                        # 创建错误响应
                        json_data = {
                            "error": f"处理失败: {str(e)}",
                            "file": fn,
                            "timestamp": datetime.datetime.now().isoformat()
                        }
            
            deal_fn_list.append(fn)
            
            with open(save_fn, "w", encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"处理文件 {fn} 时发生严重错误: {str(e)}")
            # 继续处理下一个文件，不中断整个流程
            continue

    # 根据cache_answer参数决定fn_md5_dict.json的保存位置
    if cache_answer:
        # 选项1：保存到answer目录
        dict_save_dir = os.path.join(project_root, "answer", DIR_NAME)
        os.makedirs(dict_save_dir, exist_ok=True)
    else:
        # 选项2：保存到check目录（带时间戳）
        dict_save_dir = check_dir
    
    with open(os.path.join(dict_save_dir, "fn_md5_dict.json"), "w", encoding='utf-8') as f:
        json.dump(fn_md5_dict, f, ensure_ascii=False, indent=4)
    
    if len(deal_fn_list) > 0:
        print(f"所有答案预存完成，共处理 {len(deal_fn_list)} 个文件！")
        print(f"结果保存在: {check_dir}")
    else:
        print("所有答案都已缓存，无新生成的答案！")

if __name__ == "__main__":
    select_but = input("请选择操作：\n1. 生成标准答案\n2. 生成测试答案\n")
    if select_but == "1":
        test_al(cache_answer=True)
    elif select_but == "2":
        test_al(cache_answer=False)
    else:
        print("无效选择！")
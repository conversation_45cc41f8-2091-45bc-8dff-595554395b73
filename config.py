# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 配置文件
"""
import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'nb-bank-ai-poc-secret-key-2024'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'password'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'document_analysis_system'

    # SQLAlchemy配置 - 使用SQLite作为默认数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///document_analysis_system.db?timeout=20'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 30,  # 增加连接池超时时间
        'pool_recycle': 3600,  # 1小时后回收连接
        'max_overflow': 5,  # 允许更多溢出连接
        'echo': False,
        # SQLite特定配置
        'connect_args': {
            'timeout': 30,  # SQLite连接超时
            'check_same_thread': False,  # 允许多线程访问
        } if 'sqlite' in (os.environ.get('DATABASE_URL') or 'sqlite:///document_analysis_system.db') else {}
    }
    
    # Flask-Login配置
    REMEMBER_COOKIE_DURATION = timedelta(days=7)
    SESSION_PROTECTION = 'strong'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 200 * 1024 * 1024  # 200MB
    UPLOAD_BASE = 'uploads'
    PROCESSED_BASE = 'processed'
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
    
    # 分析类型配置
    ANALYSIS_TYPES = {
        'futures_account': '期货账户/开户文件解析',
        'wealth_management': '理财产品说明书',
        'broker_interest': '券商账户计息变更',
        'account_opening': '账户开户场景',
        'ningyin_fee': '宁银理财费用变更',
        'non_standard_trade': '非标交易确认单解析'
    }
    
    # AI模型配置
    DEFAULT_MODEL_CONFIG = {
        'model_id': 'qwen3-32b',
        'model_name': 'Qwen3-32B',
        'api_url': 'http://192.168.10.236:23000/v1',
        'api_key': 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553',
        'vision_model': 'qwen-vl-max',
        'timeout': 30,
        'max_tokens': 4096,
        'temperature': 0.7
    }
    
    # 系统设置
    SYSTEM_NAME = '多场景智能化文档分析系统'
    SYSTEM_VERSION = '2.0.0'
    COMPANY_NAME = '宁波银行'
    
    # 挡板模式配置
    MOCK_MODE_ENABLED = True
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 分页配置
    RECORDS_PER_PAGE = 20
    MAX_RECORDS_PER_PAGE = 100
    
    # 安全配置
    WTF_CSRF_ENABLED = False
    WTF_CSRF_TIME_LIMIT = None
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('uploads', exist_ok=True)
        os.makedirs('processed', exist_ok=True)
        os.makedirs('static', exist_ok=True)
        os.makedirs('templates', exist_ok=True)
        
        # 为每个分析类型创建目录
        for analysis_type in Config.ANALYSIS_TYPES.keys():
            os.makedirs(os.path.join('uploads', analysis_type), exist_ok=True)
            os.makedirs(os.path.join('processed', analysis_type), exist_ok=True)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        **Config.SQLALCHEMY_ENGINE_OPTIONS,
        'echo': True
    }

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///document_analysis_system_test.db'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为每个分析类型插入测试数据脚本
"""

import os
import sys
import json
from datetime import datetime
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, AnalysisRecord, User
from utils.file_utils import calculate_file_hash, get_file_info

def get_test_files():
    """获取uploads目录下的测试文件路径"""
    test_files = {}

    # 扫描uploads目录下的各个类型目录
    uploads_dir = 'uploads'
    if not os.path.exists(uploads_dir):
        print(f"错误: uploads目录不存在")
        return {}

    # 定义每个类型对应的目录
    type_dirs = {
        'futures_account': 'futures_account',
        'wealth_management': 'wealth_management',
        'non_standard_trade': 'non_standard_trade',
        'broker_interest': 'broker_interest',
        'ningyin_fee': 'ningyin_fee',
        'account_opening': 'account_opening'
    }

    for analysis_type, dir_name in type_dirs.items():
        type_dir = os.path.join(uploads_dir, dir_name)
        if os.path.exists(type_dir):
            # 获取目录下的第一个文件
            files = [f for f in os.listdir(type_dir) if os.path.isfile(os.path.join(type_dir, f))]
            if files:
                # 优先选择PDF文件，其次是Excel，最后是图片
                pdf_files = [f for f in files if f.lower().endswith('.pdf')]
                excel_files = [f for f in files if f.lower().endswith(('.xlsx', '.xls'))]
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                if pdf_files:
                    selected_file = pdf_files[0]
                elif excel_files:
                    selected_file = excel_files[0]
                elif image_files:
                    selected_file = image_files[0]
                else:
                    selected_file = files[0]

                test_files[analysis_type] = os.path.join(type_dir, selected_file)
                print(f"找到 {analysis_type} 类型文件: {selected_file}")
            else:
                print(f"警告: {type_dir} 目录为空")
        else:
            print(f"警告: {type_dir} 目录不存在")

    return test_files

def get_json_data():
    """从类型对应json.txt文件中获取JSON数据"""
    json_data = {}
    
    # 期货账户文件解析
    json_data['futures_account'] = {
        "产品名称": "合煦智远嘉选混合型证券投资基金",
        "资金账号": "********",
        "会员号": {
            "上期所": "0249",
            "大商所": "0101",
            "郑商所": "0206",
            "中金所": "0316",
            "上能所": "8249",
            "广期所": "0101"
        },
        "交易编码": {
            "上期所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            },
            "大商所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            },
            "郑商所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            },
            "中金所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            },
            "上能所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            },
            "广期所": {
                "投机": "/",
                "套利": "/",
                "套保": "/"
            }
        },
        "开始时间": "2024-07-03",
        "结束时间": "/"
    }
    
    # 理财产品说明书
    json_data['wealth_management'] = {
        "销售机构": [
            "浙江舟山普陀农村商业银行股份有限公司",
            "浙江临安农村商业银行股份有限公司",
            "宁波银行股份有限公司"
        ]
    }

    # 非标交易确认单
    json_data['non_standard_trade'] = [
        {
            "投资者名称": "上海农—商业银行股份有限公司",
            "投资者账号": "ZS010008026",
            "业务日期": "/",
            "业务类型": "分红",
            "投资标的名称": "上海农商银行鑫利系列天天金1号人民币理财产品",
            "投资标的代码": "/",
            "投资标的金额": "12472553.63",
            "投资标的数量": "329961736.14",
            "交易费用": "/"
        },
        {
            "投资者名称": "上海农—商业银行股份有限公司",
            "投资者账号": "ZS010007993",
            "业务日期": "/",
            "业务类型": "分红",
            "投资标的名称": "上海农商银行鑫利系列天天金2号人民币理财产品（安享款）",
            "投资标的代码": "/",
            "投资标的金额": "46184353.62",
            "投资标的数量": "1221808296.86",
            "交易费用": "/"
        },
        {
            "投资者名称": "上海农—商业银行股份有限公司",
            "投资者账号": "ZS010008086",
            "业务日期": "/",
            "业务类型": "分红",
            "投资标的名称": "上海农商银行鑫利系列鑫享利22001期（最低持有30天之月月鑫）人民币理财产品",
            "投资标的代码": "/",
            "投资标的金额": "24967676.97",
            "投资标的数量": "660520554.84",
            "交易费用": "/"
        }
    ]

    # 券商账户计息变更
    json_data['broker_interest'] = [
        {
            "产品名称": "汇添富远景成长一年持有期混合型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富稳利60天滚动持有短债债券型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富成长领先混合型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富北证50成份指数型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富中证800指数增强型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富中证2000交易型开放式指数证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富稳益60天持有期债券型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富中证信息技术应用创新产业交易型开放式指数证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富添添乐双鑫债券型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富中短债债券型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        },
        {
            "产品名称": "汇添富稳福60天滚动持有中短债债券型证券投资基金",
            "产品类别": "单产品",
            "利率(年化)": {
                "all": "1.4%"
            },
            "开始时间": "2025-01-02",
            "截止时间": "",
            "计息天数": 360,
            "备注": "按月20日前结息至期货账户；利率随市场利率调整"
        }
    ]

    # 宁银费用变更
    json_data['ningyin_fee'] = [
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品7 号（ZGN2350007）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品8 号（ZGN2350008）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品9 号（ZGN2350009）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品10 号-B 份额（ZGN2350010B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品11 号-B 份额（ZGN2350011B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品12 号-B 份额（ZGN2350012B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.25%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品S 款3 号-B 份（ZGN235S003B）",
            "费率名称": "固定管理费",
            "原始费率": "0.40%（年化）",
            "优惠期间费率": "0.21%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣天天鎏金现金管理类理财产品S 款6 号-B 份（ZGN235S006B）",
            "费率名称": "固定管理费",
            "原始费率": "0.40%（年化）",
            "优惠期间费率": "0.03%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.33%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.16%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.16%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.30%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣固定收益类日开理财5 号（最短持有7 天）（ZGN2360005）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.30%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.40%（年化）",
            "优惠起始日期": "2025 年2 月13 日",
            "优惠截止日期*": "2025 年8 月13 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0",
            "优惠起始日期": "2025 年2 月13 日",
            "优惠截止日期*": "2025 年8 月13 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.03%（年化）",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月5 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "固定管理费",
            "原始费率": "0.50%（年化）",
            "优惠期间费率": "0.06%（年化）",
            "优惠起始日期": "2025 年2 月6 日",
            "优惠截止日期*": "2025 年2 月28 日"
        },
        {
            "产品名称/代码": "宁银理财宁欣日日薪固定收益类日开理财10 号（最短持有28 天）-B（ZGN2360010B）",
            "费率名称": "销售服务费",
            "原始费率": "0.30%（年化）",
            "优惠期间费率": "0%",
            "优惠起始日期": "2025 年2 月1 日",
            "优惠截止日期*": "2025 年2 月28 日"
        }
    ]

    # 开户场景
    json_data['account_opening'] = [
        {
            "资金账户信息": {
                "资金账号": "2120061",
                "交易编码对应名称": "金瑞期货股份有限公司-金瑞前海资本管理(深圳)有限公司-金瑞同进尊享1号FOF单一资产管理计划",
                "交易密码": "None",
                "统一交易编码": "********",
                "交易编码": {
                    "上期所": "********",
                    "大商所": "********",
                    "郑商所": "********",
                    "中金所（投机）": "********",
                    "中金所（套保）": "None",
                    "上能所": "********",
                    "广期所": "********"
                }
            },
            "期货公司保证金账户信息": {
                "保证金开户行": "交通银行上海分行营业部",
                "户名": "宁波银行股份有限公司金瑞同进尊享1号FOF单一资产管理计划",
                "账号": "31006666101880043387"
            },
            "银期转账信息": {
                "银期转账机构代码": "000087",
                "资金密码": "None"
            },
            "交易所会员号": {
                "郑州交易所": "0121",
                "大连交易所": "0195",
                "上海交易所": "0059",
                "金融交易所": "0170",
                "能源中心": "8059",
                "广州交易所": "0021"
            },
            "监控中心信息": {
                "监控中心登录名": "None",
                "监控中心密码": "None"
            }
        }
    ]

    return json_data

def create_test_records():
    """创建测试记录"""
    app = create_app()
    
    with app.app_context():
        # 获取管理员用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("错误: 未找到管理员用户，请先运行 init_database.py")
            return
        
        # 获取测试文件
        test_files = get_test_files()
        if not test_files:
            print("错误: 未找到任何测试文件")
            return
        
        # 获取JSON数据
        json_data = get_json_data()
        
        print("开始插入测试数据...")
        
        for analysis_type, file_path in test_files.items():
            try:
                print(f"\n处理类型: {analysis_type}")
                print(f"文件路径: {file_path}")
                
                # 获取文件信息
                file_info = get_file_info(file_path)
                if not file_info:
                    print(f"无法获取文件信息: {file_path}")
                    continue
                
                # 获取对应的JSON数据
                ai_result = json_data.get(analysis_type, {})
                expected_result = ai_result.copy()  # 预期结果与AI结果相同，确保100%匹配
                
                print(f"AI结果字段数: {len(str(ai_result))}")
                
                # 创建分析记录
                record = AnalysisRecord(
                    filename=os.path.basename(file_path),
                    analysis_type=analysis_type,
                    ai_result=json.dumps(ai_result, ensure_ascii=False),
                    expected_result=json.dumps(expected_result, ensure_ascii=False),
                    status='completed',
                    file_status='active',
                    review_status='pending',
                    accuracy_score=Decimal('1.0000'),  # 100%准确率
                    created_by=admin_user.id,
                    file_hash=calculate_file_hash(file_path),
                    file_info={
                        'original_filename': os.path.basename(file_path),
                        'file_size': file_info['file_size'],
                        'upload_path': file_path,
                        'file_type': file_info['file_type'],
                        'page_count': file_info.get('page_count', 1)
                    }
                )
                
                db.session.add(record)
                print(f"✓ 已创建记录: {record.filename}")
                
            except Exception as e:
                print(f"✗ 创建记录失败: {e}")
                continue
        
        # 提交所有更改
        try:
            db.session.commit()
            print(f"\n✅ 成功插入 {len(test_files)} 条测试数据")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ 提交数据失败: {e}")

if __name__ == '__main__':
    create_test_records()

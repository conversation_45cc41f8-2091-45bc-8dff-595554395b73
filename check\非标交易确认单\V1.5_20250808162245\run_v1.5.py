# V1.5版本run函数源代码
def run(fn):
    """V1.5版本主运行函数，PNG识别大幅优化"""
    usage_model = 'InternVL3-38B'

    print(f"=== V1.5版本处理开始 ===")
    print(f"文件: {fn}")

    # 判断文件类型
    file_ext = os.path.splitext(fn)[1].lower()
    print(f"文件类型: {file_ext}")

    # 对Excel文件特殊处理 - 继承V1.3版本优化
    if file_ext in ['.xlsx', '.xls']:
        try:
            # 根据文件扩展名选择合适的引擎
            if file_ext == '.xls':
                # .xls文件使用xlrd引擎
                df = pd.read_excel(fn, dtype=str, engine='xlrd')
            else:
                # .xlsx文件使用openpyxl引擎
                df = pd.read_excel(fn, dtype=str, engine='openpyxl')

            # 处理NaN值，但保持其他值的原始格式
            df = df.fillna('')  # 将NaN替换为空字符串

            # V1.5版本：使用优化的markdown生成
            markdown_content = "# Excel文件内容\n\n**V1.5版本重要提示：以下表格中的所有数值必须严格按照显示的格式进行提取，包括所有小数位数和负号，不得进行任何精度优化！**\n\n"

            # 使用自定义表格生成函数，避免pandas的数值格式化
            headers = df.columns.tolist()
            data = df.values.tolist()

            markdown_content += create_custom_markdown_table(data, headers)

            # V1.5版本：添加特殊提示
            markdown_content += "\n\n**【V1.5数据精度检查】**：请严格按照上表中显示的数值进行提取，特别注意小数位数！去除金融标识符（如￥、$等），保持中英文引号原样！千分位分隔符（逗号）不是小数点！\n"

        except Exception as e:
            print(f"Excel文件读取错误: {str(e)}")
            raise Exception(f"Excel文件读取错误: {str(e)}")

    # 对Word文件特殊处理 - 继承V1.3版本优化
    elif file_ext in ['.docx', '.doc']:
        try:
            import docx2txt
            # 提取文本内容
            text = docx2txt.process(fn)
            # V1.5版本：保持引号原样
            text = clean_quotes(text)
            markdown_content = f"# Word文档内容\n\n{text}"
        except ImportError:
            print("缺少docx2txt库，请安装: pip install docx2txt")
            raise Exception("缺少docx2txt库，请安装: pip install docx2txt")
        except Exception as e:
            print(f"Word文件读取错误: {str(e)}")
            raise Exception(f"Word文件读取错误: {str(e)}")

    # PDF和图片文件处理 - V1.5版本PNG超级优化
    else:
        if fn.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model=usage_model)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            # V1.5版本：PNG文件超级优化处理
            print(f"处理图片文件: {file_ext}")

            # 对于PNG文件，使用超级优化的处理函数
            if file_ext == '.png':
                print("启用PNG超级优化模式...")
                markdown_content, seal_img_list = process_png_file_super_optimized(fn, usage_model)

                # V1.5版本：增强OCR处理
                print("启用增强OCR处理...")
                markdown_content = enhanced_single_model_ocr(fn, markdown_content)
            else:
                # 其他图片格式使用原有参数
                markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
        else:
            # 其他格式的图片文件
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)

    # 如果是Excel或Word文件，跳过VLM OCR处理
    if file_ext not in ['.xlsx', '.xls', '.docx', '.doc']:
        # V1.5版本：PNG文件OCR优化
        print("开始补充OCR处理...")

        # V1.5版本：针对红利转投文档的超级优化OCR提示词
        if file_ext == '.png':
            ocr_prompt = """你是一名专业的OCR专家，特别擅长处理金融文档。请仔细分析这个PNG图片，这很可能是一个红利转投确认单。

【超级重要】请特别注意以下内容：

1. **投资者信息识别**：
   - 查找"长城证券"开头的投资者名称（如"长城证券乐活1号集合资产管理计划"）
   - 区分投资方和发行方，投资者是"长城证券XX计划"，不是"永诚保险"
   - 提取对应的账号信息

2. **业务类型识别**：
   - 查找"红利转投"、"分红转投"、"再投资"、"转投"、"份额结转"等关键词
   - 这是红利转投业务，不是普通分红

3. **投资标的信息**：
   - 产品名称通常是"永诚资产XX产品"
   - 产品代码通常是6位数字（如110001）

4. **数值信息**：
   - 完整识别所有金额和份额数据
   - 特别注意千分位分隔符（如1,213.03）
   - 红利转投中，金额和份额通常相等

5. **表格结构**：
   - 通常有多行交易记录
   - 每行包含：投资者名称、账号、金额、份额等信息
   - 按行逐一识别，不要遗漏

请按表格结构详细输出所有识别到的内容，特别是多行交易数据！"""
            max_tokens = 800  # 增加token数量以获得更详细的识别结果
        else:
            ocr_prompt = "你是一名OCR专家。请从用户提供的图片中提取有关印章信息、落款日期的全部有效信息"
            max_tokens = 100

        # VL2OCR
        ocr_bot = ChatBot(model='InternVL3-38B')
        img_list = process_file_to_base64(fn)
        vlm_info = ocr_bot.chat_with_img(ocr_prompt, img_url=img_list, max_tokens=max_tokens)
        markdown_content += f"\n# 补充信息：\n{vlm_info}"
        print("补充OCR处理完成")

    # 继承V1.3版本的系统提示词（保持完全一致）
    sys_prompt = """你是一位资深的银行托管部经理，负责从客户提供的非标交易确认单中**抽取交易要素**，并按以下要求结构化输出 JSON 数组。

**【V1.5版本关键提醒】**：在处理数值时，你必须像复制粘贴一样，完全按照原文档中显示的字符进行提取，不得进行任何数值处理！
**【千分位分隔符特别注意】**：当遇到包含逗号的数值时，必须正确识别千分位分隔符！例如：
- 2,123.57 → 应识别为 2123.57（不是 2.12）
- 1,234,567.89 → 应识别为 1234567.89（不是 1.23）
- 逗号在数值中作为千分位分隔符，不是小数点！

=====================【V1.5版本字段定义】=====================
1. 【投资者名称】：通常指代客户姓名，一般是资管计划的名称，在文档中可对应'投资人名称'、'客户名称'、'计划名称'等字段。**超级重要**：投资者是投资方（如"长城证券乐活1号集合资产管理计划"），绝对不是发行方（如"永诚保险资产管理有限公司"）！在红利转投文档中，投资者名称通常以"长城证券"开头。
2. 【投资者账号】通常指客户的资金账户，在文档中可对应'交易账号'、'客户账号'、'证件号码'、'基金账号'、'理财账户号'等字段，优先级顺序为：交易账号 > 资金账户 > 客户账号 > 证件号码 > 基金账号 > 理财账户号。投资者账号对应的值（value）应根据该优先级选取。不要取'合同编号'为本字段值。
3. 【业务日期】：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 【业务类型】：需要提取文件中代表当前类型的文字，并映射到下面的选项中：分红、红利转投、买入、卖出、认购、申购、赎回。**红利转投文档特别注意**：如果文档标题包含"分红确认单"但内容涉及"再投资"、"转投"、"份额结转"等，业务类型应该是"红利转投"而不是"分红"！
5. 【投资标的名称】：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 【投资标的代码】：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 【投资标的金额】：实际交易的确认金额（缺失填"/"）
8. 【投资标的数量】：文档中可能用份额来描述（缺失填"/"）
9. 【交易费用】：一般申购、赎回、买入、卖出交易中，会标明交易费用（缺失填"/"）

=====================【V1.5版本特殊处理规则】=====================

**【V1.5版本超级重要】金额识别规则：**
- 识别金额时，自动去除金融标识符（如￥、$、€、£等），只保留数字部分
- 例如：￥5010317.83 → 5010317.83
- **【关键】千分位分隔符处理**：必须正确识别逗号作为千分位分隔符，不能截断数值！
- 例如：￥2,123.57 → 2123.57（不是2.12）
- 例如：1,234,567.89 → 1234567.89（不是1.23）
- **【超级重要】完整数值识别**：47,047,164.46 → 47047164.46（绝对不能识别为470.00！）
- **【防止截断】**：如果看到千分位格式的数值，必须提取完整的数值，不能只提取前几位！

**数值格式要求：**
- 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
- 例如：1000000 → "1000000.00"，1000000.5 → "1000000.50"
- 如果字段不存在或为空，填写"/"

**引号处理：**
- 中文引号""保持原样，不要转换为英文引号
- 英文引号""保持原样，不要转换为中文引号

**特殊场景处理（分红）：**
- 【投资标的金额】：若文档中"红利"字段存在且非空，优先提取"红利"作为标的金额
- 【投资标的数量】：若文档中"红股"字段存在且非空，提取"红股"作为标的数量
- 【交易费用】：仅在申购、赎回、买入、卖出等交易类型中提取费用字段，分红等场景填写"/"

=====================【V1.5版本业务类型识别规则】=====================
**重要：必须严格按照以下规则识别业务类型，并映射到标准术语**

**标准业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回

**识别关键词映射表**：
• **分红** ← 关键词：分红、股息、利息分配、收益分配、现金分红、分红派息
• **红利转投** ← 关键词：红利转投、分红转投、红利再投资、分红再投资、红利转份额、分红转份额、再投资、转投、份额结转、红利结转、分红结转、收益再投
• **买入** ← 关键词：买入、购买、投资、成交买入
• **卖出** ← 关键词：卖出、出售、变现、成交卖出、兑付、到期兑付
• **认购** ← 关键词：认购、首次认购、新发认购、认购确认
• **申购** ← 关键词：申购、追加申购、申购确认、增购
• **赎回** ← 关键词：赎回、赎回确认、提取、退出、部分赎回、全部赎回

**V1.5版本识别步骤**：
1. 首先在文档中寻找明确的业务类型标识（如"业务类型"、"交易类型"、"操作类型"等字段）
2. 如果没有明确字段，则根据文档标题、内容描述中的关键词进行识别
3. 将识别到的原始词汇映射到上述7个标准业务类型之一
4. 如果无法确定，优先根据文档的主要内容和金额流向判断
5. **特别注意**：分红场景要优先查找"红利"和"红股"字段

=====================【数值提取增强规则】=====================
**针对投资标的金额、投资标的数量、交易费用字段的特殊处理**：

1. **多重查找策略**：
   - 优先查找明确标注的字段（如"确认金额"、"交易金额"、"份额"、"数量"等）
   - 查找表格中的数值列
   - 查找文本中的货币符号后的数值（¥、￥、RMB等）
   - 查找带有千分位分隔符的数值（如1,234,567.89）

2. **数值识别模式**：
   - 正数：123456.78、1,234,567.89、￥123,456.78
   - 负数：-123456.78、-1,234,567.89、-￥123,456.78
   - 零值：0、0.00、0.000等
   - **千分位分隔符处理**：2,123.57 应识别为 2123.57，不是 2.12

3. **容错处理**：
   - 如果某个数值字段在文档中确实存在但难以精确定位，不要填写"/"
   - 仔细查看表格的每一行每一列
   - 注意数值可能出现在不同的位置（表头、表尾、备注等）

4. **精度保持**：
   - 完全按照原文档显示的格式输出，包括所有小数位
   - 保留千分位分隔符（如果原文档有的话）
   - 保留负号和货币符号前缀

=====================【V1.5版本格式要求】=====================
• 日期全部转为 `YYYY-MM-DD` 格式
• **V1.5版本数值格式要求**：投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
• 去除金融标识符（￥、$等），只保留数字部分
• 保持中英文引号原样，不进行转换
• 业务类型必须是以下7个之一：分红、红利转投、买入、卖出、认购、申购、赎回
• 输出 **JSON 数组**，字段顺序固定，V1.5版本示例如下：

```json
[
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-15",
    "业务类型": "申购",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "100000.00",
    "投资标的数量": "10000.00",
    "交易费用": "150.00"
  },
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-16",
    "业务类型": "分红",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "5000.00",
    "投资标的数量": "/",
    "交易费用": "/"
  }
]
```

**【V1.5版本严格精度要求 - 必须遵守】**
1. 如果文档中确实没有相关信息，对应字段填写"/"
2. **V1.5版本数值格式要求（绝对不可违反）**：
   - 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
   - 例如：原文档显示"329961736.14"，输出"329961736.14"
   - 例如：原文档显示"100"，输出"100.00"
   - 例如：原文档显示"￥5010317.83"，输出"5010317.83"
   - **千分位分隔符处理**：原文档显示"2,123.57"，输出"2123.57"（不是"2.12"）
   - **千分位分隔符处理**：原文档显示"1,234,567.89"，输出"1234567.89"（不是"1.23"）
   - 去除金融标识符和千分位分隔符，但保留数值精度
3. **V1.5版本引号处理（绝对不可违反）**：
   - 中文引号""保持原样，不要转换为英文引号
   - 英文引号""保持原样，不要转换为中文引号
4. **V1.5版本特殊场景处理**：
   - 分红场景：优先查找"红利"字段作为投资标的金额
   - 分红场景：优先查找"红股"字段作为投资标的数量
   -分红场景：投资标的代码不要取"理财账户号"
   - 分红场景：交易费用字段填写"/"
   - 红利转投场景：优先查找"再投资红利金额"、"转投金额"、"结转金额"字段作为投资标的金额
   - 红利转投场景：优先查找"再投资份额"、"结转份额"、"转投份额"字段作为投资标的数量
   - 红利转投场景：投资者名称应该是投资方（如"长城证券XX计划"），绝对不是发行方（如"永诚保险"）
   - 红利转投场景：业务类型必须识别为"红利转投"，关键词包括：再投资、转投、份额结转等
   - 红利转投场景：通常有多笔交易，每笔都要单独提取
   - 红利转投场景：投资标的代码通常是6位数字（如110001），不要与账号混淆
   - 投资者账号：优先查找"交易账号"或"客户账号"字段


**【V1.5版本特别注意】**：
- 仔细检查文档中的每个表格、每行数据，特别是多行交易记录
- 数值字段绝对不能轻易填写"/"，除非确实在文档中找不到任何相关数值
- 业务类型必须严格按照映射规则转换为标准术语
- 所有数值必须格式化为两位小数的字符串格式
- **红利转投文档特别注意**：
  * 通常包含多笔交易记录，每笔都要单独提取
  * 投资者名称是投资方（如"长城证券XX计划"），不是发行方（如"永诚保险"）
  * 业务类型必须识别为"红利转投"
  * 投资标的金额和数量在红利转投中通常相等
  * 仔细区分投资标的代码和投资者账号，不要混淆
- **PNG图片特别关注**：由于PNG图片识别已经过多重优化，请特别仔细提取其中的数值信息
- **红利转投文档识别要点**：
  * 如果文档标题是"分红确认单"但内容涉及再投资，这是红利转投业务
  * 投资者名称格式："长城证券XX集合资产管理计划"
  * 投资标的名称格式："永诚资产XX产品"
  * 投资标的代码：通常是6位数字（如110001）
  * 金额和份额：在红利转投中通常相等
  * 多笔交易：每行数据都要单独提取
"""

    chat_bot = ChatBot(system_prompt=sys_prompt, model='InternVL3-38B')

    res = chat_bot.chat(markdown_content, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(res)

    # V1.5版本：后处理优化
    json_data = post_process_json_data(json_data, markdown_content)

    # V1.5版本：最终格式验证
    print("=== V1.5版本数值格式验证 ===")
    if isinstance(json_data, list):
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                print(f"交易 {i+1}:")
                for field in ['投资标的金额', '投资标的数量', '交易费用']:
                    if field in item:
                        value = item[field]
                        print(f"  {field}: {value} (类型: {type(value).__name__})")
                        if value == "/":
                            print(f"    ✓ 空值格式正确")
                        elif isinstance(value, str) and re.match(r'^-?\d+\.\d{2}$', value):
                            print(f"    ✓ 两位小数格式正确")
                        else:
                            print(f"    ⚠ 格式可能不符合V1.5要求")

    print("=== V1.5版本处理完成 ===")
    return json_data

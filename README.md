# 多场景智能化文档分析系统

这是一个基于 Web 的多场景智能化文档分析系统，支持多种业务场景的文档智能识别和分析。

## 功能特点

- 📁 支持多种文件格式（PDF, JPG, PNG, JPEG, GIF, BMP, TIFF）
- 🎯 多场景分析支持（期货账户、理财产品、券商计息、账户开户场景、宁银费用、非标交易）
- 🔍 智能识别文档关键信息
- 📊 结构化数据展示
- 💾 结果导出功能
- 🖱️ 拖拽上传支持
- 📱 响应式设计
- 🔄 批量文件处理
- 📈 准确率统计
- 🎨 现代化UI界面

## 支持的分析场景

### 1. 期货账户分析
- 产品名称、资金账号
- 交易编码（各大交易所）
- 交易所会员号
- 开始/结束时间

### 2. 理财产品分析
- 销售机构信息
- 产品基本信息
- 风险等级

### 3. 券商计息分析
- 计息变更信息
- 利率调整记录
- 生效日期

### 4. 账户开户场景
- 客户姓名、账户类型
- 账户号码、开户日期
- 开户网点、身份证号
- 业务类型

### 5. 宁银费用分析
- 费用类型、费率
- 生效日期
- 产品名称

### 6. 非标交易分析
- 交易确认信息
- 交易详情

## 安装说明

1. 安装 Python 依赖：

```bash
pip install -r requirements.txt
```

2. 启动应用：

```bash
python app.py
```

3. 打开浏览器访问：

```
http://localhost:5106
```

## 使用方法

1. 上传期货开户文件（支持拖拽或点击选择）
2. 点击"分析"按钮
3. 查看识别结果
4. 可选择导出结果为 JSON 格式

## 技术栈

- **后端**: Flask + Python
- **前端**: HTML5 + CSS3 + JavaScript
- **AI 模型**: OpenAI API (Qwen2.5VL)
- **图像处理**: PIL + PyMuPDF

## 项目结构

```
├── app.py              # Flask应用主文件
├── func.py             # 期货账户分析功能
├── util_img_process.py # 图像处理工具
├── util_ai.py          # AI聊天工具
├── mineru_pdf.py       # PDF处理工具
├── templates/
│   └── index.html      # 前端页面
├── uploads/            # 上传文件目录
├── processed/          # 处理后文件目录
└── requirements.txt    # Python依赖
```

## 注意事项

- 确保 AI 服务地址和 API 密钥配置正确
- 建议上传文件大小不超过 50MB
- 系统会自动将 PDF 转换为图片进行分析

## 更新日志

- v1.0.0: 初始版本，支持基本的期货账户分析功能
